class DeviceModel {
  final String id;
  final String name;
  final String manufacturerId;
  final String categoryId;
  final String? description;
  final String? imageUrl;
  final Map<String, dynamic>? specifications;
  final DateTime createdAt;
  final DateTime? updatedAt;

  DeviceModel({
    required this.id,
    required this.name,
    required this.manufacturerId,
    required this.categoryId,
    this.description,
    this.imageUrl,
    this.specifications,
    required this.createdAt,
    this.updatedAt,
  });

  factory DeviceModel.fromMap(String id, Map<String, dynamic> map) {
    return DeviceModel(
      id: id,
      name: map['name'] ?? '',
      manufacturerId: map['manufacturerId'] ?? '',
      categoryId: map['categoryId'] ?? '',
      description: map['description'],
      imageUrl: map['imageUrl'],
      specifications: map['specifications'],
      createdAt: map['createdAt']?.toDate() ?? DateTime.now(),
      updatedAt: map['updatedAt']?.toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'manufacturerId': manufacturerId,
      'categoryId': categoryId,
      'description': description,
      'imageUrl': imageUrl,
      'specifications': specifications,
      'createdAt': createdAt,
      'updatedAt': updatedAt ?? DateTime.now(),
    };
  }

  DeviceModel copyWith({
    String? id,
    String? name,
    String? manufacturerId,
    String? categoryId,
    String? description,
    String? imageUrl,
    Map<String, dynamic>? specifications,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DeviceModel(
      id: id ?? this.id,
      name: name ?? this.name,
      manufacturerId: manufacturerId ?? this.manufacturerId,
      categoryId: categoryId ?? this.categoryId,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      specifications: specifications ?? this.specifications,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DeviceModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'DeviceModel(id: $id, name: $name, manufacturerId: $manufacturerId)';
  }
}
