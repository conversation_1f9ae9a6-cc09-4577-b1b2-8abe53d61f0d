# دمج إعدادات التخزين المؤقت في شاشة الإعدادات الرئيسية

## نظرة عامة

تم نقل إعدادات التخزين المؤقت (Cache) من شاشة منفصلة إلى شاشة الإعدادات الرئيسية لتحسين تجربة المستخدم وتوحيد واجهة الإعدادات.

## التحديثات المنجزة

### 1. تحديث نموذج إعدادات المستخدم (UserSettings)

#### الحقول الجديدة المضافة:
```dart
// Cache settings
final bool enableCache;           // تفعيل/إلغاء التخزين المؤقت
final int maxCacheSizeMB;        // الحد الأقصى لحجم التخزين (بالميجابايت)
final bool autoCleanupCache;     // التنظيف التلقائي للملفات القديمة
final int cacheRetentionDays;    // مدة الاحتفاظ بالملفات (بالأيام)
final bool cacheImages;          // حفظ الصور في التخزين المؤقت
final bool cacheVideos;          // حفظ الفيديو في التخزين المؤقت
final bool cacheDocuments;       // حفظ المستندات في التخزين المؤقت
```

#### القيم الافتراضية:
- `enableCache`: `true`
- `maxCacheSizeMB`: `500` (500 ميجابايت)
- `autoCleanupCache`: `true`
- `cacheRetentionDays`: `30` (30 يوم)
- `cacheImages`: `true`
- `cacheVideos`: `true`
- `cacheDocuments`: `true`

### 2. تحديث مزود إعدادات المستخدم (UserSettingsProvider)

#### الدوال الجديدة المضافة:

```dart
// تحديث تفعيل التخزين المؤقت
Future<void> updateCacheEnabled(bool enabled)

// تحديث الحد الأقصى لحجم التخزين
Future<void> updateMaxCacheSize(int sizeMB)

// تحديث التنظيف التلقائي
Future<void> updateAutoCleanupCache(bool enabled)

// تحديث مدة الاحتفاظ بالملفات
Future<void> updateCacheRetentionDays(int days)

// تحديث أنواع الملفات المحفوظة
Future<void> updateCacheFileTypes({
  bool? images,
  bool? videos,
  bool? documents,
})
```

### 3. تحديث شاشة الإعدادات الرئيسية

#### القسم الجديد المضاف:
```dart
// Cache Settings
_buildSectionHeader(context, isRTL ? 'التخزين المؤقت' : 'Cache'),

_buildSettingCard(
  context,
  child: Column(
    children: [
      // تفعيل التخزين المؤقت
      SwitchListTile(
        title: Text(isRTL ? 'تفعيل التخزين المؤقت' : 'Enable Cache'),
        subtitle: Text(isRTL ? 'حفظ الملفات مؤقتاً لتسريع الوصول' : 'Save files temporarily for faster access'),
        value: settingsProvider.settings?.enableCache ?? true,
        onChanged: (value) async {
          await settingsProvider.updateCacheEnabled(value);
        },
      ),
      
      // التنظيف التلقائي
      SwitchListTile(
        title: Text(isRTL ? 'التنظيف التلقائي' : 'Auto Cleanup'),
        subtitle: Text(isRTL ? 'حذف الملفات القديمة تلقائياً' : 'Automatically delete old files'),
        value: settingsProvider.settings?.autoCleanupCache ?? true,
        onChanged: (value) async {
          await settingsProvider.updateAutoCleanupCache(value);
        },
      ),
      
      // رابط للإعدادات المتقدمة
      ListTile(
        title: Text(isRTL ? 'إعدادات متقدمة' : 'Advanced Settings'),
        subtitle: Text(isRTL ? 'إدارة تفصيلية للتخزين المؤقت' : 'Detailed cache management'),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: () => _showAdvancedCacheSettings(context, settingsProvider, isRTL),
      ),
    ],
  ),
),
```

### 4. تحسين شاشة إعدادات Cache المتقدمة

#### التحسينات المضافة:

##### أ. دعم اللغتين (العربية والإنجليزية):
- جميع النصوص تدعم الآن اللغتين
- واجهة متجاوبة مع اتجاه النص (RTL/LTR)

##### ب. إعدادات تفاعلية:
```dart
// شريط تمرير لحجم التخزين الأقصى
_buildSliderSetting(
  isRTL ? 'الحد الأقصى للتخزين' : 'Max Cache Size',
  '${settings?.maxCacheSizeMB ?? 500} MB',
  (settings?.maxCacheSizeMB ?? 500).toDouble(),
  100,    // الحد الأدنى: 100 MB
  2000,   // الحد الأقصى: 2000 MB (2 GB)
  (value) async {
    await settingsProvider.updateMaxCacheSize(value.round());
  },
  isRTL,
),

// شريط تمرير لمدة الاحتفاظ
_buildSliderSetting(
  isRTL ? 'مدة الاحتفاظ بالملفات' : 'File Retention Period',
  isRTL ? '${settings?.cacheRetentionDays ?? 30} يوم' : '${settings?.cacheRetentionDays ?? 30} days',
  (settings?.cacheRetentionDays ?? 30).toDouble(),
  7,      // الحد الأدنى: 7 أيام
  90,     // الحد الأقصى: 90 يوم
  (value) async {
    await settingsProvider.updateCacheRetentionDays(value.round());
  },
  isRTL,
),
```

##### ج. إعدادات أنواع الملفات:
```dart
// تحكم في أنواع الملفات المحفوظة
SwitchListTile(
  title: Text(isRTL ? 'الصور' : 'Images'),
  value: settings?.cacheImages ?? true,
  onChanged: (value) async {
    await settingsProvider.updateCacheFileTypes(images: value);
  },
),

SwitchListTile(
  title: Text(isRTL ? 'الفيديو' : 'Videos'),
  value: settings?.cacheVideos ?? true,
  onChanged: (value) async {
    await settingsProvider.updateCacheFileTypes(videos: value);
  },
),

SwitchListTile(
  title: Text(isRTL ? 'المستندات' : 'Documents'),
  value: settings?.cacheDocuments ?? true,
  onChanged: (value) async {
    await settingsProvider.updateCacheFileTypes(documents: value);
  },
),
```

## الفوائد المحققة

### 1. تحسين تجربة المستخدم
- **وصول سهل**: إعدادات Cache متاحة مباشرة من شاشة الإعدادات الرئيسية
- **تنظيم أفضل**: جميع الإعدادات في مكان واحد
- **تحكم مرن**: إعدادات أساسية في الشاشة الرئيسية، ومتقدمة في شاشة منفصلة

### 2. إدارة محسنة للذاكرة
- **حدود قابلة للتخصيص**: المستخدم يمكنه تحديد حجم التخزين المؤقت
- **تنظيف ذكي**: حذف تلقائي للملفات القديمة
- **تحكم دقيق**: اختيار أنواع الملفات المحفوظة

### 3. توفير مساحة التخزين
- **إعدادات مرنة**: تخصيص مدة الاحتفاظ بالملفات
- **تحكم انتقائي**: حفظ أنواع معينة من الملفات فقط
- **تنظيف سهل**: أدوات لمسح Cache عند الحاجة

### 4. أداء محسن
- **تحميل أسرع**: الملفات المحفوظة تحمل بسرعة
- **استخدام أقل للإنترنت**: تقليل إعادة تحميل الملفات
- **تجربة سلسة**: وصول فوري للمرفقات المحفوظة

## التكامل مع النظام

### 1. حفظ الإعدادات
- **Firestore**: حفظ سحابي للإعدادات
- **SharedPreferences**: حفظ محلي للوصول السريع
- **مزامنة تلقائية**: تحديث الإعدادات عبر الأجهزة

### 2. تطبيق الإعدادات
- **فوري**: تطبيق التغييرات مباشرة
- **ذكي**: احترام إعدادات المستخدم في جميع العمليات
- **آمن**: التحقق من صحة القيم قبل التطبيق

### 3. مراقبة الاستخدام
- **إحصائيات**: تتبع استخدام التخزين المؤقت
- **تنبيهات**: إشعارات عند امتلاء التخزين
- **تحليلات**: فهم أنماط استخدام المستخدمين

## الاستخدام

### للمستخدم العادي:
1. فتح شاشة الإعدادات
2. التمرير إلى قسم "التخزين المؤقت"
3. تفعيل/إلغاء التخزين المؤقت
4. تفعيل/إلغاء التنظيف التلقائي

### للمستخدم المتقدم:
1. النقر على "إعدادات متقدمة"
2. تخصيص حجم التخزين الأقصى
3. تحديد مدة الاحتفاظ بالملفات
4. اختيار أنواع الملفات المحفوظة
5. استخدام أدوات التنظيف اليدوي

## الخلاصة

هذا التحديث يوفر نظام إدارة شامل ومرن للتخزين المؤقت، مع واجهة سهلة الاستخدام وإعدادات قابلة للتخصيص. المستخدمون يمكنهم الآن التحكم الكامل في كيفية حفظ واستخدام الملفات المؤقتة، مما يحسن الأداء ويوفر مساحة التخزين حسب احتياجاتهم.
