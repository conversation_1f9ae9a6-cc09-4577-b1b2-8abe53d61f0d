# خطة تحسين التبعيات - تقليل حجم التطبيق

## التحليل الحالي للتبعيات

### Firebase Packages (~20MB)
- `firebase_core: ^3.13.1` - **مطلوب** (~3MB)
- `firebase_auth: ^5.5.4` - **مطلوب** (~4MB)
- `cloud_firestore: ^5.6.8` - **مطلوب** (~6MB)
- `firebase_storage: ^12.4.6` - **مطلوب** (~3MB)
- `firebase_messaging: ^15.1.6` - **مطلوب** (~2MB)
- `firebase_remote_config: ^5.1.6` - **مطلوب** (~2MB)

### Google APIs (~10MB)
- `googleapis: ^12.0.0` - **مطلوب** للـ Google Drive (~8MB)
- `googleapis_auth: ^1.4.1` - **مطلوب** للـ Google Drive (~2MB)

### UI/Image Packages (~8MB)
- `flutter_svg: ^2.0.10+1` - **مطلوب** للشعارات (~2MB)
- `cached_network_image: ^3.3.1` - **مطلوب** (~2MB)
- `image_picker: ^1.0.7` - **مطلوب** (~3MB)
- `flutter_image_compress: ^2.3.0` - **مطلوب** (~1MB)

### Packages يمكن إزالتها (~4-5MB توفير)
- `flutter_staggered_grid_view: ^0.7.0` - **يمكن إزالة** (~1MB)
  - البديل: استخدام `GridView` العادي
- `flutter_spinkit: ^5.2.0` - **يمكن إزالة** (~0.5MB)
  - البديل: `CircularProgressIndicator`
- `shimmer: ^3.0.0` - **يمكن إزالة** (~0.3MB)
  - البديل: تأثير shimmer مخصص
- `excel: ^4.0.6` - **يمكن إزالة إذا لم يُستخدم كثيراً** (~2MB)
  - البديل: CSV export بدلاً من Excel
- `encrypt: ^5.0.3` - **يمكن إزالة إذا لم يكن ضرورياً** (~1MB)
  - البديل: استخدام Firebase Security Rules

### Packages أخرى مطلوبة
- `provider: ^6.1.2` - **مطلوب** لإدارة الحالة
- `file_picker: ^8.1.2` - **مطلوب**
- `url_launcher: ^6.2.5` - **مطلوب**
- `share_plus: ^7.2.2` - **مطلوب**
- `path_provider: ^2.1.2` - **مطلوب**
- `geolocator: ^10.1.0` - **مطلوب**
- `permission_handler: ^11.3.1` - **مطلوب**

## خطة التحسين

### المرحلة الأولى: إزالة Packages غير الضرورية (توفير ~2MB)
1. إزالة `flutter_staggered_grid_view`
2. إزالة `flutter_spinkit`
3. إزالة `shimmer`

### المرحلة الثانية: تحسين الكود (توفير ~2-3MB إضافية)
1. استبدال `StaggeredGridView` بـ `GridView`
2. استبدال `SpinKitWidget` بـ `CircularProgressIndicator`
3. إنشاء تأثير shimmer مخصص بسيط

### المرحلة الثالثة: تحسين Firebase (توفير ~3-5MB)
1. تحسين ProGuard rules لإزالة Firebase features غير المستخدمة
2. استخدام Firebase BoM للإصدارات المتسقة
3. إزالة Firebase Analytics إذا لم يُستخدم

## التوفير المتوقع
- **إجمالي التوفير المتوقع**: 7-10MB
- **الحجم الحالي**: 51MB
- **الحجم المتوقع بعد التحسين**: 41-44MB

## خطوات التنفيذ
1. تشغيل script إزالة التبعيات
2. تحديث الكود لاستخدام البدائل
3. اختبار التطبيق
4. بناء APK محسن
5. قياس النتائج
