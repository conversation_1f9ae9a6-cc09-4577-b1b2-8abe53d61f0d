import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/locale_provider.dart';
import '../../models/user_model.dart';
import '../../services/user_profile_image_service.dart';
import '../../utils/theme.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart' hide AuthProvider;

class UserProfileScreen extends StatefulWidget {
  const UserProfileScreen({super.key});

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  bool _isLoading = false;
  bool _isChangingPassword = false;
  String? _profileImageUrl;
  Gender? _selectedGender;
  DateTime? _birthDate;

  @override
  void initState() {
    super.initState();
    _initializeUserData();
  }

  void _initializeUserData() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final user = authProvider.user;

    if (user != null) {
      _nameController.text = user.displayName ?? '';
      _phoneController.text = user.phoneNumber ?? '';
      _profileImageUrl = user.profileImageUrl;
      _selectedGender = user.gender;
      _birthDate = user.birthDate;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    if (authProvider.user == null) return;

    final scaffoldMessenger = ScaffoldMessenger.of(context);

    setState(() {
      _isLoading = true;
    });

    try {
      final String? imageUrl = await UserProfileImageService.updateProfileImageWithErrorHandling(
        context: context,
        userId: authProvider.user!.id,
        onSuccess: () {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(isRTL ? 'تم رفع الصورة بنجاح' : 'Image uploaded successfully'),
              backgroundColor: Colors.green,
            ),
          );
        },
        onError: (error) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(error),
              backgroundColor: Colors.red,
            ),
          );
        },
        onProgress: (progress) {
          // يمكن إضافة مؤشر تقدم هنا إذا أردت
        },
      );

      if (imageUrl != null) {
        setState(() {
          _profileImageUrl = imageUrl;
        });
      }
    } catch (e) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(e.toString()),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteProfileImage() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    if (authProvider.user == null) return;

    // عرض حوار التأكيد
    final bool confirmed = await UserProfileImageService.showDeleteConfirmationDialog(context);
    if (!confirmed || !mounted) return;

    final scaffoldMessenger = ScaffoldMessenger.of(context);

    setState(() {
      _isLoading = true;
    });

    try {
      await UserProfileImageService.deleteProfileImage(authProvider.user!.id);

      if (mounted) {
        setState(() {
          _profileImageUrl = null;
        });

        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(isRTL ? 'تم حذف الصورة بنجاح' : 'Image deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(e.toString()),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _selectBirthDate() async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    final date = await showDatePicker(
      context: context,
      initialDate: _birthDate ?? DateTime.now().subtract(const Duration(days: 365 * 25)),
      firstDate: DateTime(1950),
      lastDate: DateTime.now(),
      helpText: isRTL ? 'اختر تاريخ الميلاد' : 'Select birth date',
      cancelText: isRTL ? 'إلغاء' : 'Cancel',
      confirmText: isRTL ? 'موافق' : 'OK',
    );

    if (date != null) {
      setState(() {
        _birthDate = date;
      });
    }
  }

  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate()) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);

    if (authProvider.user == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final updatedUser = authProvider.user!.copyWith(
        displayName: _nameController.text.trim().isEmpty ? null : _nameController.text.trim(),
        phoneNumber: _phoneController.text.trim().isEmpty ? null : _phoneController.text.trim(),
        gender: _selectedGender,
        birthDate: _birthDate,
        profileImageUrl: _profileImageUrl,
      );

      await _firestore
          .collection('users')
          .doc(authProvider.user!.id)
          .update(updatedUser.toMap());

      // Update display name in Firebase Auth
      if (_nameController.text.trim().isNotEmpty) {
        await _auth.currentUser?.updateDisplayName(_nameController.text.trim());
      }

      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(isRTL ? 'تم تحديث الملف الشخصي بنجاح' : 'Profile updated successfully'),
          backgroundColor: Colors.green,
        ),
      );

      navigator.pop();
    } catch (e) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(isRTL ? 'خطأ في تحديث الملف الشخصي: $e' : 'Error updating profile: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _changePassword() async {
    if (_currentPasswordController.text.isEmpty ||
        _newPasswordController.text.isEmpty ||
        _confirmPasswordController.text.isEmpty) {
      return;
    }

    if (_newPasswordController.text != _confirmPasswordController.text) {
      return;
    }

    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);

    setState(() {
      _isChangingPassword = true;
    });

    try {
      final user = _auth.currentUser;
      if (user == null) return;

      // Re-authenticate user
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: _currentPasswordController.text,
      );

      await user.reauthenticateWithCredential(credential);

      // Update password
      await user.updatePassword(_newPasswordController.text);

      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(isRTL ? 'تم تغيير كلمة المرور بنجاح' : 'Password changed successfully'),
          backgroundColor: Colors.green,
        ),
      );

      navigator.pop();
    } catch (e) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(isRTL ? 'خطأ في تغيير كلمة المرور: $e' : 'Error changing password: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isChangingPassword = false;
      });
    }
  }

  void _showChangePasswordDialog() {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    AppTheme.showBlurredDialog(
      context: context,
      child: StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            title: Text(
              isRTL ? 'تغيير كلمة المرور' : 'Change Password',
              textAlign: TextAlign.center,
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: _currentPasswordController,
                  decoration: InputDecoration(
                    labelText: isRTL ? 'كلمة المرور الحالية' : 'Current Password',
                    prefixIcon: const Icon(Icons.lock_outline),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  obscureText: true,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: _newPasswordController,
                  decoration: InputDecoration(
                    labelText: isRTL ? 'كلمة المرور الجديدة' : 'New Password',
                    prefixIcon: const Icon(Icons.lock),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  obscureText: true,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: _confirmPasswordController,
                  decoration: InputDecoration(
                    labelText: isRTL ? 'تأكيد كلمة المرور' : 'Confirm Password',
                    prefixIcon: const Icon(Icons.lock),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    errorText: _newPasswordController.text.isNotEmpty &&
                            _confirmPasswordController.text.isNotEmpty &&
                            _newPasswordController.text != _confirmPasswordController.text
                        ? (isRTL ? 'كلمات المرور غير متطابقة' : 'Passwords do not match')
                        : null,
                  ),
                  obscureText: true,
                  onChanged: (value) {
                    setState(() {});
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  _currentPasswordController.clear();
                  _newPasswordController.clear();
                  _confirmPasswordController.clear();
                  Navigator.pop(context);
                },
                child: Text(isRTL ? 'إلغاء' : 'Cancel'),
              ),
              ElevatedButton(
                onPressed: _isChangingPassword ||
                        _currentPasswordController.text.isEmpty ||
                        _newPasswordController.text.isEmpty ||
                        _newPasswordController.text != _confirmPasswordController.text
                    ? null
                    : () async {
                        await _changePassword();
                        _currentPasswordController.clear();
                        _newPasswordController.clear();
                        _confirmPasswordController.clear();
                      },
                child: _isChangingPassword
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : Text(isRTL ? 'تغيير' : 'Change'),
              ),
            ],
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;
    final user = authProvider.user;

    if (user == null) {
      return Scaffold(
        appBar: AppBar(
          title: Text(isRTL ? 'الملف الشخصي' : 'Profile'),
        ),
        body: const Center(
          child: Text('User not found'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(isRTL ? 'الملف الشخصي' : 'Profile'),
        actions: [
          IconButton(
            icon: const Icon(Icons.lock),
            tooltip: isRTL ? 'تغيير كلمة المرور' : 'Change Password',
            onPressed: _showChangePasswordDialog,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    // Profile Image
                    Center(
                      child: Stack(
                        children: [
                          GestureDetector(
                            onTap: _pickImage,
                            child: Container(
                              width: 120,
                              height: 120,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Theme.of(context).colorScheme.primary.withAlpha(25),
                                border: Border.all(
                                  color: Theme.of(context).colorScheme.primary,
                                  width: 3,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Theme.of(context).colorScheme.primary.withAlpha(76),
                                    blurRadius: 10,
                                    spreadRadius: 2,
                                  ),
                                ],
                              ),
                              child: _profileImageUrl != null
                                  ? ClipOval(
                                      child: Image.network(
                                        _profileImageUrl!,
                                        fit: BoxFit.cover,
                                        width: 120,
                                        height: 120,
                                        errorBuilder: (context, error, stackTrace) {
                                          return Icon(
                                            Icons.error,
                                            size: 50,
                                            color: Colors.red,
                                          );
                                        },
                                        loadingBuilder: (context, child, loadingProgress) {
                                          if (loadingProgress == null) return child;
                                          return Center(
                                            child: CircularProgressIndicator(
                                              value: loadingProgress.expectedTotalBytes != null
                                                  ? loadingProgress.cumulativeBytesLoaded /
                                                      loadingProgress.expectedTotalBytes!
                                                  : null,
                                            ),
                                          );
                                        },
                                      ),
                                    )
                                  : Icon(
                                      Icons.add_a_photo,
                                      size: 50,
                                      color: Theme.of(context).colorScheme.primary,
                                    ),
                            ),
                          ),
                          // Delete button (only show if image exists)
                          if (_profileImageUrl != null)
                            Positioned(
                              top: 0,
                              right: 0,
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colors.red,
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withAlpha(76),
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: IconButton(
                                  icon: const Icon(
                                    Icons.delete,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                  onPressed: _deleteProfileImage,
                                  padding: const EdgeInsets.all(8),
                                  constraints: const BoxConstraints(
                                    minWidth: 36,
                                    minHeight: 36,
                                  ),
                                  tooltip: isRTL ? 'حذف الصورة' : 'Delete Image',
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _profileImageUrl != null
                          ? (isRTL ? 'اضغط لتغيير الصورة' : 'Tap to change image')
                          : (isRTL ? 'اضغط لإضافة صورة' : 'Tap to add image'),
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 32),

                    // Email (read-only)
                    TextFormField(
                      initialValue: user.email,
                      decoration: InputDecoration(
                        labelText: isRTL ? 'البريد الإلكتروني' : 'Email',
                        prefixIcon: const Icon(Icons.email),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      enabled: false,
                    ),
                    const SizedBox(height: 16),

                    // Display Name
                    TextFormField(
                      controller: _nameController,
                      decoration: InputDecoration(
                        labelText: isRTL ? 'الاسم' : 'Display Name',
                        prefixIcon: const Icon(Icons.person),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return isRTL ? 'الرجاء إدخال الاسم' : 'Please enter your name';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Phone Number
                    TextFormField(
                      controller: _phoneController,
                      decoration: InputDecoration(
                        labelText: isRTL ? 'رقم الهاتف' : 'Phone Number',
                        prefixIcon: const Icon(Icons.phone),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      keyboardType: TextInputType.phone,
                    ),
                    const SizedBox(height: 16),

                    // Gender
                    DropdownButtonFormField<Gender>(
                      value: _selectedGender,
                      decoration: InputDecoration(
                        labelText: isRTL ? 'الجنس' : 'Gender',
                        prefixIcon: const Icon(Icons.person_outline),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      items: Gender.values.map((gender) {
                        String label;
                        switch (gender) {
                          case Gender.male:
                            label = isRTL ? 'ذكر' : 'Male';
                            break;
                          case Gender.female:
                            label = isRTL ? 'أنثى' : 'Female';
                            break;
                          case Gender.other:
                            label = isRTL ? 'آخر' : 'Other';
                            break;
                        }
                        return DropdownMenuItem(
                          value: gender,
                          child: Text(label),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedGender = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),

                    // Birth Date
                    InkWell(
                      onTap: _selectBirthDate,
                      child: InputDecorator(
                        decoration: InputDecoration(
                          labelText: isRTL ? 'تاريخ الميلاد' : 'Birth Date',
                          prefixIcon: const Icon(Icons.calendar_today),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          _birthDate != null
                              ? '${_birthDate!.year}/${_birthDate!.month}/${_birthDate!.day}'
                              : (isRTL ? 'اختر تاريخ الميلاد' : 'Select birth date'),
                          style: TextStyle(
                            color: _birthDate != null ? null : Colors.grey[600],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 32),

                    // Save Button
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _updateProfile,
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: _isLoading
                            ? const CircularProgressIndicator(color: Colors.white)
                            : Text(
                                isRTL ? 'حفظ التغييرات' : 'Save Changes',
                                style: const TextStyle(fontSize: 16),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}