import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:geolocator/geolocator.dart';

/// Service for managing app permissions
class PermissionsService {
  static final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();

  /// Request all necessary permissions for the app
  static Future<Map<String, bool>> requestAllPermissions() async {
    final results = <String, bool>{};

    try {
      // Camera permission
      results['camera'] = await _requestCameraPermission();

      // Storage permissions
      results['storage'] = await _requestStoragePermissions();

      // Location permissions
      results['location'] = await _requestLocationPermissions();

      // Microphone permission
      results['microphone'] = await _requestMicrophonePermission();

      // Notification permission
      results['notification'] = await _requestNotificationPermission();

      // Phone state permission (for device ID)
      results['phone'] = await _requestPhonePermission();

      debugPrint('Permissions results: $results');
      return results;
    } catch (e) {
      debugPrint('Error requesting permissions: $e');
      return results;
    }
  }

  /// Request camera permission
  static Future<bool> _requestCameraPermission() async {
    try {
      final status = await Permission.camera.request();
      return status.isGranted;
    } catch (e) {
      debugPrint('Error requesting camera permission: $e');
      return false;
    }
  }

  /// Request storage permissions
  static Future<bool> _requestStoragePermissions() async {
    try {
      if (defaultTargetPlatform == TargetPlatform.android) {
        final androidInfo = await _deviceInfo.androidInfo;

        if (androidInfo.version.sdkInt >= 33) {
          // Android 13+ uses scoped storage
          final photos = await Permission.photos.request();
          final videos = await Permission.videos.request();
          final audio = await Permission.audio.request();

          return photos.isGranted && videos.isGranted && audio.isGranted;
        } else if (androidInfo.version.sdkInt >= 30) {
          // Android 11-12 uses manage external storage
          final manageStorage = await Permission.manageExternalStorage.request();
          return manageStorage.isGranted;
        } else {
          // Android 10 and below
          final storage = await Permission.storage.request();
          return storage.isGranted;
        }
      } else if (defaultTargetPlatform == TargetPlatform.iOS) {
        final photos = await Permission.photos.request();
        return photos.isGranted;
      }

      return false;
    } catch (e) {
      debugPrint('Error requesting storage permissions: $e');
      return false;
    }
  }

  /// Request location permissions
  static Future<bool> _requestLocationPermissions() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('Location services are disabled');
        return false;
      }

      // Request location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          debugPrint('Location permissions are denied');
          return false;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        debugPrint('Location permissions are permanently denied');
        return false;
      }

      return permission == LocationPermission.whileInUse ||
             permission == LocationPermission.always;
    } catch (e) {
      debugPrint('Error requesting location permissions: $e');
      return false;
    }
  }

  /// Request microphone permission
  static Future<bool> _requestMicrophonePermission() async {
    try {
      final status = await Permission.microphone.request();
      return status.isGranted;
    } catch (e) {
      debugPrint('Error requesting microphone permission: $e');
      return false;
    }
  }

  /// Request notification permission
  static Future<bool> _requestNotificationPermission() async {
    try {
      final status = await Permission.notification.request();
      return status.isGranted;
    } catch (e) {
      debugPrint('Error requesting notification permission: $e');
      return false;
    }
  }

  /// Request phone state permission (for device identification)
  static Future<bool> _requestPhonePermission() async {
    try {
      if (defaultTargetPlatform == TargetPlatform.android) {
        final status = await Permission.phone.request();
        return status.isGranted;
      }
      return true; // iOS doesn't need this permission
    } catch (e) {
      debugPrint('Error requesting phone permission: $e');
      return false;
    }
  }

  /// Check if all critical permissions are granted
  static Future<bool> areAllCriticalPermissionsGranted() async {
    try {
      final camera = await Permission.camera.isGranted;
      final storage = await _checkStoragePermissions();
      final location = await _checkLocationPermissions();

      return camera && storage && location;
    } catch (e) {
      debugPrint('Error checking permissions: $e');
      return false;
    }
  }

  /// Check storage permissions status
  static Future<bool> _checkStoragePermissions() async {
    try {
      if (defaultTargetPlatform == TargetPlatform.android) {
        final androidInfo = await _deviceInfo.androidInfo;

        if (androidInfo.version.sdkInt >= 33) {
          final photos = await Permission.photos.isGranted;
          final videos = await Permission.videos.isGranted;
          return photos && videos;
        } else if (androidInfo.version.sdkInt >= 30) {
          return await Permission.manageExternalStorage.isGranted;
        } else {
          return await Permission.storage.isGranted;
        }
      } else if (defaultTargetPlatform == TargetPlatform.iOS) {
        return await Permission.photos.isGranted;
      }

      return false;
    } catch (e) {
      debugPrint('Error checking storage permissions: $e');
      return false;
    }
  }

  /// Check location permissions status
  static Future<bool> _checkLocationPermissions() async {
    try {
      final permission = await Geolocator.checkPermission();
      return permission == LocationPermission.whileInUse ||
             permission == LocationPermission.always;
    } catch (e) {
      debugPrint('Error checking location permissions: $e');
      return false;
    }
  }

  /// Get device information (MAC address, device ID, etc.)
  static Future<Map<String, String?>> getDeviceInfo() async {
    final deviceInfo = <String, String?>{};

    try {
      if (defaultTargetPlatform == TargetPlatform.android) {
        final androidInfo = await _deviceInfo.androidInfo;
        deviceInfo['deviceId'] = androidInfo.id;
        deviceInfo['model'] = androidInfo.model;
        deviceInfo['manufacturer'] = androidInfo.manufacturer;
        deviceInfo['brand'] = androidInfo.brand;
        deviceInfo['device'] = androidInfo.device;
        deviceInfo['hardware'] = androidInfo.hardware;
        deviceInfo['product'] = androidInfo.product;
        deviceInfo['fingerprint'] = androidInfo.fingerprint;
        deviceInfo['host'] = androidInfo.host;
        deviceInfo['tags'] = androidInfo.tags;
      } else if (defaultTargetPlatform == TargetPlatform.iOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        deviceInfo['deviceId'] = iosInfo.identifierForVendor;
        deviceInfo['model'] = iosInfo.model;
        deviceInfo['name'] = iosInfo.name;
        deviceInfo['systemName'] = iosInfo.systemName;
        deviceInfo['systemVersion'] = iosInfo.systemVersion;
        deviceInfo['localizedModel'] = iosInfo.localizedModel;
      } else if (defaultTargetPlatform == TargetPlatform.windows) {
        final windowsInfo = await _deviceInfo.windowsInfo;
        deviceInfo['deviceId'] = windowsInfo.deviceId;
        deviceInfo['computerName'] = windowsInfo.computerName;
        deviceInfo['userName'] = windowsInfo.userName;
        deviceInfo['majorVersion'] = windowsInfo.majorVersion.toString();
        deviceInfo['minorVersion'] = windowsInfo.minorVersion.toString();
      }

      return deviceInfo;
    } catch (e) {
      debugPrint('Error getting device info: $e');
      return deviceInfo;
    }
  }

  /// Open app settings for manual permission management
  static Future<bool> openAppSettings() async {
    try {
      return await Permission.storage.request().then((status) => status.isGranted);
    } catch (e) {
      debugPrint('Error opening app settings: $e');
      return false;
    }
  }

  /// Show permission rationale dialog
  static Future<bool> showPermissionRationale(
    String permissionName,
    String reason,
  ) async {
    // This would typically show a dialog explaining why the permission is needed
    // Implementation depends on the UI framework being used
    debugPrint('Permission rationale for $permissionName: $reason');
    return true;
  }
}
