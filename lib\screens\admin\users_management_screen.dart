import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:firebase_auth/firebase_auth.dart' hide AuthProvider;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:image_picker/image_picker.dart';
import '../../models/user_model.dart';
import '../../models/permission_model.dart' as perm;
import '../../providers/auth_provider.dart';
import '../../providers/locale_provider.dart';

import '../../services/storage_service.dart';
import 'user_role_assignment_screen.dart';
import '../../utils/theme.dart';


class UsersManagementScreen extends StatefulWidget {
  const UsersManagementScreen({super.key});

  @override
  State<UsersManagementScreen> createState() => _UsersManagementScreenState();
}

class _UsersManagementScreenState extends State<UsersManagementScreen> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final StorageService _storageService = StorageService();

  List<UserModel> _users = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final users = await authProvider.getAllUsers();
      if (mounted) {
        setState(() {
          _users = users;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المستخدمين: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  List<UserModel> get _filteredUsers {
    if (_searchQuery.isEmpty) return _users;
    return _users.where((user) {
      return user.displayName?.toLowerCase().contains(_searchQuery.toLowerCase()) == true ||
             user.email.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             user.phoneNumber?.contains(_searchQuery) == true;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isRTL ? 'إدارة المستخدمين' : 'Users Management',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddUserDialog(),
            tooltip: isRTL ? 'إضافة مستخدم' : 'Add User',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadUsers,
            tooltip: isRTL ? 'تحديث' : 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              decoration: InputDecoration(
                hintText: isRTL ? 'البحث عن مستخدم...' : 'Search users...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surface,
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          // Users list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredUsers.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.people_outline,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _searchQuery.isEmpty
                                  ? (isRTL ? 'لا يوجد مستخدمين' : 'No users found')
                                  : (isRTL ? 'لا توجد نتائج للبحث' : 'No search results'),
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _filteredUsers.length,
                        itemBuilder: (context, index) {
                          final user = _filteredUsers[index];
                          return _buildUserCard(user, isRTL);
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserCard(UserModel user, bool isRTL) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isDark
              ? Theme.of(context).colorScheme.outline.withAlpha(120)
              : const Color(0xFF2563EB).withAlpha(100), // حدود زرقاء فاتحة
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
              ? Colors.black.withAlpha(120)
              : const Color(0xFF2563EB).withAlpha(40), // ظل أزرق فاتح
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: isDark
              ? Colors.black.withAlpha(60)
              : const Color(0xFF2563EB).withAlpha(20), // ظل ثانوي
            blurRadius: 6,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Card(
        elevation: 0,
        margin: EdgeInsets.zero,
        color: isDark
            ? Theme.of(context).colorScheme.surface
            : const Color(0xFFF8FAFF), // خلفية زرقاء فاتحة جداً
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isDark ? [
                Theme.of(context).colorScheme.surface,
                Theme.of(context).colorScheme.surface.withAlpha(230),
              ] : [
                const Color(0xFFF8FAFF), // زرقاء فاتحة جداً
                const Color(0xFFF1F5FF), // زرقاء فاتحة أكثر قليلاً
                const Color(0xFFEBF2FF), // زرقاء فاتحة أكثر
              ],
              stops: isDark ? null : [0.0, 0.5, 1.0],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // Profile image
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context).colorScheme.primary.withAlpha(76),
                            blurRadius: 8,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: CircleAvatar(
                        radius: 30,
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        backgroundImage: user.profileImageUrl != null
                            ? NetworkImage(user.profileImageUrl!)
                            : null,
                        child: user.profileImageUrl == null
                            ? Text(
                                user.displayName?.substring(0, 1).toUpperCase() ?? 'U',
                                style: const TextStyle(
                                  fontSize: 24,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              )
                            : null,
                      ),
                    ),
                    const SizedBox(width: 16),

                    // User info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  user.displayName ?? (isRTL ? 'مستخدم' : 'User'),
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              // Status indicators
                              if (user.isExpired)
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: Colors.red.withAlpha(25),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(color: Colors.red),
                                  ),
                                  child: Text(
                                    isRTL ? 'منتهي الصلاحية' : 'Expired',
                                    style: const TextStyle(
                                      color: Colors.red,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: user.role == UserRole.admin
                                      ? Colors.red.withAlpha(25)
                                      : Colors.blue.withAlpha(25),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: user.role == UserRole.admin ? Colors.red : Colors.blue,
                                  ),
                                ),
                                child: Text(
                                  isRTL ? user.effectiveRoleNameAr : user.effectiveRoleName,
                                  style: TextStyle(
                                    color: user.role == UserRole.admin ? Colors.red : Colors.blue,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            user.email,
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onSurface.withAlpha(178),
                              fontSize: 14,
                            ),
                          ),
                          if (user.phoneNumber != null) ...[
                            const SizedBox(height: 2),
                            Text(
                              user.phoneNumber!,
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.onSurface.withAlpha(178),
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),

                // Additional info
                if (user.gender != null || user.age != null) ...[
                  const SizedBox(height: 12),
                  const Divider(),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      if (user.gender != null) ...[
                        Icon(
                          user.gender == Gender.male ? Icons.male : Icons.female,
                          size: 16,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          user.genderDisplayName,
                          style: TextStyle(
                            fontSize: 14,
                            color: Theme.of(context).colorScheme.onSurface.withAlpha(204),
                          ),
                        ),
                      ],
                      if (user.gender != null && user.age != null)
                        const SizedBox(width: 16),
                      if (user.age != null) ...[
                        const Icon(
                          Icons.cake,
                          size: 16,
                          color: Colors.orange,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${user.age} ${isRTL ? 'سنة' : 'years'}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Theme.of(context).colorScheme.onSurface.withAlpha(204),
                          ),
                        ),
                      ],
                    ],
                  ),
                ],

                const SizedBox(height: 12),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // Active/Inactive toggle
                    IconButton(
                      icon: Icon(
                        user.isActive ? Icons.toggle_on : Icons.toggle_off,
                        color: user.isActive ? Colors.green : Colors.grey,
                        size: 32,
                      ),
                      onPressed: user.isMainAdmin ? null : () => _toggleUserStatus(user),
                      tooltip: user.isActive
                          ? (isRTL ? 'إلغاء تفعيل' : 'Deactivate')
                          : (isRTL ? 'تفعيل' : 'Activate'),
                    ),

                    // Edit button
                    IconButton(
                      icon: const Icon(Icons.edit, color: Colors.blue),
                      onPressed: () => _showEditUserDialog(user),
                      tooltip: isRTL ? 'تعديل' : 'Edit',
                    ),

                    // Reset password button
                    IconButton(
                      icon: const Icon(Icons.lock_reset, color: Colors.orange),
                      onPressed: () => _showResetPasswordDialog(user),
                      tooltip: isRTL ? 'إعادة تعيين كلمة المرور' : 'Reset Password',
                    ),

                    // Permissions button
                    IconButton(
                      icon: const Icon(Icons.admin_panel_settings, color: Colors.purple),
                      onPressed: () => _showUserPermissionsDialog(user),
                      tooltip: isRTL ? 'عرض الصلاحيات' : 'View Permissions',
                    ),

                    // Role assignment button
                    IconButton(
                      icon: const Icon(Icons.person_add, color: Colors.indigo),
                      onPressed: () => _navigateToRoleAssignment(user),
                      tooltip: isRTL ? 'تعيين الأدوار' : 'Assign Roles',
                    ),

                    // Delete button (only for non-main admin)
                    if (!user.isMainAdmin)
                      IconButton(
                        icon: const Icon(Icons.delete, color: Colors.red),
                        onPressed: () => _showDeleteUserDialog(user),
                        tooltip: isRTL ? 'حذف' : 'Delete',
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showAddUserDialog() {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    final emailController = TextEditingController();
    final nameController = TextEditingController();
    final phoneController = TextEditingController();
    final passwordController = TextEditingController();

    bool isAdmin = false;
    bool isActive = true;
    Gender? selectedGender;
    DateTime? birthDate;
    DateTime? expiryDate;
    String? profileImageUrl;

    AppTheme.showBlurredDialog(
      context: context,
      child: StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            title: Text(
              isRTL ? 'إضافة مستخدم جديد' : 'Add New User',
              textAlign: TextAlign.center,
            ),
            content: SingleChildScrollView(
              child: SizedBox(
                width: MediaQuery.of(context).size.width * 0.8,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Profile image picker
                    GestureDetector(
                      onTap: () async {
                        final scaffoldMessenger = ScaffoldMessenger.of(context);
                        final picker = ImagePicker();
                        final image = await picker.pickImage(source: ImageSource.gallery);
                        if (image != null) {
                          try {
                            final url = await _storageService.uploadUserProfileImage(
                              File(image.path),
                              'temp_${DateTime.now().millisecondsSinceEpoch}',
                            );
                            setState(() {
                              profileImageUrl = url;
                            });
                          } catch (e) {
                            scaffoldMessenger.showSnackBar(
                              SnackBar(
                                content: Text('خطأ في رفع الصورة: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        }
                      },
                      child: Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Theme.of(context).colorScheme.primary.withAlpha(25),
                          border: Border.all(
                            color: Theme.of(context).colorScheme.primary,
                            width: 2,
                          ),
                        ),
                        child: profileImageUrl != null
                            ? ClipOval(
                                child: Image.network(
                                  profileImageUrl!,
                                  fit: BoxFit.cover,
                                ),
                              )
                            : Icon(
                                Icons.add_a_photo,
                                size: 40,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Email field
                    TextField(
                      controller: emailController,
                      decoration: InputDecoration(
                        labelText: isRTL ? 'البريد الإلكتروني' : 'Email',
                        prefixIcon: const Icon(Icons.email),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      keyboardType: TextInputType.emailAddress,
                    ),
                    const SizedBox(height: 16),

                    // Name field
                    TextField(
                      controller: nameController,
                      decoration: InputDecoration(
                        labelText: isRTL ? 'اسم المستخدم' : 'Display Name',
                        prefixIcon: const Icon(Icons.person),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Phone field
                    TextField(
                      controller: phoneController,
                      decoration: InputDecoration(
                        labelText: isRTL ? 'رقم الهاتف' : 'Phone Number',
                        prefixIcon: const Icon(Icons.phone),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      keyboardType: TextInputType.phone,
                    ),
                    const SizedBox(height: 16),

                    // Password field
                    TextField(
                      controller: passwordController,
                      decoration: InputDecoration(
                        labelText: isRTL ? 'كلمة المرور' : 'Password',
                        prefixIcon: const Icon(Icons.lock),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      obscureText: true,
                    ),
                    const SizedBox(height: 16),

                    // Gender dropdown
                    DropdownButtonFormField<Gender>(
                      value: selectedGender,
                      decoration: InputDecoration(
                        labelText: isRTL ? 'النوع' : 'Gender',
                        prefixIcon: const Icon(Icons.person_outline),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      items: Gender.values.map((gender) {
                        String label;
                        switch (gender) {
                          case Gender.male:
                            label = isRTL ? 'ذكر' : 'Male';
                            break;
                          case Gender.female:
                            label = isRTL ? 'أنثى' : 'Female';
                            break;
                          case Gender.other:
                            label = isRTL ? 'آخر' : 'Other';
                            break;
                        }
                        return DropdownMenuItem(
                          value: gender,
                          child: Text(label),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          selectedGender = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),

                    // Birth date picker
                    ListTile(
                      leading: const Icon(Icons.cake),
                      title: Text(
                        birthDate != null
                            ? '${birthDate!.day}/${birthDate!.month}/${birthDate!.year}'
                            : (isRTL ? 'تاريخ الميلاد' : 'Birth Date'),
                      ),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: DateTime.now().subtract(const Duration(days: 365 * 25)),
                          firstDate: DateTime(1950),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          setState(() {
                            birthDate = date;
                          });
                        }
                      },
                    ),

                    // Expiry date picker
                    ListTile(
                      leading: const Icon(Icons.event_busy),
                      title: Text(
                        expiryDate != null
                            ? '${expiryDate!.day}/${expiryDate!.month}/${expiryDate!.year}'
                            : (isRTL ? 'تاريخ الانتهاء' : 'Expiry Date'),
                      ),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: DateTime.now().add(const Duration(days: 365)),
                          firstDate: DateTime(2020),
                          lastDate: DateTime(2030),
                        );
                        if (date != null) {
                          setState(() {
                            expiryDate = date;
                          });
                        }
                      },
                    ),

                    // Admin switch
                    SwitchListTile(
                      title: Text(isRTL ? 'مدير' : 'Admin'),
                      value: isAdmin,
                      onChanged: (value) {
                        setState(() {
                          isAdmin = value;
                        });
                      },
                    ),

                    // Active switch
                    SwitchListTile(
                      title: Text(isRTL ? 'نشط' : 'Active'),
                      value: isActive,
                      onChanged: (value) {
                        setState(() {
                          isActive = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(isRTL ? 'إلغاء' : 'Cancel'),
              ),
              ElevatedButton(
                onPressed: () async {
                  if (emailController.text.trim().isEmpty ||
                      passwordController.text.trim().isEmpty) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          isRTL
                              ? 'يرجى ملء البريد الإلكتروني وكلمة المرور'
                              : 'Please fill email and password',
                        ),
                        backgroundColor: Colors.red,
                      ),
                    );
                    return;
                  }

                  final navigator = Navigator.of(context);
                  await _addUser(
                    email: emailController.text.trim(),
                    password: passwordController.text.trim(),
                    displayName: nameController.text.trim().isEmpty
                        ? null
                        : nameController.text.trim(),
                    phoneNumber: phoneController.text.trim().isEmpty
                        ? null
                        : phoneController.text.trim(),
                    isAdmin: isAdmin,
                    isActive: isActive,
                    gender: selectedGender,
                    birthDate: birthDate,
                    expiryDate: expiryDate,
                    profileImageUrl: profileImageUrl,
                  );
                  navigator.pop();
                },
                child: Text(isRTL ? 'إنشاء' : 'Create'),
              ),
            ],
          );
        },
      ),
    );
  }

  Future<void> _addUser({
    required String email,
    required String password,
    String? displayName,
    String? phoneNumber,
    required bool isAdmin,
    required bool isActive,
    Gender? gender,
    DateTime? birthDate,
    DateTime? expiryDate,
    String? profileImageUrl,
  }) async {
    try {
      // Check if email already exists
      final existingUsers = await _firestore
          .collection('users')
          .where('email', isEqualTo: email)
          .get();

      if (existingUsers.docs.isNotEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('البريد الإلكتروني مستخدم بالفعل'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // Create user in Firebase Auth
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Update display name in Firebase Auth
      if (displayName != null) {
        await userCredential.user!.updateDisplayName(displayName);
      }

      // Create user document in Firestore
      final userModel = UserModel(
        id: userCredential.user!.uid,
        email: email,
        displayName: displayName,
        phoneNumber: phoneNumber,
        role: isAdmin ? UserRole.admin : UserRole.user,
        createdAt: DateTime.now(),
        isActive: isActive,
        gender: gender,
        birthDate: birthDate,
        expiryDate: expiryDate,
        profileImageUrl: profileImageUrl,
      );

      await _firestore
          .collection('users')
          .doc(userCredential.user!.uid)
          .set(userModel.toMap());

      // Sign out the newly created user to avoid auto-login
      await _auth.signOut();

      // Re-authenticate the current admin user
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        // If no current user, redirect to login
        if (mounted) {
          Navigator.of(context).pushReplacementNamed('/login');
        }
        return;
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء المستخدم بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }

      _loadUsers();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء المستخدم: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showEditUserDialog(UserModel user) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    final nameController = TextEditingController(text: user.displayName);
    final phoneController = TextEditingController(text: user.phoneNumber);

    bool isAdmin = user.role == UserRole.admin;
    bool isActive = user.isActive;
    Gender? selectedGender = user.gender;
    DateTime? birthDate = user.birthDate;
    DateTime? expiryDate = user.expiryDate;
    String? profileImageUrl = user.profileImageUrl;

    AppTheme.showBlurredDialog(
      context: context,
      child: StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            title: Text(
              isRTL ? 'تعديل المستخدم' : 'Edit User',
              textAlign: TextAlign.center,
            ),
            content: SingleChildScrollView(
              child: SizedBox(
                width: MediaQuery.of(context).size.width * 0.8,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Profile image
                    GestureDetector(
                      onTap: () async {
                        final scaffoldMessenger = ScaffoldMessenger.of(context);
                        final picker = ImagePicker();
                        final image = await picker.pickImage(source: ImageSource.gallery);
                        if (image != null) {
                          try {
                            final url = await _storageService.uploadUserProfileImage(
                              File(image.path),
                              user.id,
                            );
                            setState(() {
                              profileImageUrl = url;
                            });
                          } catch (e) {
                            scaffoldMessenger.showSnackBar(
                              SnackBar(
                                content: Text('خطأ في رفع الصورة: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        }
                      },
                      child: Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Theme.of(context).colorScheme.primary.withAlpha(25),
                          border: Border.all(
                            color: Theme.of(context).colorScheme.primary,
                            width: 2,
                          ),
                        ),
                        child: profileImageUrl != null
                            ? ClipOval(
                                child: Image.network(
                                  profileImageUrl!,
                                  fit: BoxFit.cover,
                                ),
                              )
                            : Icon(
                                Icons.add_a_photo,
                                size: 40,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Email (read-only)
                    TextField(
                      controller: TextEditingController(text: user.email),
                      decoration: InputDecoration(
                        labelText: isRTL ? 'البريد الإلكتروني' : 'Email',
                        prefixIcon: const Icon(Icons.email),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      enabled: false,
                    ),
                    const SizedBox(height: 16),

                    // Name field
                    TextField(
                      controller: nameController,
                      decoration: InputDecoration(
                        labelText: isRTL ? 'الاسم' : 'Name',
                        prefixIcon: const Icon(Icons.person),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Phone field
                    TextField(
                      controller: phoneController,
                      decoration: InputDecoration(
                        labelText: isRTL ? 'رقم الهاتف' : 'Phone',
                        prefixIcon: const Icon(Icons.phone),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      keyboardType: TextInputType.phone,
                    ),
                    const SizedBox(height: 16),

                    // Gender dropdown
                    DropdownButtonFormField<Gender>(
                      value: selectedGender,
                      decoration: InputDecoration(
                        labelText: isRTL ? 'النوع' : 'Gender',
                        prefixIcon: const Icon(Icons.person_outline),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      items: Gender.values.map((gender) {
                        String label;
                        switch (gender) {
                          case Gender.male:
                            label = isRTL ? 'ذكر' : 'Male';
                            break;
                          case Gender.female:
                            label = isRTL ? 'أنثى' : 'Female';
                            break;
                          case Gender.other:
                            label = isRTL ? 'آخر' : 'Other';
                            break;
                        }
                        return DropdownMenuItem(
                          value: gender,
                          child: Text(label),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          selectedGender = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),

                    // Birth date picker
                    ListTile(
                      leading: const Icon(Icons.cake),
                      title: Text(
                        birthDate != null
                            ? '${birthDate!.day}/${birthDate!.month}/${birthDate!.year}'
                            : (isRTL ? 'تاريخ الميلاد' : 'Birth Date'),
                      ),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: birthDate ?? DateTime.now().subtract(const Duration(days: 365 * 25)),
                          firstDate: DateTime(1950),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          setState(() {
                            birthDate = date;
                          });
                        }
                      },
                    ),

                    // Expiry date picker
                    ListTile(
                      leading: const Icon(Icons.event_busy),
                      title: Text(
                        expiryDate != null
                            ? '${expiryDate!.day}/${expiryDate!.month}/${expiryDate!.year}'
                            : (isRTL ? 'تاريخ الانتهاء' : 'Expiry Date'),
                      ),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: expiryDate ?? DateTime.now().add(const Duration(days: 365)),
                          firstDate: DateTime(2020),
                          lastDate: DateTime(2030),
                        );
                        if (date != null) {
                          setState(() {
                            expiryDate = date;
                          });
                        }
                      },
                    ),

                    // Admin switch (disabled for main admin)
                    SwitchListTile(
                      title: Text(isRTL ? 'مدير' : 'Admin'),
                      value: isAdmin,
                      onChanged: user.isMainAdmin ? null : (value) {
                        setState(() {
                          isAdmin = value;
                        });
                      },
                    ),

                    // Active switch (disabled for main admin)
                    SwitchListTile(
                      title: Text(isRTL ? 'نشط' : 'Active'),
                      value: isActive,
                      onChanged: user.isMainAdmin ? null : (value) {
                        setState(() {
                          isActive = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(isRTL ? 'إلغاء' : 'Cancel'),
              ),
              ElevatedButton(
                onPressed: () async {
                  final navigator = Navigator.of(context);
                  await _updateUser(
                    user,
                    displayName: nameController.text.trim().isEmpty
                        ? null
                        : nameController.text.trim(),
                    phoneNumber: phoneController.text.trim().isEmpty
                        ? null
                        : phoneController.text.trim(),
                    isAdmin: isAdmin,
                    isActive: isActive,
                    gender: selectedGender,
                    birthDate: birthDate,
                    expiryDate: expiryDate,
                    profileImageUrl: profileImageUrl,
                  );
                  navigator.pop();
                },
                child: Text(isRTL ? 'حفظ' : 'Save'),
              ),
            ],
          );
        },
      ),
    );
  }

  Future<void> _updateUser(
    UserModel user, {
    String? displayName,
    String? phoneNumber,
    required bool isAdmin,
    required bool isActive,
    Gender? gender,
    DateTime? birthDate,
    DateTime? expiryDate,
    String? profileImageUrl,
  }) async {
    try {
      final updatedUser = user.copyWith(
        displayName: displayName,
        phoneNumber: phoneNumber,
        role: isAdmin ? UserRole.admin : UserRole.user,
        isActive: isActive,
        gender: gender,
        birthDate: birthDate,
        expiryDate: expiryDate,
        profileImageUrl: profileImageUrl,
      );

      await _firestore
          .collection('users')
          .doc(user.id)
          .update(updatedUser.toMap());

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث المستخدم بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }

      _loadUsers();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث المستخدم: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _toggleUserStatus(UserModel user) async {
    try {
      await _firestore.collection('users').doc(user.id).update({
        'isActive': !user.isActive,
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              user.isActive ? 'تم إلغاء تفعيل المستخدم' : 'تم تفعيل المستخدم',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }

      _loadUsers();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تغيير حالة المستخدم: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showResetPasswordDialog(UserModel user) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;
    final passwordController = TextEditingController();
    bool obscurePassword = true;

    AppTheme.showBlurredDialog(
      context: context,
      child: StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            title: Text(
              isRTL ? 'إدارة كلمة المرور' : 'Password Management',
              textAlign: TextAlign.center,
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  isRTL
                      ? 'إدارة كلمة المرور للمستخدم "${user.displayName ?? user.email}"'
                      : 'Manage password for user "${user.displayName ?? user.email}"',
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),

                // New password field
                TextField(
                  controller: passwordController,
                  decoration: InputDecoration(
                    labelText: isRTL ? 'كلمة المرور الجديدة' : 'New Password',
                    prefixIcon: const Icon(Icons.lock),
                    suffixIcon: IconButton(
                      icon: Icon(
                        obscurePassword ? Icons.visibility : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setState(() {
                          obscurePassword = !obscurePassword;
                        });
                      },
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  obscureText: obscurePassword,
                ),
                const SizedBox(height: 16),

                // Generate password button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        passwordController.text = _generateRandomPassword();
                      });
                    },
                    icon: const Icon(Icons.auto_fix_high),
                    label: Text(isRTL ? 'توليد كلمة مرور قوية' : 'Generate Strong Password'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(height: 12),

                // Send email reset button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () => _sendPasswordResetEmail(user),
                    icon: const Icon(Icons.email),
                    label: Text(isRTL ? 'إرسال رابط إعادة التعيين' : 'Send Reset Email'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(isRTL ? 'إلغاء' : 'Cancel'),
              ),
              ElevatedButton(
                onPressed: passwordController.text.trim().isEmpty ? null : () async {
                  final navigator = Navigator.of(context);
                  await _changeUserPassword(user, passwordController.text.trim());
                  navigator.pop();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
                child: Text(isRTL ? 'تغيير كلمة المرور' : 'Change Password'),
              ),
            ],
          );
        },
      ),
    );
  }

  String _generateRandomPassword() {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#\$%^&*';
    final random = Random();
    return String.fromCharCodes(
      Iterable.generate(12, (_) => chars.codeUnitAt(random.nextInt(chars.length))),
    );
  }

  Future<void> _changeUserPassword(UserModel user, String newPassword) async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    try {
      // Note: In a real-world application, you would need Firebase Admin SDK
      // to change user passwords directly. For now, we'll use the reset email method
      // and update the user document to indicate a password change was requested.

      // Update user document with password change info
      await _firestore.collection('users').doc(user.id).update({
        'passwordChangeRequired': true,
        'passwordChangedAt': FieldValue.serverTimestamp(),
        'passwordChangedBy': _auth.currentUser?.email ?? 'admin',
        'tempPassword': newPassword, // In production, this should be hashed
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL
                ? 'تم تعيين كلمة المرور الجديدة للمستخدم ${user.displayName ?? user.email}'
                : 'New password set for user ${user.displayName ?? user.email}',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 5),
          ),
        );
      }

      // Also send a notification email
      await _sendPasswordChangeNotification(user, newPassword);

    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL
                ? 'خطأ في تغيير كلمة المرور: $e'
                : 'Error changing password: $e',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _sendPasswordChangeNotification(UserModel user, String newPassword) async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    try {
      // In a real application, you would send an email notification here
      // For now, we'll just show a message to the admin
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(isRTL ? 'كلمة المرور الجديدة' : 'New Password'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isRTL
                    ? 'كلمة المرور الجديدة للمستخدم ${user.displayName ?? user.email}:'
                    : 'New password for user ${user.displayName ?? user.email}:',
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: SelectableText(
                    newPassword,
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  isRTL
                    ? 'يرجى إبلاغ المستخدم بكلمة المرور الجديدة'
                    : 'Please inform the user of their new password',
                  style: TextStyle(
                    color: Colors.orange[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(isRTL ? 'موافق' : 'OK'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      debugPrint('Error showing password notification: $e');
    }
  }

  Future<void> _sendPasswordResetEmail(UserModel user) async {
    try {
      await _auth.sendPasswordResetEmail(email: user.email);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال رابط إعادة تعيين كلمة المرور إلى البريد الإلكتروني'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إرسال البريد الإلكتروني: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showDeleteUserDialog(UserModel user) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    AppTheme.showBlurredDialog(
      context: context,
      child: AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Text(isRTL ? 'حذف المستخدم' : 'Delete User'),
        content: Text(
          isRTL
              ? 'هل أنت متأكد من حذف المستخدم "${user.displayName ?? user.email}"؟'
              : 'Are you sure you want to delete user "${user.displayName ?? user.email}"?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              await _deleteUser(user);
              if (mounted) {
                Navigator.pop(context);
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(isRTL ? 'حذف' : 'Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteUser(UserModel user) async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    try {
      // Delete all user-related data
      await _deleteAllUserData(user.id);

      // Delete user document from Firestore
      await _firestore.collection('users').doc(user.id).delete();

      // Note: We don't delete from Firebase Auth as it requires the user to be signed in
      // In a production app, you might want to use Firebase Admin SDK for this

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL
                ? 'تم حذف المستخدم وجميع بياناته بنجاح'
                : 'User and all related data deleted successfully',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }

      _loadUsers();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL
                ? 'خطأ في حذف المستخدم: $e'
                : 'Error deleting user: $e',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteAllUserData(String userId) async {
    try {
      // Delete user's favorite errors (remove user from favoriteUsers arrays)
      final errorsSnapshot = await _firestore
          .collection('device_errors')
          .where('favoriteUsers', arrayContains: userId)
          .get();

      final batch = _firestore.batch();

      for (final doc in errorsSnapshot.docs) {
        final data = doc.data();
        final favoriteUsers = List<String>.from(data['favoriteUsers'] ?? []);
        favoriteUsers.remove(userId);

        batch.update(doc.reference, {'favoriteUsers': favoriteUsers});
      }

      // Delete user's login logs
      final loginLogsSnapshot = await _firestore
          .collection('login_logs')
          .where('userId', isEqualTo: userId)
          .get();

      for (final doc in loginLogsSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // Delete user's login history
      final loginHistorySnapshot = await _firestore
          .collection('login_history')
          .where('userId', isEqualTo: userId)
          .get();

      for (final doc in loginHistorySnapshot.docs) {
        batch.delete(doc.reference);
      }

      // Delete user's created errors (optional - you might want to keep them)
      final userErrorsSnapshot = await _firestore
          .collection('device_errors')
          .where('createdBy', isEqualTo: userId)
          .get();

      for (final doc in userErrorsSnapshot.docs) {
        // Option 1: Delete the error completely
        // batch.delete(doc.reference);

        // Option 2: Update createdBy to 'deleted_user' (recommended)
        batch.update(doc.reference, {'createdBy': 'deleted_user'});
      }

      // Delete user's settings
      final userSettingsDoc = _firestore.collection('user_settings').doc(userId);
      batch.delete(userSettingsDoc);

      // Commit all deletions
      await batch.commit();

      debugPrint('Successfully deleted all data for user: $userId');
    } catch (e) {
      debugPrint('Error deleting user data: $e');
      rethrow;
    }
  }

  void _showUserPermissionsDialog(UserModel user) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          isRTL
              ? 'صلاحيات ${user.displayName ?? user.email}'
              : '${user.displayName ?? user.email} Permissions',
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Current role
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.admin_panel_settings,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isRTL ? 'الدور الحالي' : 'Current Role',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          isRTL ? user.effectiveRoleNameAr : user.effectiveRoleName,
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              Text(
                isRTL ? 'الصلاحيات:' : 'Permissions:',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),

              const SizedBox(height: 8),

              // Permissions list
              Expanded(
                child: user.allPermissions.isEmpty
                    ? Center(
                        child: Text(
                          isRTL ? 'لا توجد صلاحيات' : 'No permissions',
                          style: const TextStyle(fontSize: 16),
                        ),
                      )
                    : ListView.builder(
                        itemCount: user.allPermissions.length,
                        itemBuilder: (context, index) {
                          final permission = user.allPermissions[index];
                          return ListTile(
                            leading: const Icon(Icons.check_circle, color: Colors.green),
                            title: Text(
                              perm.PermissionHelper.getPermissionName(permission, isRTL),
                            ),
                            subtitle: Text(
                              perm.PermissionHelper.getPermissionCategory(permission, isRTL),
                            ),
                          );
                        },
                      ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'إغلاق' : 'Close'),
          ),
          if (!user.isMainAdmin)
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _showEditUserRoleDialog(user);
              },
              child: Text(isRTL ? 'تعديل الدور' : 'Edit Role'),
            ),
        ],
      ),
    );
  }

  void _showEditUserRoleDialog(UserModel user) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'تعديل دور المستخدم' : 'Edit User Role'),
        content: Text(
          isRTL
              ? 'ستتم إضافة هذه الميزة قريباً'
              : 'This feature will be added soon',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'موافق' : 'OK'),
          ),
        ],
      ),
    );
  }

  void _navigateToRoleAssignment(UserModel user) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserRoleAssignmentScreen(user: user),
      ),
    );

    // If changes were made, reload the users list
    if (result == true) {
      _loadUsers();
    }
  }
}
