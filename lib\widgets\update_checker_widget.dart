import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/update_provider.dart';
import '../providers/locale_provider.dart';
import '../services/app_update_service.dart';
import 'update_dialog.dart';

class UpdateCheckerWidget extends StatefulWidget {
  final Widget child;
  final bool showNotificationBar;

  const UpdateCheckerWidget({
    super.key,
    required this.child,
    this.showNotificationBar = true,
  });

  @override
  State<UpdateCheckerWidget> createState() => _UpdateCheckerWidgetState();
}

class _UpdateCheckerWidgetState extends State<UpdateCheckerWidget>
    with WidgetsBindingObserver {
  bool _hasShownDialog = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Check for updates after widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkForUpdates();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Check for updates when app comes to foreground
    if (state == AppLifecycleState.resumed) {
      _checkForUpdates();
    }
  }

  Future<void> _checkForUpdates() async {
    if (!mounted) return;

    final updateProvider = Provider.of<UpdateProvider>(context, listen: false);

    // Check for updates silently
    await updateProvider.checkForUpdates(showLoading: false);

    // Show dialog if update is available and we haven't shown it yet
    if (mounted && updateProvider.shouldShowUpdateDialog() && !_hasShownDialog) {
      _showUpdateDialog(updateProvider.updateInfo!);
    }
  }

  void _showUpdateDialog(UpdateInfo updateInfo) {
    if (!mounted) return;

    _hasShownDialog = true;

    // Show dialog after a short delay to ensure UI is ready
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        UpdateDialog.show(
          context,
          updateInfo,
          canDismiss: !updateInfo.isRequired,
        ).then((_) {
          // Reset flag when dialog is dismissed
          _hasShownDialog = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<UpdateProvider>(
      builder: (context, updateProvider, child) {
        return Column(
          children: [
            // Update notification bar
            if (widget.showNotificationBar &&
                updateProvider.hasUpdate &&
                !updateProvider.isUpdateRequired)
              _buildUpdateNotificationBar(updateProvider),

            // Main content
            Expanded(child: widget.child),
          ],
        );
      },
    );
  }

  Widget _buildUpdateNotificationBar(UpdateProvider updateProvider) {
    return UpdateNotificationWidget(
      updateInfo: updateProvider.updateInfo!,
      onTap: () => _showUpdateDialog(updateProvider.updateInfo!),
      onDismiss: () => updateProvider.dismissUpdate(),
    );
  }
}

/// Floating update button widget
class FloatingUpdateButton extends StatelessWidget {
  const FloatingUpdateButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<UpdateProvider>(
      builder: (context, updateProvider, child) {
        if (!updateProvider.hasUpdate) return const SizedBox.shrink();

        final localeProvider = Provider.of<LocaleProvider>(context);
        final isRTL = localeProvider.isRTL;

        return Positioned(
          bottom: 80,
          right: isRTL ? null : 16,
          left: isRTL ? 16 : null,
          child: FloatingActionButton.extended(
            onPressed: () => UpdateDialog.show(context, updateProvider.updateInfo!),
            backgroundColor: _getUpdateColor(updateProvider.updateInfo!.type),
            foregroundColor: Colors.white,
            icon: Icon(_getUpdateIcon(updateProvider.updateInfo!.type)),
            label: Text(
              isRTL ? 'تحديث متوفر' : 'Update Available',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        );
      },
    );
  }

  IconData _getUpdateIcon(UpdateType type) {
    switch (type) {
      case UpdateType.required:
        return Icons.priority_high;
      case UpdateType.optional:
        return Icons.update;
      case UpdateType.maintenance:
        return Icons.build;
    }
  }

  Color _getUpdateColor(UpdateType type) {
    switch (type) {
      case UpdateType.required:
        return Colors.red;
      case UpdateType.optional:
        return Colors.blue;
      case UpdateType.maintenance:
        return Colors.orange;
    }
  }
}

/// Update status indicator widget
class UpdateStatusIndicator extends StatelessWidget {
  final bool showText;

  const UpdateStatusIndicator({
    super.key,
    this.showText = true,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<UpdateProvider>(
      builder: (context, updateProvider, child) {
        final localeProvider = Provider.of<LocaleProvider>(context);
        final isRTL = localeProvider.isRTL;

        if (updateProvider.isChecking) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              if (showText) ...[
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'فحص التحديثات...' : 'Checking updates...',
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ],
          );
        }

        if (updateProvider.hasUpdate) {
          return GestureDetector(
            onTap: () => UpdateDialog.show(context, updateProvider.updateInfo!),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _getUpdateIcon(updateProvider.updateInfo!.type),
                  size: 16,
                  color: _getUpdateColor(updateProvider.updateInfo!.type),
                ),
                if (showText) ...[
                  const SizedBox(width: 4),
                  Text(
                    isRTL ? 'تحديث' : 'Update',
                    style: TextStyle(
                      fontSize: 12,
                      color: _getUpdateColor(updateProvider.updateInfo!.type),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ],
            ),
          );
        }

        // No update available
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.check_circle,
              size: 16,
              color: Colors.green[600],
            ),
            if (showText) ...[
              const SizedBox(width: 4),
              Text(
                isRTL ? 'محدث' : 'Updated',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.green[600],
                ),
              ),
            ],
          ],
        );
      },
    );
  }

  IconData _getUpdateIcon(UpdateType type) {
    switch (type) {
      case UpdateType.required:
        return Icons.priority_high;
      case UpdateType.optional:
        return Icons.update;
      case UpdateType.maintenance:
        return Icons.build;
    }
  }

  Color _getUpdateColor(UpdateType type) {
    switch (type) {
      case UpdateType.required:
        return Colors.red;
      case UpdateType.optional:
        return Colors.blue;
      case UpdateType.maintenance:
        return Colors.orange;
    }
  }
}

/// Manual update check button
class ManualUpdateCheckButton extends StatelessWidget {
  const ManualUpdateCheckButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<UpdateProvider>(
      builder: (context, updateProvider, child) {
        final localeProvider = Provider.of<LocaleProvider>(context);
        final isRTL = localeProvider.isRTL;

        return IconButton(
          onPressed: updateProvider.isChecking
              ? null
              : () => updateProvider.checkForUpdates(forceCheck: true),
          icon: updateProvider.isChecking
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.refresh),
          tooltip: isRTL ? 'فحص التحديثات' : 'Check for updates',
        );
      },
    );
  }
}
