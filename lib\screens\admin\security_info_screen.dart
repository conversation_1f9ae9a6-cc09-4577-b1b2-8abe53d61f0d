import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../providers/locale_provider.dart';
import '../../providers/auth_provider.dart';
import '../../services/app_fingerprint_service.dart';
import '../../services/app_signature_service.dart';

class SecurityInfoScreen extends StatefulWidget {
  const SecurityInfoScreen({super.key});

  @override
  State<SecurityInfoScreen> createState() => _SecurityInfoScreenState();
}

class _SecurityInfoScreenState extends State<SecurityInfoScreen> {
  Map<String, dynamic>? _fingerprintInfo;
  Map<String, dynamic>? _securityReport;
  bool _isLoading = true;
  String? _appFingerprint;
  String? _secureToken;

  @override
  void initState() {
    super.initState();
    _loadSecurityInfo();
  }

  Future<void> _loadSecurityInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final fingerprintInfo = await AppFingerprintService.getFingerprintInfo();
      final securityReport = await AppSignatureService.getSecurityReport();
      final fingerprint = await AppFingerprintService.generateFingerprint();
      final token = await AppFingerprintService.generateSecureToken();

      setState(() {
        _fingerprintInfo = fingerprintInfo;
        _securityReport = securityReport;
        _appFingerprint = fingerprint;
        _secureToken = token;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل معلومات الأمان: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final isRTL = localeProvider.isRTL;

    if (!authProvider.isAdmin) {
      return Scaffold(
        appBar: AppBar(
          title: Text(
            isRTL ? 'غير مصرح' : 'Unauthorized',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
          elevation: 4,
          iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
          centerTitle: true,
        ),
        body: Center(
          child: Text(
            isRTL ? 'ليس لديك صلاحية للوصول لهذه الصفحة' : 'You don\'t have permission to access this page',
            style: const TextStyle(fontSize: 18),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isRTL ? 'معلومات الأمان' : 'Security Information',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSecurityInfo,
            tooltip: isRTL ? 'تحديث' : 'Refresh',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'copy_fingerprint',
                child: Row(
                  children: [
                    const Icon(Icons.copy),
                    const SizedBox(width: 8),
                    Text(isRTL ? 'نسخ البصمة' : 'Copy Fingerprint'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'copy_token',
                child: Row(
                  children: [
                    const Icon(Icons.security),
                    const SizedBox(width: 8),
                    Text(isRTL ? 'نسخ الرمز الآمن' : 'Copy Secure Token'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'verify_integrity',
                child: Row(
                  children: [
                    const Icon(Icons.verified),
                    const SizedBox(width: 8),
                    Text(isRTL ? 'فحص السلامة' : 'Verify Integrity'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // App Fingerprint Card
                  _buildFingerprintCard(isRTL),

                  const SizedBox(height: 16),

                  // Security Report Card
                  _buildSecurityReportCard(isRTL),

                  const SizedBox(height: 16),

                  // Device Information Card
                  _buildDeviceInfoCard(isRTL),

                  const SizedBox(height: 16),

                  // Security Actions Card
                  _buildSecurityActionsCard(isRTL),
                ],
              ),
            ),
    );
  }

  Widget _buildFingerprintCard(bool isRTL) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.fingerprint, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'بصمة التطبيق' : 'App Fingerprint',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (_appFingerprint != null) ...[
              _buildInfoRow(
                isRTL ? 'البصمة' : 'Fingerprint',
                '${_appFingerprint!.substring(0, 32)}...',
                copyable: true,
                fullValue: _appFingerprint!,
              ),
              const SizedBox(height: 8),
            ],

            if (_fingerprintInfo?['generated_at'] != null)
              _buildInfoRow(
                isRTL ? 'تاريخ الإنشاء' : 'Generated At',
                _fingerprintInfo!['generated_at'],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityReportCard(bool isRTL) {
    if (_securityReport == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.security, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'تقرير الأمان' : 'Security Report',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildSecurityStatus(
              isRTL ? 'صحة التوقيع' : 'Signature Valid',
              _securityReport!['signature_valid'] ?? false,
            ),

            _buildSecurityStatus(
              isRTL ? 'التطبيق آمن' : 'App Integrity',
              !(_securityReport!['is_tampered'] ?? true),
            ),

            _buildSecurityStatus(
              isRTL ? 'وضع الإصدار' : 'Release Mode',
              _securityReport!['is_release_mode'] ?? false,
            ),

            const SizedBox(height: 8),

            _buildInfoRow(
              isRTL ? 'المنصة' : 'Platform',
              _securityReport!['platform'] ?? 'Unknown',
            ),

            if (_securityReport!['signature'] != null)
              _buildInfoRow(
                isRTL ? 'التوقيع' : 'Signature',
                '${_securityReport!['signature'].toString().substring(0, 16)}...',
                copyable: true,
                fullValue: _securityReport!['signature'].toString(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceInfoCard(bool isRTL) {
    if (_fingerprintInfo?['components'] == null) return const SizedBox.shrink();

    final components = _fingerprintInfo!['components'] as Map<String, dynamic>;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.phone_android, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'معلومات الجهاز' : 'Device Information',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (components['app_name'] != null)
              _buildInfoRow(
                isRTL ? 'اسم التطبيق' : 'App Name',
                components['app_name'],
              ),

            if (components['version'] != null)
              _buildInfoRow(
                isRTL ? 'الإصدار' : 'Version',
                '${components['version']} (${components['build_number']})',
              ),

            if (components['platform'] != null)
              _buildInfoRow(
                isRTL ? 'المنصة' : 'Platform',
                components['platform'],
              ),

            if (components['device_model'] != null)
              _buildInfoRow(
                isRTL ? 'موديل الجهاز' : 'Device Model',
                components['device_model'],
              ),

            if (components['device_brand'] != null)
              _buildInfoRow(
                isRTL ? 'العلامة التجارية' : 'Brand',
                components['device_brand'],
              ),

            if (components['android_version'] != null)
              _buildInfoRow(
                isRTL ? 'إصدار أندرويد' : 'Android Version',
                components['android_version'],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityActionsCard(bool isRTL) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.admin_panel_settings, color: Colors.purple),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'إجراءات الأمان' : 'Security Actions',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ],
            ),
            const SizedBox(height: 16),

            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: () => _handleMenuAction('verify_integrity'),
                  icon: const Icon(Icons.verified),
                  label: Text(isRTL ? 'فحص السلامة' : 'Verify Integrity'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _generateNewToken(),
                  icon: const Icon(Icons.refresh),
                  label: Text(isRTL ? 'رمز جديد' : 'New Token'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _exportSecurityReport(),
                  icon: const Icon(Icons.download),
                  label: Text(isRTL ? 'تصدير التقرير' : 'Export Report'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityStatus(String label, bool isSecure) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            isSecure ? Icons.check_circle : Icons.error,
            color: isSecure ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(child: Text(label)),
          Text(
            isSecure ? 'آمن' : 'غير آمن',
            style: TextStyle(
              color: isSecure ? Colors.green : Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {bool copyable = false, String? fullValue}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          if (copyable)
            IconButton(
              icon: const Icon(Icons.copy, size: 16),
              onPressed: () => _copyToClipboard(fullValue ?? value),
              tooltip: 'نسخ',
            ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'copy_fingerprint':
        if (_appFingerprint != null) {
          _copyToClipboard(_appFingerprint!);
        }
        break;
      case 'copy_token':
        if (_secureToken != null) {
          _copyToClipboard(_secureToken!);
        }
        break;
      case 'verify_integrity':
        _verifyIntegrity();
        break;
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم النسخ إلى الحافظة'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  Future<void> _verifyIntegrity() async {
    try {
      final isValid = await AppFingerprintService.verifyAppIntegrity();
      final isSignatureValid = await AppSignatureService.verifyAppSignature();

      final message = isValid && isSignatureValid
          ? 'التطبيق آمن وسليم'
          : 'تم اكتشاف مشكلة في سلامة التطبيق';

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('نتيجة فحص السلامة'),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فحص السلامة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _generateNewToken() async {
    try {
      final newToken = await AppFingerprintService.generateSecureToken();
      setState(() {
        _secureToken = newToken;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء رمز آمن جديد'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء الرمز: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _exportSecurityReport() async {
    try {
      final report = {
        'fingerprint_info': _fingerprintInfo,
        'security_report': _securityReport,
        'app_fingerprint': _appFingerprint,
        'secure_token': _secureToken,
        'exported_at': DateTime.now().toIso8601String(),
      };

      final jsonString = const JsonEncoder.withIndent('  ').convert(report);
      await Clipboard.setData(ClipboardData(text: jsonString));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم نسخ التقرير إلى الحافظة'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تصدير التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
