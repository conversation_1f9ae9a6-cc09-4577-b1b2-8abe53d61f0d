import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../providers/login_log_provider.dart';
import '../../providers/locale_provider.dart';
import '../../models/login_log_model.dart';

class LoginLogsScreen extends StatefulWidget {
  final String? userId;

  const LoginLogsScreen({super.key, this.userId});

  @override
  State<LoginLogsScreen> createState() => _LoginLogsScreenState();
}

class _LoginLogsScreenState extends State<LoginLogsScreen> {
  @override
  void initState() {
    super.initState();
    _loadLogs();
  }

  Future<void> _loadLogs() async {
    final loginLogProvider = Provider.of<LoginLogProvider>(context, listen: false);

    if (widget.userId != null) {
      await loginLogProvider.fetchLogsForUser(widget.userId!);
    } else {
      await loginLogProvider.fetchLogs();
    }
  }

  @override
  Widget build(BuildContext context) {
    final loginLogProvider = Provider.of<LoginLogProvider>(context);
    final localeProvider = Provider.of<LocaleProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.userId != null
              ? localeProvider.isRTL
                  ? 'سجلات تسجيل الدخول للمستخدم'
                  : 'User Login Logs'
              : localeProvider.isRTL
                  ? 'سجلات تسجيل الدخول'
                  : 'Login Logs',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: localeProvider.isRTL ? 'تحديث' : 'Refresh',
            onPressed: _loadLogs,
          ),
        ],
      ),
      body: loginLogProvider.isLoading
          ? const Center(child: CircularProgressIndicator())
          : loginLogProvider.logs.isEmpty
              ? Center(
                  child: Text(
                    localeProvider.isRTL
                        ? 'لا توجد سجلات تسجيل دخول'
                        : 'No login logs found',
                    style: const TextStyle(fontSize: 18),
                  ),
                )
              : ListView.builder(
                  itemCount: loginLogProvider.logs.length,
                  itemBuilder: (context, index) {
                    final log = loginLogProvider.logs[index];
                    return _buildLogCard(context, log, localeProvider);
                  },
                ),
    );
  }

  Widget _buildLogCard(BuildContext context, LoginLog log, LocaleProvider localeProvider) {
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
    final formattedDate = dateFormat.format(log.timestamp);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: Icon(
          log.success ? Icons.check_circle : Icons.error,
          color: log.success ? Colors.green : Colors.red,
          size: 32,
        ),
        title: Text(log.userEmail),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              localeProvider.isRTL
                  ? 'الوقت: $formattedDate'
                  : 'Time: $formattedDate',
              style: const TextStyle(fontSize: 12),
            ),
            const SizedBox(height: 2),
            Text(
              localeProvider.isRTL
                  ? 'الجهاز: ${log.deviceInfo}'
                  : 'Device: ${log.deviceInfo}',
              style: const TextStyle(fontSize: 12),
            ),
            const SizedBox(height: 2),
            Text(
              localeProvider.isRTL
                  ? 'الموقع: ${log.location}'
                  : 'Location: ${log.location}',
              style: const TextStyle(fontSize: 12),
            ),
            const SizedBox(height: 2),
            Text(
              localeProvider.isRTL
                  ? 'عنوان IP: ${log.ipAddress}'
                  : 'IP Address: ${log.ipAddress}',
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
        isThreeLine: true,
        trailing: Text(
          log.success
              ? localeProvider.isRTL ? 'نجاح' : 'Success'
              : localeProvider.isRTL ? 'فشل' : 'Failed',
          style: TextStyle(
            color: log.success ? Colors.green : Colors.red,
            fontWeight: FontWeight.bold,
          ),
        ),
        onTap: () {
          // Show detailed log information
          _showLogDetailsDialog(context, log, localeProvider);
        },
      ),
    );
  }

  void _showLogDetailsDialog(BuildContext context, LoginLog log, LocaleProvider localeProvider) {
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
    final formattedDate = dateFormat.format(log.timestamp);

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            localeProvider.isRTL ? 'تفاصيل تسجيل الدخول' : 'Login Details',
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildDetailRow(
                  localeProvider.isRTL ? 'البريد الإلكتروني' : 'Email',
                  log.userEmail,
                  context,
                ),
                _buildDetailRow(
                  localeProvider.isRTL ? 'الحالة' : 'Status',
                  log.success
                      ? localeProvider.isRTL ? 'نجاح' : 'Success'
                      : localeProvider.isRTL ? 'فشل' : 'Failed',
                  context,
                  textColor: log.success ? Colors.green : Colors.red,
                ),
                _buildDetailRow(
                  localeProvider.isRTL ? 'الوقت' : 'Time',
                  formattedDate,
                  context,
                ),
                _buildDetailRow(
                  localeProvider.isRTL ? 'الجهاز' : 'Device',
                  log.deviceInfo,
                  context,
                ),
                _buildDetailRow(
                  localeProvider.isRTL ? 'عنوان IP' : 'IP Address',
                  log.ipAddress,
                  context,
                ),
                _buildDetailRow(
                  localeProvider.isRTL ? 'الموقع' : 'Location',
                  log.location,
                  context,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(localeProvider.isRTL ? 'إغلاق' : 'Close'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value, BuildContext context, {Color? textColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              color: textColor,
            ),
          ),
          const Divider(),
        ],
      ),
    );
  }
}
