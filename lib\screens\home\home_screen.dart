import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../providers/auth_provider.dart';
import '../../providers/theme_provider.dart';
import '../../providers/locale_provider.dart';
import '../../localization/app_localizations.dart';
import '../../widgets/app_logo.dart';
import '../../widgets/update_checker_widget.dart';
import '../../widgets/gradient_background.dart';
import 'welcome_home_screen.dart';
import '../search/search_screen.dart';
import '../settings/settings_screen.dart';
import '../errors/errors_list_screen.dart';
import '../favorites/favorites_screen.dart';
import '../admin/admin_panel_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const WelcomeHomeScreen(), // Welcome home screen
    const SearchScreen(), // Search screen
    const ErrorsListScreen(), // Errors screen
    const FavoritesScreen(), // Favorites screen
    const SettingsScreen(), // Settings screen
  ];

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    final localeProvider = Provider.of<LocaleProvider>(context);

    return GradientScaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(kToolbarHeight),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.primary.withAlpha(230),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: AppBar(
            title: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const AppLogo(
                    size: 32,
                    backgroundColor: Colors.transparent,
                    showShadow: false,
                    borderRadius: 6,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    context.t('app_name'),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.transparent,
            elevation: 0,
            iconTheme: const IconThemeData(color: Colors.white),
            actions: [
              IconButton(
                icon: Icon(
                  themeProvider.isDarkMode ? Icons.light_mode : Icons.dark_mode,
                  color: Colors.white,
                ),
                onPressed: () {
                  themeProvider.toggleTheme();
                },
              ),
            ],
          ),
        ),
      ),
      drawer: Drawer(
        child: Column(
          children: [
            // Drawer content without SafeArea to remove white space
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                    LayoutBuilder(
                      builder: (context, constraints) {
                        // Get screen dimensions
                        final screenHeight = MediaQuery.of(context).size.height;

                        // Define screen size categories
                        final isVerySmallScreen = screenHeight < 650;
                        final isSmallScreen = screenHeight < 700 && screenHeight >= 650;
                        final isLargeScreen = screenHeight >= 800;

                        // Calculate responsive dimensions based on screen size
                        double headerHeight;
                        double avatarSize;
                        double nameSize;
                        double emailSize;
                        double badgeSize;
                        double iconSize;
                        double horizontalPadding;
                        double verticalPadding;

                        if (isVerySmallScreen) {
                          headerHeight = 120.0;
                          avatarSize = 50.0;
                          nameSize = 15.0;
                          emailSize = 11.0;
                          badgeSize = 9.0;
                          iconSize = 11.0;
                          horizontalPadding = 14.0;
                          verticalPadding = 10.0;
                        } else if (isSmallScreen) {
                          headerHeight = 140.0;
                          avatarSize = 55.0;
                          nameSize = 16.0;
                          emailSize = 12.0;
                          badgeSize = 10.0;
                          iconSize = 12.0;
                          horizontalPadding = 16.0;
                          verticalPadding = 12.0;
                        } else if (isLargeScreen) {
                          headerHeight = 180.0;
                          avatarSize = 70.0;
                          nameSize = 20.0;
                          emailSize = 16.0;
                          badgeSize = 14.0;
                          iconSize = 16.0;
                          horizontalPadding = 24.0;
                          verticalPadding = 20.0;
                        } else {
                          // Default medium screen
                          headerHeight = 160.0;
                          avatarSize = 65.0;
                          nameSize = 18.0;
                          emailSize = 14.0;
                          badgeSize = 12.0;
                          iconSize = 14.0;
                          horizontalPadding = 20.0;
                          verticalPadding = 16.0;
                        }

                        return Container(
                          height: headerHeight,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: themeProvider.isDarkMode
                                  ? [
                                      const Color(0xFF1A1A1A),
                                      const Color(0xFF2A2A2A),
                                    ]
                                  : [
                                      Theme.of(context).colorScheme.primary,
                                      Theme.of(context).colorScheme.primary.withAlpha(204),
                                    ],
                            ),
                            border: themeProvider.isDarkMode
                                ? Border(
                                    bottom: BorderSide(
                                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                                      width: 1,
                                    ),
                                  )
                                : null,
                          ),
                          child: SafeArea(
                            top: true,
                            bottom: false,
                            left: false,
                            right: false,
                            child: Padding(
                              padding: EdgeInsets.fromLTRB(
                                horizontalPadding,
                                verticalPadding,
                                horizontalPadding,
                                verticalPadding,
                              ),
                              child: Row(
                                children: [
                                  // User Profile Image
                                  Container(
                                    width: avatarSize,
                                    height: avatarSize,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: Colors.white,
                                        width: isVerySmallScreen ? 2 : (isSmallScreen ? 2.5 : 3),
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withAlpha(76),
                                          blurRadius: isVerySmallScreen ? 6 : (isSmallScreen ? 8 : 12),
                                          offset: const Offset(0, 4),
                                        ),
                                      ],
                                    ),
                                    child: ClipOval(
                                      child: authProvider.user?.profileImageUrl != null
                                          ? CachedNetworkImage(
                                              imageUrl: authProvider.user!.profileImageUrl!,
                                              fit: BoxFit.cover,
                                              placeholder: (context, url) => Container(
                                                color: themeProvider.isDarkMode
                                                    ? Colors.grey[700]
                                                    : Colors.grey[300],
                                                child: Icon(
                                                  Icons.person,
                                                  size: avatarSize * 0.5,
                                                  color: themeProvider.isDarkMode
                                                      ? Colors.grey[400]
                                                      : Colors.grey[600],
                                                ),
                                              ),
                                              errorWidget: (context, url, error) => Container(
                                                color: themeProvider.isDarkMode
                                                    ? Colors.grey[700]
                                                    : Colors.grey[300],
                                                child: Icon(
                                                  Icons.person,
                                                  size: avatarSize * 0.5,
                                                  color: themeProvider.isDarkMode
                                                      ? Colors.grey[400]
                                                      : Colors.grey[600],
                                                ),
                                              ),
                                            )
                                          : Container(
                                              color: themeProvider.isDarkMode
                                                  ? Colors.grey[700]
                                                  : Colors.white.withAlpha(51),
                                              child: Icon(
                                                Icons.person,
                                                size: avatarSize * 0.5,
                                                color: themeProvider.isDarkMode
                                                    ? Colors.grey[400]
                                                    : Colors.white,
                                              ),
                                            ),
                                    ),
                                  ),
                                  SizedBox(width: isVerySmallScreen ? 10 : (isSmallScreen ? 12 : 16)),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: localeProvider.isRTL
                                          ? CrossAxisAlignment.end
                                          : CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          authProvider.user?.displayName ?? context.t('user'),
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: nameSize,
                                            fontWeight: FontWeight.w600,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        SizedBox(height: isVerySmallScreen ? 1 : (isSmallScreen ? 2 : 4)),
                                        Text(
                                          authProvider.user?.email ?? '',
                                          style: TextStyle(
                                            color: Colors.white.withAlpha(230),
                                            fontSize: emailSize,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        SizedBox(height: isVerySmallScreen ? 4 : (isSmallScreen ? 6 : 8)),
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                            horizontal: isVerySmallScreen ? 8 : (isSmallScreen ? 10 : 12),
                                            vertical: isVerySmallScreen ? 3 : (isSmallScreen ? 4 : 6),
                                          ),
                                          decoration: BoxDecoration(
                                            color: themeProvider.isDarkMode
                                                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
                                                : Colors.white.withAlpha(51),
                                            borderRadius: BorderRadius.circular(isVerySmallScreen ? 10 : (isSmallScreen ? 12 : 16)),
                                            border: Border.all(
                                              color: themeProvider.isDarkMode
                                                  ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.5)
                                                  : Colors.white.withAlpha(102),
                                              width: 1,
                                            ),
                                          ),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Icon(
                                                authProvider.isAdmin ? Icons.admin_panel_settings : Icons.person,
                                                color: Colors.white,
                                                size: iconSize,
                                              ),
                                              SizedBox(width: isVerySmallScreen ? 2 : (isSmallScreen ? 3 : 4)),
                                              Text(
                                                authProvider.isAdmin
                                                    ? (localeProvider.isRTL ? 'مدير' : 'Admin')
                                                    : (localeProvider.isRTL ? 'مستخدم' : 'User'),
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: badgeSize,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.home),
                      title: Text(context.t('home')),
                      selected: _selectedIndex == 0,
                      onTap: () {
                        _onItemTapped(0);
                        Navigator.pop(context);
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.search),
                      title: Text(context.t('search')),
                      selected: _selectedIndex == 1,
                      onTap: () {
                        _onItemTapped(1);
                        Navigator.pop(context);
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.error),
                      title: Text(context.t('errors')),
                      selected: _selectedIndex == 2,
                      onTap: () {
                        _onItemTapped(2);
                        Navigator.pop(context);
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.favorite),
                      title: Text(context.t('favorites')),
                      selected: _selectedIndex == 3,
                      onTap: () {
                        _onItemTapped(3);
                        Navigator.pop(context);
                      },
                    ),
                    if (authProvider.isAdmin)
                      ListTile(
                        leading: const Icon(Icons.admin_panel_settings),
                        title: Text(
                          localeProvider.isRTL ? 'لوحة الإدارة' : 'Admin Panel'
                        ),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const AdminPanelScreen(),
                            ),
                          );
                        },
                      ),
                    ListTile(
                      leading: const Icon(Icons.settings),
                      title: Text(context.t('settings')),
                      selected: _selectedIndex == 4,
                      onTap: () {
                        _onItemTapped(4);
                        Navigator.pop(context);
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      body: UpdateCheckerWidget(
        child: _screens[_selectedIndex],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        items: [
          BottomNavigationBarItem(
            icon: const Icon(Icons.home),
            label: context.t('home'),
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.search),
            label: context.t('search'),
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.error),
            label: context.t('errors'),
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.favorite),
            label: context.t('favorites'),
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.settings),
            label: context.t('settings'),
          ),
        ],
      ),
    );
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }
}