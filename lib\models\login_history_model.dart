import 'package:cloud_firestore/cloud_firestore.dart';

class LoginHistoryModel {
  final String id;
  final String userId;
  final String userEmail;
  final String? userName;
  final DateTime loginTime;
  final String? deviceInfo;
  final String? ipAddress;
  final String? location;
  final String? userAgent;
  final bool isSuccessful;

  LoginHistoryModel({
    required this.id,
    required this.userId,
    required this.userEmail,
    this.userName,
    required this.loginTime,
    this.deviceInfo,
    this.ipAddress,
    this.location,
    this.userAgent,
    this.isSuccessful = true,
  });

  factory LoginHistoryModel.fromMap(Map<String, dynamic> map) {
    return LoginHistoryModel(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      userEmail: map['userEmail'] ?? '',
      userName: map['userName'],
      loginTime: (map['loginTime'] as Timestamp).toDate(),
      deviceInfo: map['deviceInfo'],
      ipAddress: map['ipAddress'],
      location: map['location'],
      userAgent: map['userAgent'],
      isSuccessful: map['isSuccessful'] ?? true,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'userEmail': userEmail,
      'userName': userName,
      'loginTime': Timestamp.fromDate(loginTime),
      'deviceInfo': deviceInfo,
      'ipAddress': ipAddress,
      'location': location,
      'userAgent': userAgent,
      'isSuccessful': isSuccessful,
    };
  }

  LoginHistoryModel copyWith({
    String? id,
    String? userId,
    String? userEmail,
    String? userName,
    DateTime? loginTime,
    String? deviceInfo,
    String? ipAddress,
    String? location,
    String? userAgent,
    bool? isSuccessful,
  }) {
    return LoginHistoryModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userEmail: userEmail ?? this.userEmail,
      userName: userName ?? this.userName,
      loginTime: loginTime ?? this.loginTime,
      deviceInfo: deviceInfo ?? this.deviceInfo,
      ipAddress: ipAddress ?? this.ipAddress,
      location: location ?? this.location,
      userAgent: userAgent ?? this.userAgent,
      isSuccessful: isSuccessful ?? this.isSuccessful,
    );
  }

  String get formattedLoginTime {
    return '${loginTime.day}/${loginTime.month}/${loginTime.year} ${loginTime.hour}:${loginTime.minute.toString().padLeft(2, '0')}';
  }
}
