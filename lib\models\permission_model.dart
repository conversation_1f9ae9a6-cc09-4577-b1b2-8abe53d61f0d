import 'package:cloud_firestore/cloud_firestore.dart';

enum PermissionType {
  // Error Management
  viewErrors,
  addErrors,
  editErrors,
  deleteErrors,
  exportErrors,
  importErrors,
  
  // Category Management
  viewCategories,
  addCategories,
  editCategories,
  deleteCategories,
  exportCategories,
  importCategories,
  
  // Manufacturer Management
  viewManufacturers,
  addManufacturers,
  editManufacturers,
  deleteManufacturers,
  exportManufacturers,
  importManufacturers,
  
  // Model Management
  viewModels,
  addModels,
  editModels,
  deleteModels,
  exportModels,
  importModels,
  
  // User Management
  viewUsers,
  addUsers,
  editUsers,
  deleteUsers,
  changeUserPasswords,
  viewUserLoginHistory,
  
  // System Management
  viewAdminPanel,
  manageBackups,
  manageCloudStorage,
  viewSystemLogs,
  manageAppSettings,
  
  // Data Management
  createBackups,
  restoreBackups,
  exportAllData,
  importAllData,
  
  // Advanced Features
  manageFavorites,
  viewReports,
  manageNotifications,
}

class UserRole {
  final String id;
  final String name;
  final String nameAr;
  final String description;
  final String descriptionAr;
  final List<PermissionType> permissions;
  final bool isSystemRole;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const UserRole({
    required this.id,
    required this.name,
    required this.nameAr,
    required this.description,
    required this.descriptionAr,
    required this.permissions,
    this.isSystemRole = false,
    required this.createdAt,
    this.updatedAt,
  });

  factory UserRole.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserRole(
      id: doc.id,
      name: data['name'] ?? '',
      nameAr: data['nameAr'] ?? '',
      description: data['description'] ?? '',
      descriptionAr: data['descriptionAr'] ?? '',
      permissions: (data['permissions'] as List<dynamic>?)
          ?.map((p) => PermissionType.values.firstWhere(
                (type) => type.toString() == p,
                orElse: () => PermissionType.viewErrors,
              ))
          .toList() ?? [],
      isSystemRole: data['isSystemRole'] ?? false,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: data['updatedAt'] != null 
          ? (data['updatedAt'] as Timestamp).toDate() 
          : null,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'nameAr': nameAr,
      'description': description,
      'descriptionAr': descriptionAr,
      'permissions': permissions.map((p) => p.toString()).toList(),
      'isSystemRole': isSystemRole,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  UserRole copyWith({
    String? id,
    String? name,
    String? nameAr,
    String? description,
    String? descriptionAr,
    List<PermissionType>? permissions,
    bool? isSystemRole,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserRole(
      id: id ?? this.id,
      name: name ?? this.name,
      nameAr: nameAr ?? this.nameAr,
      description: description ?? this.description,
      descriptionAr: descriptionAr ?? this.descriptionAr,
      permissions: permissions ?? this.permissions,
      isSystemRole: isSystemRole ?? this.isSystemRole,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool hasPermission(PermissionType permission) {
    return permissions.contains(permission);
  }

  bool hasAnyPermission(List<PermissionType> permissionList) {
    return permissionList.any((permission) => permissions.contains(permission));
  }

  bool hasAllPermissions(List<PermissionType> permissionList) {
    return permissionList.every((permission) => permissions.contains(permission));
  }
}

// Predefined system roles
class SystemRoles {
  static UserRole get superAdmin => UserRole(
    id: 'super_admin',
    name: 'Super Administrator',
    nameAr: 'المدير العام',
    description: 'Full system access with all permissions',
    descriptionAr: 'وصول كامل للنظام مع جميع الصلاحيات',
    permissions: PermissionType.values,
    isSystemRole: true,
    createdAt: DateTime.now(),
  );

  static UserRole get admin => UserRole(
    id: 'admin',
    name: 'Administrator',
    nameAr: 'مدير',
    description: 'Administrative access with most permissions',
    descriptionAr: 'وصول إداري مع معظم الصلاحيات',
    permissions: [
      // Error Management
      PermissionType.viewErrors,
      PermissionType.addErrors,
      PermissionType.editErrors,
      PermissionType.deleteErrors,
      PermissionType.exportErrors,
      PermissionType.importErrors,
      
      // Category Management
      PermissionType.viewCategories,
      PermissionType.addCategories,
      PermissionType.editCategories,
      PermissionType.deleteCategories,
      
      // Manufacturer Management
      PermissionType.viewManufacturers,
      PermissionType.addManufacturers,
      PermissionType.editManufacturers,
      PermissionType.deleteManufacturers,
      
      // Model Management
      PermissionType.viewModels,
      PermissionType.addModels,
      PermissionType.editModels,
      PermissionType.deleteModels,
      
      // User Management (limited)
      PermissionType.viewUsers,
      PermissionType.addUsers,
      PermissionType.editUsers,
      
      // System Management (limited)
      PermissionType.viewAdminPanel,
      PermissionType.manageBackups,
      
      // Advanced Features
      PermissionType.manageFavorites,
      PermissionType.viewReports,
    ],
    isSystemRole: true,
    createdAt: DateTime.now(),
  );

  static UserRole get moderator => UserRole(
    id: 'moderator',
    name: 'Moderator',
    nameAr: 'مشرف',
    description: 'Content moderation with limited administrative access',
    descriptionAr: 'إشراف على المحتوى مع وصول إداري محدود',
    permissions: [
      // Error Management
      PermissionType.viewErrors,
      PermissionType.addErrors,
      PermissionType.editErrors,
      PermissionType.exportErrors,
      
      // Category Management
      PermissionType.viewCategories,
      PermissionType.addCategories,
      PermissionType.editCategories,
      
      // Manufacturer Management
      PermissionType.viewManufacturers,
      PermissionType.addManufacturers,
      PermissionType.editManufacturers,
      
      // Model Management
      PermissionType.viewModels,
      PermissionType.addModels,
      PermissionType.editModels,
      
      // Advanced Features
      PermissionType.manageFavorites,
    ],
    isSystemRole: true,
    createdAt: DateTime.now(),
  );

  static UserRole get user => UserRole(
    id: 'user',
    name: 'Regular User',
    nameAr: 'مستخدم عادي',
    description: 'Basic user access for viewing and searching',
    descriptionAr: 'وصول أساسي للمشاهدة والبحث',
    permissions: [
      PermissionType.viewErrors,
      PermissionType.viewCategories,
      PermissionType.viewManufacturers,
      PermissionType.viewModels,
      PermissionType.manageFavorites,
    ],
    isSystemRole: true,
    createdAt: DateTime.now(),
  );

  static UserRole get viewer => UserRole(
    id: 'viewer',
    name: 'Viewer',
    nameAr: 'مشاهد',
    description: 'Read-only access for viewing content',
    descriptionAr: 'وصول للقراءة فقط لمشاهدة المحتوى',
    permissions: [
      PermissionType.viewErrors,
      PermissionType.viewCategories,
      PermissionType.viewManufacturers,
      PermissionType.viewModels,
    ],
    isSystemRole: true,
    createdAt: DateTime.now(),
  );

  static List<UserRole> get allSystemRoles => [
    superAdmin,
    admin,
    moderator,
    user,
    viewer,
  ];
}

// Permission helper functions
class PermissionHelper {
  static String getPermissionName(PermissionType permission, bool isArabic) {
    switch (permission) {
      // Error Management
      case PermissionType.viewErrors:
        return isArabic ? 'عرض الأعطال' : 'View Errors';
      case PermissionType.addErrors:
        return isArabic ? 'إضافة الأعطال' : 'Add Errors';
      case PermissionType.editErrors:
        return isArabic ? 'تعديل الأعطال' : 'Edit Errors';
      case PermissionType.deleteErrors:
        return isArabic ? 'حذف الأعطال' : 'Delete Errors';
      case PermissionType.exportErrors:
        return isArabic ? 'تصدير الأعطال' : 'Export Errors';
      case PermissionType.importErrors:
        return isArabic ? 'استيراد الأعطال' : 'Import Errors';
        
      // Category Management
      case PermissionType.viewCategories:
        return isArabic ? 'عرض الفئات' : 'View Categories';
      case PermissionType.addCategories:
        return isArabic ? 'إضافة الفئات' : 'Add Categories';
      case PermissionType.editCategories:
        return isArabic ? 'تعديل الفئات' : 'Edit Categories';
      case PermissionType.deleteCategories:
        return isArabic ? 'حذف الفئات' : 'Delete Categories';
      case PermissionType.exportCategories:
        return isArabic ? 'تصدير الفئات' : 'Export Categories';
      case PermissionType.importCategories:
        return isArabic ? 'استيراد الفئات' : 'Import Categories';
        
      // Add more cases for other permissions...
      default:
        return permission.toString().split('.').last;
    }
  }

  static String getPermissionCategory(PermissionType permission, bool isArabic) {
    if (permission.toString().contains('Error')) {
      return isArabic ? 'إدارة الأعطال' : 'Error Management';
    } else if (permission.toString().contains('Categor')) {
      return isArabic ? 'إدارة الفئات' : 'Category Management';
    } else if (permission.toString().contains('Manufacturer')) {
      return isArabic ? 'إدارة الشركات' : 'Manufacturer Management';
    } else if (permission.toString().contains('Model')) {
      return isArabic ? 'إدارة الموديلات' : 'Model Management';
    } else if (permission.toString().contains('User')) {
      return isArabic ? 'إدارة المستخدمين' : 'User Management';
    } else if (permission.toString().contains('Backup') || permission.toString().contains('Data')) {
      return isArabic ? 'إدارة البيانات' : 'Data Management';
    } else {
      return isArabic ? 'إدارة النظام' : 'System Management';
    }
  }
}
