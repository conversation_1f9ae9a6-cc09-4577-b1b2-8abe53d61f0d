import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة تحسين شاملة للتطبيق
/// تركز على تحسين الأداء وتقليل الحجم وتحسين السرعة
class AppOptimizationService {
  static AppOptimizationService? _instance;
  static AppOptimizationService get instance => _instance ??= AppOptimizationService._();
  
  AppOptimizationService._();

  // إعدادات التحسين
  static const int _maxMemoryUsage = 100 * 1024 * 1024; // 100 MB
  static const int _cacheCleanupInterval = 300; // 5 دقائق
  static const int _maxImageCacheSize = 20 * 1024 * 1024; // 20 MB للصور
  
  Timer? _cleanupTimer;
  bool _isInitialized = false;
  
  /// تهيئة خدمة التحسين
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // تنظيف الملفات المؤقتة القديمة
      await _cleanupTempFiles();
      
      // تحسين إعدادات الذاكرة
      await _optimizeMemorySettings();
      
      // بدء مراقبة الأداء
      _startPerformanceMonitoring();
      
      // تحسين الصور المخزنة مؤقتاً
      await _optimizeImageCache();
      
      _isInitialized = true;
      debugPrint('✅ App Optimization Service initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing App Optimization Service: $e');
    }
  }

  /// تنظيف الملفات المؤقتة القديمة
  Future<void> _cleanupTempFiles() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final cacheDir = await getApplicationCacheDirectory();
      
      // تنظيف الملفات الأقدم من 7 أيام
      final cutoffDate = DateTime.now().subtract(const Duration(days: 7));
      
      await _cleanDirectory(tempDir, cutoffDate);
      await _cleanDirectory(cacheDir, cutoffDate);
      
      debugPrint('🧹 Temporary files cleaned up');
    } catch (e) {
      debugPrint('Error cleaning temp files: $e');
    }
  }

  /// تنظيف مجلد معين
  Future<void> _cleanDirectory(Directory dir, DateTime cutoffDate) async {
    try {
      if (!await dir.exists()) return;
      
      final files = dir.listSync(recursive: true);
      int deletedCount = 0;
      
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          if (stat.modified.isBefore(cutoffDate)) {
            try {
              await file.delete();
              deletedCount++;
            } catch (e) {
              // تجاهل الأخطاء في حذف الملفات المحمية
            }
          }
        }
      }
      
      if (deletedCount > 0) {
        debugPrint('🗑️ Deleted $deletedCount old files from ${dir.path}');
      }
    } catch (e) {
      debugPrint('Error cleaning directory ${dir.path}: $e');
    }
  }

  /// تحسين إعدادات الذاكرة
  Future<void> _optimizeMemorySettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // تحسين حجم الكاش للصور
      await prefs.setInt('image_cache_size', _maxImageCacheSize);
      
      // تحسين إعدادات الشبكة
      await prefs.setBool('enable_network_cache', true);
      await prefs.setInt('network_cache_duration', 300); // 5 دقائق
      
      debugPrint('⚙️ Memory settings optimized');
    } catch (e) {
      debugPrint('Error optimizing memory settings: $e');
    }
  }

  /// بدء مراقبة الأداء
  void _startPerformanceMonitoring() {
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(
      const Duration(seconds: _cacheCleanupInterval),
      (_) => _performPeriodicCleanup(),
    );
    
    debugPrint('📊 Performance monitoring started');
  }

  /// تنظيف دوري للذاكرة
  Future<void> _performPeriodicCleanup() async {
    try {
      // تنظيف الذاكرة إذا تجاوزت الحد المسموح
      final memoryUsage = await _getMemoryUsage();
      if (memoryUsage > _maxMemoryUsage) {
        await _forceMemoryCleanup();
      }
      
      // تنظيف كاش الصور
      await _cleanupImageCache();
      
    } catch (e) {
      debugPrint('Error in periodic cleanup: $e');
    }
  }

  /// الحصول على استخدام الذاكرة الحالي
  Future<int> _getMemoryUsage() async {
    try {
      // تقدير تقريبي لاستخدام الذاكرة
      final info = ProcessInfo.currentRss;
      return info;
    } catch (e) {
      // في حالة عدم توفر المعلومات، نعيد قيمة افتراضية
      return 50 * 1024 * 1024; // 50 MB
    }
  }

  /// إجبار تنظيف الذاكرة
  Future<void> _forceMemoryCleanup() async {
    try {
      // تنظيف كاش الصور
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();
      
      // تنظيف كاش الشبكة
      await _clearNetworkCache();
      
      debugPrint('🧽 Forced memory cleanup completed');
    } catch (e) {
      debugPrint('Error in force memory cleanup: $e');
    }
  }

  /// تحسين كاش الصور
  Future<void> _optimizeImageCache() async {
    try {
      // تحديد حجم كاش الصور
      PaintingBinding.instance.imageCache.maximumSize = 100; // عدد الصور
      PaintingBinding.instance.imageCache.maximumSizeBytes = _maxImageCacheSize;
      
      debugPrint('🖼️ Image cache optimized');
    } catch (e) {
      debugPrint('Error optimizing image cache: $e');
    }
  }

  /// تنظيف كاش الصور
  Future<void> _cleanupImageCache() async {
    try {
      final imageCache = PaintingBinding.instance.imageCache;
      
      // إذا تجاوز الكاش الحد المسموح، قم بتنظيفه
      if (imageCache.currentSizeBytes > _maxImageCacheSize * 0.8) {
        imageCache.clear();
        debugPrint('🖼️ Image cache cleared due to size limit');
      }
    } catch (e) {
      debugPrint('Error cleaning image cache: $e');
    }
  }

  /// تنظيف كاش الشبكة
  Future<void> _clearNetworkCache() async {
    try {
      // تنظيف كاش HTTP
      HttpClient().close(force: true);
      
      debugPrint('🌐 Network cache cleared');
    } catch (e) {
      debugPrint('Error clearing network cache: $e');
    }
  }

  /// تحسين أداء التطبيق للأجهزة الضعيفة
  Future<void> optimizeForLowEndDevice() async {
    try {
      // تقليل حجم كاش الصور أكثر
      PaintingBinding.instance.imageCache.maximumSize = 50;
      PaintingBinding.instance.imageCache.maximumSizeBytes = 10 * 1024 * 1024; // 10 MB
      
      // تقليل جودة الرسوم المتحركة
      timeDilation = 0.5; // تسريع الرسوم المتحركة
      
      // تنظيف فوري للذاكرة
      await _forceMemoryCleanup();
      
      debugPrint('📱 App optimized for low-end device');
    } catch (e) {
      debugPrint('Error optimizing for low-end device: $e');
    }
  }

  /// الحصول على إحصائيات الأداء
  Future<Map<String, dynamic>> getPerformanceStats() async {
    try {
      final imageCache = PaintingBinding.instance.imageCache;
      final memoryUsage = await _getMemoryUsage();
      
      return {
        'memory_usage_mb': (memoryUsage / (1024 * 1024)).toStringAsFixed(2),
        'image_cache_size_mb': (imageCache.currentSizeBytes / (1024 * 1024)).toStringAsFixed(2),
        'image_cache_count': imageCache.currentSize,
        'max_image_cache_mb': (_maxImageCacheSize / (1024 * 1024)).toStringAsFixed(2),
        'is_optimized': _isInitialized,
      };
    } catch (e) {
      debugPrint('Error getting performance stats: $e');
      return {};
    }
  }

  /// تنظيف شامل للتطبيق
  Future<void> performFullCleanup() async {
    try {
      await _cleanupTempFiles();
      await _forceMemoryCleanup();
      await _optimizeImageCache();
      
      debugPrint('🧹 Full app cleanup completed');
    } catch (e) {
      debugPrint('Error in full cleanup: $e');
    }
  }

  /// إيقاف خدمة التحسين
  void dispose() {
    _cleanupTimer?.cancel();
    _cleanupTimer = null;
    _isInitialized = false;
    debugPrint('🛑 App Optimization Service disposed');
  }
}
