import 'package:flutter_test/flutter_test.dart';

/// Test version comparison logic similar to AppUpdateService._isVersionLower
bool isVersionLower(String current, String target) {
  try {
    final currentParts = current.split('.').map(int.parse).toList();
    final targetParts = target.split('.').map(int.parse).toList();

    // Ensure both lists have the same length
    while (currentParts.length < targetParts.length) {
      currentParts.add(0);
    }
    while (targetParts.length < currentParts.length) {
      targetParts.add(0);
    }

    for (int i = 0; i < currentParts.length; i++) {
      if (currentParts[i] < targetParts[i]) {
        return true;
      } else if (currentParts[i] > targetParts[i]) {
        return false;
      }
    }
    return false; // Versions are equal
  } catch (e) {
    // Error comparing versions: $e
    return false;
  }
}

void main() {
  group('Version Comparison Tests', () {
    test('Current version 1.0.4 vs different target versions', () {
      const currentVersion = '1.0.4';

      // Test cases with expected results
      final testCases = [
        // [target_version, expected_result, description]
        ['1.0.3', false, 'Current is higher than target'],
        ['1.0.4', false, 'Current equals target'],
        ['1.0.5', true, 'Current is lower than target'],
        ['1.1.0', true, 'Current is lower (minor version)'],
        ['2.0.0', true, 'Current is lower (major version)'],
        ['0.9.9', false, 'Current is higher (all parts)'],
        ['*******', true, 'Target has more parts'],
        ['1.0', false, 'Target has fewer parts (1.0.4 > 1.0.0)'],
      ];

      for (final testCase in testCases) {
        final target = testCase[0] as String;
        final expected = testCase[1] as bool;
        final description = testCase[2] as String;

        final result = isVersionLower(currentVersion, target);

        // Testing: $currentVersion vs $target
        // Expected: $expected, Got: $result
        // Description: $description

        expect(result, equals(expected),
          reason: 'Failed for $currentVersion vs $target: $description');
      }
    });

    test('Edge cases and error handling', () {
      // Test invalid version formats
      expect(isVersionLower('invalid', '1.0.0'), false);
      expect(isVersionLower('1.0.0', 'invalid'), false);
      expect(isVersionLower('', '1.0.0'), false);
      expect(isVersionLower('1.0.0', ''), false);

      // Test with non-numeric parts
      expect(isVersionLower('1.0.a', '1.0.1'), false);
      expect(isVersionLower('1.0.1', '1.0.b'), false);
    });

    test('Real-world scenarios for version 1.0.4', () {
      const currentVersion = '1.0.4';

      // Scenario 1: Force update (current < minimum)
      expect(isVersionLower(currentVersion, '1.0.5'), true,
        reason: 'Should force update when current < minimum');

      // Scenario 2: Optional update (minimum <= current < latest)
      expect(isVersionLower(currentVersion, '1.0.4'), false,
        reason: 'No update needed when current = minimum');
      expect(isVersionLower(currentVersion, '1.0.6'), true,
        reason: 'Optional update when current < latest');

      // Scenario 3: No update needed (current >= latest)
      expect(isVersionLower(currentVersion, '1.0.3'), false,
        reason: 'No update when current > latest');
    });

    test('Semantic versioning examples', () {
      // Major version changes
      expect(isVersionLower('1.0.4', '2.0.0'), true);
      expect(isVersionLower('2.0.0', '1.0.4'), false);

      // Minor version changes
      expect(isVersionLower('1.0.4', '1.1.0'), true);
      expect(isVersionLower('1.1.0', '1.0.4'), false);

      // Patch version changes
      expect(isVersionLower('1.0.4', '1.0.5'), true);
      expect(isVersionLower('1.0.5', '1.0.4'), false);

      // Equal versions
      expect(isVersionLower('1.0.4', '1.0.4'), false);
    });
  });
}
