import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/firestore_service.dart';

class ManufacturerProvider with ChangeNotifier {
  final FirestoreService _firestoreService = FirestoreService();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  List<String> _manufacturers = [];
  final Map<String, List<String>> _modelsByManufacturer = {};
  final Map<String, String> _modelDeviceTypes = {}; // Map<manufacturer_model, deviceType>
  final List<String> _deviceTypes = [
    'Smartphone',
    'Tablet',
    'Laptop',
    'Desktop',
    'TV',
    'Refrigerator',
    'Washing Machine',
    'Air Conditioner',
    'Microwave',
    'Other'
  ];

  bool _isLoading = false;
  String? _error;

  List<String> get manufacturers => _manufacturers;
  List<String> get deviceTypes => _deviceTypes;

  List<String> getModelsForManufacturer(String manufacturer) {
    return _modelsByManufacturer[manufacturer] ?? [];
  }

  String getDeviceTypeForModel(String manufacturer, String model) {
    final key = '${manufacturer}_$model';
    return _modelDeviceTypes[key] ?? 'Other';
  }

  bool get isLoading => _isLoading;
  String? get error => _error;

  // Fetch all manufacturers
  Future<void> fetchManufacturers() async {
    _setLoading(true);
    _clearError();

    try {
      _manufacturers = await _firestoreService.getUniqueManufacturers();
      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    } catch (e) {
      debugPrint('Error getting manufacturers: $e');
      // If we have permission issues or no data, use some sample manufacturers
      if (e.toString().contains('permission-denied') ||
          e.toString().contains('collection-not-found') ||
          _manufacturers.isEmpty) {
        _manufacturers = [
          'Samsung',
          'LG',
          'Whirlpool',
          'Bosch',
          'Siemens',
          'Electrolux',
          'Haier',
          'Panasonic',
          'Sharp',
          'Toshiba',
        ];

        // Add some sample models for each manufacturer
        _modelsByManufacturer['Samsung'] = ['Galaxy S21', 'Galaxy Note 20', 'RF28R7351SG', 'WA50R5400AW'];
        _modelsByManufacturer['LG'] = ['OLED55C1PUB', 'WM3900HWA', '**********', 'LP0817WSR'];
        _modelsByManufacturer['Whirlpool'] = ['WTW5000DW', 'WRF535SWHZ', 'WDF520PADM', 'WMH31017HS'];
        _modelsByManufacturer['Bosch'] = ['WAT28400UC', 'B36CL80ENS', 'SHX3AR75UC', 'HMV3053U'];
        _modelsByManufacturer['Siemens'] = ['WM14T790GB', 'KA93NVIFP', 'SN236I03ME', 'HF24M564'];

        SchedulerBinding.instance.addPostFrameCallback((_) {
          notifyListeners();
        });
      } else {
        _setError(e.toString());
      }
    } finally {
      _setLoading(false);
    }
  }

  // Fetch models for a specific manufacturer
  Future<void> fetchModelsForManufacturer(String manufacturer) async {
    if (manufacturer.isEmpty) return;

    // If we already have models for this manufacturer, don't fetch again
    if (_modelsByManufacturer.containsKey(manufacturer) &&
        _modelsByManufacturer[manufacturer]!.isNotEmpty) {
      return;
    }

    _setLoading(true);
    _clearError();

    try {
      // Get models from Firestore
      final modelsQuery = await _firestore.collection('models')
          .where('manufacturer', isEqualTo: manufacturer)
          .get();

      final List<String> models = [];

      for (var doc in modelsQuery.docs) {
        final data = doc.data();
        final modelName = data['name'] as String? ?? '';
        final deviceType = data['deviceType'] as String? ?? 'Other';

        if (modelName.isNotEmpty) {
          models.add(modelName);

          // Store device type
          final key = '${manufacturer}_$modelName';
          _modelDeviceTypes[key] = deviceType;
        }
      }

      models.sort();
      _modelsByManufacturer[manufacturer] = models;

      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    } catch (e) {
      debugPrint('Error getting models for manufacturer: $e');
      // If we can't fetch from Firestore, check if we have sample data
      if (!_modelsByManufacturer.containsKey(manufacturer)) {
        _modelsByManufacturer[manufacturer] = [];
      }
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Add a new manufacturer
  Future<bool> addManufacturer(String name) async {
    if (name.isEmpty) return false;
    if (_manufacturers.contains(name)) return true; // Already exists

    _setLoading(true);
    _clearError();

    try {
      // Add to Firestore manufacturers collection
      await _firestore.collection('manufacturers').add({
        'name': name,
        'createdAt': FieldValue.serverTimestamp(),
      });

      // Update local list
      _manufacturers.add(name);
      _manufacturers.sort();
      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
      return true;
    } catch (e) {
      debugPrint('Error adding manufacturer: $e');
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Add a new model for a manufacturer
  Future<bool> addModel(String manufacturer, String model, {String deviceType = 'Other'}) async {
    if (manufacturer.isEmpty || model.isEmpty) return false;

    // Ensure manufacturer exists
    if (!_manufacturers.contains(manufacturer)) {
      final success = await addManufacturer(manufacturer);
      if (!success) return false;
    }

    // Check if model already exists for this manufacturer
    final models = _modelsByManufacturer[manufacturer] ?? [];
    if (models.contains(model)) {
      // Update device type if model already exists
      final key = '${manufacturer}_$model';
      _modelDeviceTypes[key] = deviceType;
      return true;
    }

    _setLoading(true);
    _clearError();

    try {
      // Add to Firestore models collection
      await _firestore.collection('models').add({
        'manufacturer': manufacturer,
        'name': model,
        'deviceType': deviceType,
        'createdAt': FieldValue.serverTimestamp(),
      });

      // Update local list
      if (!_modelsByManufacturer.containsKey(manufacturer)) {
        _modelsByManufacturer[manufacturer] = [];
      }
      _modelsByManufacturer[manufacturer]!.add(model);
      _modelsByManufacturer[manufacturer]!.sort();

      // Store device type
      final key = '${manufacturer}_$model';
      _modelDeviceTypes[key] = deviceType;

      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
      return true;
    } catch (e) {
      debugPrint('Error adding model: $e');
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update a manufacturer name
  Future<bool> updateManufacturer(String oldName, String newName) async {
    if (oldName.isEmpty || newName.isEmpty) return false;
    if (oldName == newName) return true; // No change needed
    if (!_manufacturers.contains(oldName)) return false; // Manufacturer doesn't exist
    if (_manufacturers.contains(newName) && oldName != newName) return false; // New name already exists

    _setLoading(true);
    _clearError();

    try {
      // Get the manufacturer document
      final manufacturerQuery = await _firestore.collection('manufacturers')
          .where('name', isEqualTo: oldName)
          .limit(1)
          .get();

      if (manufacturerQuery.docs.isEmpty) {
        _setError('Manufacturer not found');
        return false;
      }

      // Update the manufacturer name in Firestore
      final manufacturerDoc = manufacturerQuery.docs.first;
      await manufacturerDoc.reference.update({
        'name': newName,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update all models associated with this manufacturer
      final modelsQuery = await _firestore.collection('models')
          .where('manufacturer', isEqualTo: oldName)
          .get();

      final batch = _firestore.batch();
      for (var doc in modelsQuery.docs) {
        batch.update(doc.reference, {
          'manufacturer': newName,
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }
      await batch.commit();

      // Update local lists
      _manufacturers.remove(oldName);
      _manufacturers.add(newName);
      _manufacturers.sort();

      if (_modelsByManufacturer.containsKey(oldName)) {
        _modelsByManufacturer[newName] = _modelsByManufacturer[oldName]!;
        _modelsByManufacturer.remove(oldName);
      }

      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
      return true;
    } catch (e) {
      debugPrint('Error updating manufacturer: $e');
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete a manufacturer and all its models
  Future<bool> deleteManufacturer(String name) async {
    if (name.isEmpty) return false;
    if (!_manufacturers.contains(name)) return false; // Manufacturer doesn't exist

    _setLoading(true);
    _clearError();

    try {
      // Get the manufacturer document
      final manufacturerQuery = await _firestore.collection('manufacturers')
          .where('name', isEqualTo: name)
          .limit(1)
          .get();

      if (manufacturerQuery.docs.isEmpty) {
        _setError('Manufacturer not found');
        return false;
      }

      // Delete the manufacturer from Firestore
      final manufacturerDoc = manufacturerQuery.docs.first;
      await manufacturerDoc.reference.delete();

      // Delete all models associated with this manufacturer
      final modelsQuery = await _firestore.collection('models')
          .where('manufacturer', isEqualTo: name)
          .get();

      final batch = _firestore.batch();
      for (var doc in modelsQuery.docs) {
        batch.delete(doc.reference);
      }
      await batch.commit();

      // Update local lists
      _manufacturers.remove(name);
      _modelsByManufacturer.remove(name);

      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
      return true;
    } catch (e) {
      debugPrint('Error deleting manufacturer: $e');
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update a model name and device type
  Future<bool> updateModel(
    String manufacturer,
    String oldName,
    String newName,
    {String? deviceType}
  ) async {
    if (manufacturer.isEmpty || oldName.isEmpty || newName.isEmpty) return false;
    if (oldName == newName && deviceType == null) return true; // No change needed
    if (!_manufacturers.contains(manufacturer)) return false; // Manufacturer doesn't exist

    final models = _modelsByManufacturer[manufacturer] ?? [];
    if (!models.contains(oldName)) return false; // Model doesn't exist
    if (models.contains(newName) && oldName != newName) return false; // New name already exists

    _setLoading(true);
    _clearError();

    try {
      // Get the model document
      final modelQuery = await _firestore.collection('models')
          .where('manufacturer', isEqualTo: manufacturer)
          .where('name', isEqualTo: oldName)
          .limit(1)
          .get();

      if (modelQuery.docs.isEmpty) {
        _setError('Model not found');
        return false;
      }

      // Update the model in Firestore
      final modelDoc = modelQuery.docs.first;
      final updateData = <String, dynamic>{
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (oldName != newName) {
        updateData['name'] = newName;
      }

      if (deviceType != null) {
        updateData['deviceType'] = deviceType;
      }

      await modelDoc.reference.update(updateData);

      // Update local list
      if (oldName != newName) {
        _modelsByManufacturer[manufacturer]!.remove(oldName);
        _modelsByManufacturer[manufacturer]!.add(newName);
        _modelsByManufacturer[manufacturer]!.sort();
      }

      // Update device type
      if (deviceType != null) {
        final oldKey = '${manufacturer}_$oldName';
        _modelDeviceTypes.remove(oldKey);

        final newKey = '${manufacturer}_$newName';
        _modelDeviceTypes[newKey] = deviceType;
      } else if (oldName != newName) {
        // Just update the key if only the name changed
        final oldKey = '${manufacturer}_$oldName';
        final newKey = '${manufacturer}_$newName';
        final oldType = _modelDeviceTypes[oldKey];
        if (oldType != null) {
          _modelDeviceTypes[newKey] = oldType;
          _modelDeviceTypes.remove(oldKey);
        }
      }

      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
      return true;
    } catch (e) {
      debugPrint('Error updating model: $e');
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete a model
  Future<bool> deleteModel(String manufacturer, String name) async {
    if (manufacturer.isEmpty || name.isEmpty) return false;
    if (!_manufacturers.contains(manufacturer)) return false; // Manufacturer doesn't exist

    final models = _modelsByManufacturer[manufacturer] ?? [];
    if (!models.contains(name)) return false; // Model doesn't exist

    _setLoading(true);
    _clearError();

    try {
      // Get the model document
      final modelQuery = await _firestore.collection('models')
          .where('manufacturer', isEqualTo: manufacturer)
          .where('name', isEqualTo: name)
          .limit(1)
          .get();

      if (modelQuery.docs.isEmpty) {
        _setError('Model not found');
        return false;
      }

      // Delete the model from Firestore
      final modelDoc = modelQuery.docs.first;
      await modelDoc.reference.delete();

      // Update local list
      _modelsByManufacturer[manufacturer]!.remove(name);

      // Remove device type
      final key = '${manufacturer}_$name';
      _modelDeviceTypes.remove(key);

      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
      return true;
    } catch (e) {
      debugPrint('Error deleting model: $e');
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Helper methods
  void _setLoading(bool value) {
    if (_isLoading != value) {
      _isLoading = value;
      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }

  void _setError(String error) {
    if (_error != error) {
      _error = error;
      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }

  void _clearError() {
    if (_error != null) {
      _error = null;
      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }
}
