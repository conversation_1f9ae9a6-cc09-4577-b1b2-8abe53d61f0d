<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Hm Device Errors</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>hm_device_errors</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>

	<!-- Camera permission -->
	<key>NSCameraUsageDescription</key>
	<string>This app needs access to camera to capture photos for device error documentation.</string>

	<!-- Photo Library permission -->
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app needs access to photo library to select images for device error documentation.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>This app needs access to save photos to your photo library.</string>

	<!-- Microphone permission -->
	<key>NSMicrophoneUsageDescription</key>
	<string>This app needs access to microphone to record audio for device error documentation.</string>

	<!-- Location permissions -->
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app needs access to location to tag device errors with location information.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>This app needs access to location to tag device errors with location information.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>This app needs access to location to tag device errors with location information.</string>

	<!-- File access permissions -->
	<key>NSDocumentsFolderUsageDescription</key>
	<string>This app needs access to documents to attach files to device error reports.</string>
	<key>NSDownloadsFolderUsageDescription</key>
	<string>This app needs access to downloads folder to save and access files.</string>

	<!-- Network permissions -->
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>

	<!-- Background modes -->
	<key>UIBackgroundModes</key>
	<array>
		<string>background-fetch</string>
		<string>background-processing</string>
		<string>remote-notification</string>
	</array>
</dict>
</plist>
