import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';

class PlaceholderImageCreator {
  /// Create a simple placeholder image
  static Future<void> createPlaceholderImage() async {
    try {
      // Create a simple 100x100 gray rectangle
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);

      // Draw gray background
      final paint = Paint()..color = Colors.grey.shade300;
      canvas.drawRect(const Rect.fromLTWH(0, 0, 100, 100), paint);

      // Draw border
      final borderPaint = Paint()
        ..color = Colors.grey.shade600
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      canvas.drawRect(const Rect.fromLTWH(0, 0, 100, 100), borderPaint);

      // Draw text "No Image"
      final textPainter = TextPainter(
        text: const TextSpan(
          text: 'No\nImage',
          style: TextStyle(
            color: Colors.black54,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          (100 - textPainter.width) / 2,
          (100 - textPainter.height) / 2,
        ),
      );

      final picture = recorder.endRecording();
      final image = await picture.toImage(100, 100);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

      if (byteData != null) {
        final file = File('assets/images/placeholder.png');
        await file.create(recursive: true);
        await file.writeAsBytes(byteData.buffer.asUint8List());
        debugPrint('Placeholder image created successfully');
      }
    } catch (e) {
      debugPrint('Error creating placeholder image: $e');
    }
  }

  /// Create a simple PNG header for a 1x1 transparent pixel
  static Uint8List createSimplePNG() {
    // Simple 1x1 transparent PNG
    return Uint8List.fromList([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
      0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
      0x49, 0x48, 0x44, 0x52, // IHDR
      0x00, 0x00, 0x00, 0x01, // Width: 1
      0x00, 0x00, 0x00, 0x01, // Height: 1
      0x08, 0x06, 0x00, 0x00, 0x00, // Bit depth: 8, Color type: 6 (RGBA), Compression: 0, Filter: 0, Interlace: 0
      0x1F, 0x15, 0xC4, 0x89, // CRC
      0x00, 0x00, 0x00, 0x0A, // IDAT chunk length
      0x49, 0x44, 0x41, 0x54, // IDAT
      0x78, 0x9C, 0x62, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, // Compressed data
      0xE2, 0x21, 0xBC, 0x33, // CRC
      0x00, 0x00, 0x00, 0x00, // IEND chunk length
      0x49, 0x45, 0x4E, 0x44, // IEND
      0xAE, 0x42, 0x60, 0x82  // CRC
    ]);
  }
}
