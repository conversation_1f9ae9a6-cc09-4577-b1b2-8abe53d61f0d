# Flutter Launcher Icons Configuration
# This file can be used with the flutter_launcher_icons package
# to automatically generate app icons from a source image.

flutter_icons:
  android: true
  ios: true
  web:
    generate: true
    background_color: "#9C27B0"
    theme_color: "#E91E63"
  windows:
    generate: true
  macos:
    generate: true
  
  # Use the vector drawable for Android
  image_path_android: "android/app/src/main/res/drawable/app_logo.xml"
  
  # For other platforms, you would need a high-resolution PNG
  # image_path: "assets/icon/app_icon.png"
  
  # Adaptive icon configuration for Android
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "android/app/src/main/res/drawable/app_logo.xml"
  
  # Platform-specific configurations
  android_min_sdk_android: 21
  
  # Remove old icons before generating new ones
  remove_alpha_ios: true
