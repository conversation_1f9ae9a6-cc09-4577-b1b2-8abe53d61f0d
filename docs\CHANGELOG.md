# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.1] - 2024-12-29

### Fixed
- ✅ **Individual Attachment Deletion**: Fixed issue where deleting one attachment would remove all attachments
  - Updated `_performRemoveAttachment` method in `attachment_picker_widget.dart`
  - Added proper cloud storage deletion using `CloudUploadService.deleteFile()`
  - Ensured only the selected attachment is removed from local list
  - Added detailed logging for debugging attachment deletion process

- ✅ **Full Screen Error Edit**: Converted error edit dialog to full screen for better user experience
  - Created new `ErrorEditScreen` in `lib/screens/admin/error_edit_screen.dart`
  - Professional UI design with organized sections (device info, description, solution, attachments)
  - Added app bar with save button and upload progress indicators
  - Automatic list refresh when returning from edit screen

- ✅ **Flutter Lifecycle Channel**: Resolved flutter/lifecycle channel warning messages
  - Added proper message handler configuration in `main.dart`
  - Configured `ServicesBinding.instance.defaultBinaryMessenger.setMessageHandler`

- ✅ **Google Drive Images in Browser**: Fixed CORS issues when displaying Google Drive images in web browser
  - Added platform-specific handling using `kIsWeb` check
  - For web: Show clickable placeholder to open images in new tab
  - Added `_openImageInNewTab()` function with `url_launcher`
  - Improved user experience with clear instructions

- ✅ **BuildContext Async Usage**: Fixed BuildContext usage across async gaps
  - Updated `_showAddErrorDialog` method to capture provider reference before async operations
  - Added proper `mounted` checks to prevent context usage after widget disposal

### Improved
- **Error Handling**: Enhanced error handling with clearer user messages
- **Upload Prevention**: Added mechanism to prevent saving during file uploads
- **Progress Indicators**: Added visual feedback during upload and save operations
- **Code Quality**: Removed unused imports and parameters
- **Logging**: Added detailed logging for debugging and monitoring

### Technical Details
- **Files Modified**:
  - `lib/widgets/attachment_picker_widget.dart`
  - `lib/screens/admin/error_edit_screen.dart` (new)
  - `lib/screens/admin/errors_management_screen.dart`
  - `lib/widgets/google_drive_image_widget.dart`
  - `lib/main.dart`
  - `pubspec.yaml`
  - `android/app/build.gradle.kts`
  - `lib/services/app_update_service.dart`
  - `windows/runner/Runner.rc`

- **Dependencies**: No new dependencies added
- **Breaking Changes**: None
- **Migration**: No migration required

## [1.0.0] - 2024-12-28

### Added
- Initial release of HM Device Errors application
- Complete device error management system
- Firebase integration for data storage
- Google Drive cloud storage support
- Multi-language support (Arabic/English)
- User authentication and authorization
- Professional UI with Material Design 3
- Attachment management (images, videos, documents)
- Excel import/export functionality
- Admin panel for system management
- Real-time notifications
- Offline support with local caching
- Security features with encryption
- Performance optimizations
