# دليل الإشعارات التلقائية FCM - HM Device Errors

## نظرة عامة

تم تطوير نظام شامل للإشعارات التلقائية باستخدام Firebase Cloud Messaging (FCM) يتيح إرسال إشعارات تلقائية لجميع المستخدمين أو مجموعات محددة.

## 🎯 الميزات الرئيسية

### 1. **الإشعارات التلقائية**
- إشعارات عند إضافة أخطاء جديدة
- إشعارات التحديثات والتطوير
- ملخص يومي وأسبوعي
- إشعارات الصيانة والتنبيهات الأمنية

### 2. **الإشعارات المخصصة**
- إرسال إشعارات مخصصة لجميع المستخدمين
- إرسال إشعارات لمجموعات محددة (مديرين، مستخدمين نشطين، إلخ)
- إشعارات طوارئ عالية الأولوية

### 3. **الإدارة المتقدمة**
- لوحة تحكم شاملة للإعدادات
- إحصائيات مفصلة عن الإشعارات
- إعدادات قابلة للتخصيص لكل نوع إشعار

## 🚀 كيفية التفعيل والاستخدام

### للمديرين:

#### 1. **الوصول لإدارة الإشعارات**
```
التطبيق → لوحة الإدارة → إدارة الإشعارات التلقائية
```

#### 2. **تفعيل الإشعارات التلقائية**
1. افتح شاشة إدارة الإشعارات
2. فعّل "تفعيل الإشعارات التلقائية"
3. اختر أنواع الإشعارات المطلوبة:
   - ✅ إشعارات الأخطاء الجديدة
   - ✅ إشعارات التحديثات
   - ✅ الإشعارات الدورية
   - ✅ إشعارات الصيانة
   - ✅ التنبيهات الأمنية

#### 3. **إرسال إشعار مخصص**
1. اذهب إلى قسم "إشعار مخصص"
2. املأ الحقول:
   - العنوان (عربي وإنجليزي)
   - المحتوى (عربي وإنجليزي)
3. اضغط "إرسال لجميع المستخدمين"

#### 4. **الإجراءات السريعة**
- **ملخص يومي**: إرسال ملخص فوري للأنشطة
- **إشعار تجريبي**: اختبار النظام
- **للمديرين**: إرسال رسالة خاصة للمديرين
- **للمستخدمين النشطين**: إرسال رسالة للمستخدمين الفعالين

### للمطورين:

#### 1. **إعداد Cloud Functions**
```bash
# تثبيت Firebase CLI
npm install -g firebase-tools

# تسجيل الدخول
firebase login

# نشر Cloud Functions
cd cloud_functions
npm install
firebase deploy --only functions
```

#### 2. **استخدام الخدمة في الكود**
```dart
// تهيئة الخدمة
await AutoNotificationService.instance.initialize();

// إرسال إشعار مخصص
await AutoNotificationService.instance.sendCustomNotificationToAll(
  titleAr: 'عنوان عربي',
  titleEn: 'English Title',
  bodyAr: 'محتوى عربي',
  bodyEn: 'English Content',
  priority: 'high',
);

// إرسال إشعار لمجموعة
await AutoNotificationService.instance.sendNotificationToGroup(
  groupType: 'admins',
  titleAr: 'رسالة للمديرين',
  titleEn: 'Message for Admins',
  bodyAr: 'محتوى الرسالة',
  bodyEn: 'Message content',
);
```

## 📊 أنواع الإشعارات

### 1. **الإشعارات التلقائية**

#### إشعارات الأخطاء الجديدة
- **المحفز**: إضافة خطأ جديد في قاعدة البيانات
- **التوقيت**: فوري (خلال 5 دقائق من الإضافة)
- **المستلمون**: جميع المستخدمين
- **المحتوى**: تفاصيل الخطأ (الشركة، الموديل، كود الخطأ)

#### إشعارات التحديثات
- **المحفز**: إضافة تحديث جديد للتطبيق
- **التوقيت**: فوري
- **المستلمون**: جميع المستخدمين
- **الأولوية**: عادية أو حرجة (للتحديثات الإجبارية)

#### الملخص اليومي
- **المحفز**: مجدول (6 مساءً يومياً)
- **التوقيت**: تلقائي
- **المستلمون**: جميع المستخدمين
- **المحتوى**: عدد الأخطاء المضافة اليوم

### 2. **الإشعارات المخصصة**

#### إشعارات الطوارئ
```dart
// استخدام Cloud Function للطوارئ
final result = await FirebaseFunctions.instance
    .httpsCallable('sendEmergencyBroadcast')
    .call({
  'title_ar': 'إشعار طوارئ',
  'title_en': 'Emergency Alert',
  'body_ar': 'رسالة طوارئ مهمة',
  'body_en': 'Important emergency message',
});
```

#### إشعارات الصيانة
```dart
await AutoNotificationService.instance.sendMaintenanceNotification(
  startTime: DateTime.now().add(Duration(hours: 2)),
  endTime: DateTime.now().add(Duration(hours: 4)),
  reasonAr: 'صيانة دورية للخوادم',
  reasonEn: 'Routine server maintenance',
);
```

### 3. **إشعارات المجموعات**

#### المجموعات المتاحة:
- `admins`: المديرين فقط
- `active_users`: المستخدمين النشطين
- `expired_users`: المستخدمين منتهي الصلاحية
- `premium_users`: المستخدمين المميزين

## ⚙️ الإعدادات المتقدمة

### إعدادات الإشعارات الدورية
```dart
final settings = AutoNotificationSettings(
  isEnabled: true,
  enablePeriodicNotifications: true,
  periodicIntervalHours: 24, // كل 24 ساعة
  notifyOnNewErrors: true,
  notifyOnUpdates: true,
  notifyOnMaintenance: true,
  notifyOnSecurityAlerts: true,
);

await AutoNotificationService.instance.updateSettings(settings);
```

### إعدادات الأولوية
- **normal**: إشعارات عادية
- **high**: إشعارات مهمة (لون برتقالي)
- **critical**: إشعارات حرجة (لون أحمر، أولوية قصوى)

## 📱 إعدادات المنصات

### Android
```xml
<!-- في AndroidManifest.xml -->
<service
    android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
    android:exported="false">
    <intent-filter>
        <action android:name="com.google.firebase.MESSAGING_EVENT" />
    </intent-filter>
</service>
```

### iOS
```xml
<!-- في Info.plist -->
<key>FirebaseMessagingAutoInitEnabled</key>
<true/>
```

### Web
```javascript
// في firebase-messaging-sw.js
messaging.onBackgroundMessage(function(payload) {
  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    icon: '/favicon.png',
    badge: '/favicon.png',
  };
  
  self.registration.showNotification(notificationTitle, notificationOptions);
});
```

## 📈 مراقبة الأداء

### إحصائيات متاحة:
- عدد الإشعارات المرسلة (24 ساعة، 7 أيام)
- نسبة الإشعارات التلقائية مقابل اليدوية
- معدل نجاح التسليم
- إحصائيات المجموعات المستهدفة

### مراقبة الأخطاء:
```dart
// الحصول على إحصائيات
final stats = await AutoNotificationService.instance.getNotificationStats();
print('إشعارات آخر 24 ساعة: ${stats['last_24_hours']}');
print('إشعارات تلقائية: ${stats['auto_generated_last_7_days']}');
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. **الإشعارات لا تصل**
- تحقق من صحة FCM tokens
- تأكد من تفعيل الإذونات
- راجع Cloud Functions logs

#### 2. **الإشعارات التلقائية لا تعمل**
- تحقق من إعدادات الخدمة
- تأكد من تشغيل Cloud Functions
- راجع Firestore triggers

#### 3. **مشاكل في المجموعات**
- تحقق من صحة user roles في Firestore
- تأكد من وجود FCM tokens للمستخدمين

### أوامر التشخيص:
```bash
# فحص Cloud Functions
firebase functions:log

# فحص Firestore rules
firebase firestore:rules:get

# اختبار الإشعارات
firebase functions:shell
```

## 🔐 الأمان

### إعدادات الأمان:
- التحقق من صحة المدير قبل إرسال إشعارات الطوارئ
- تشفير البيانات الحساسة
- تسجيل جميع العمليات للمراجعة

### قواعد Firestore:
```javascript
// قواعد الأمان للإشعارات
match /notifications/{notificationId} {
  allow read: if request.auth != null;
  allow write: if request.auth != null && 
    (resource == null || request.auth.token.admin == true);
}
```

## 📞 الدعم الفني

### للحصول على المساعدة:
1. راجع logs التطبيق
2. تحقق من Firebase Console
3. استخدم أدوات التشخيص المدمجة
4. راجع إحصائيات الإشعارات

### معلومات مفيدة للدعم:
- إصدار التطبيق
- نوع المنصة (Android/iOS/Web)
- رسائل الخطأ
- إعدادات الإشعارات الحالية

النظام جاهز للاستخدام ويوفر حلاً شاملاً للإشعارات التلقائية! 🎉
