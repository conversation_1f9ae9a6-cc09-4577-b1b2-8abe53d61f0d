import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/locale_provider.dart';
import '../../providers/permission_provider.dart';
import '../../providers/activity_log_provider.dart';
import '../../models/permission_model.dart' as perm;
import '../../models/activity_log_model.dart';


class PermissionsReportsScreen extends StatefulWidget {
  const PermissionsReportsScreen({super.key});

  @override
  State<PermissionsReportsScreen> createState() => _PermissionsReportsScreenState();
}

class _PermissionsReportsScreenState extends State<PermissionsReportsScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  Map<String, dynamic> _stats = {};
  List<ActivityLog> _recentActivities = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final permissionProvider = Provider.of<PermissionProvider>(context, listen: false);
      final activityLogProvider = Provider.of<ActivityLogProvider>(context, listen: false);

      // Load roles and statistics
      await permissionProvider.loadRoles();
      final roleStats = await permissionProvider.getRoleUsageStats();
      final activityStats = await activityLogProvider.getActivityStatistics();
      // Get recent activities of different types
      final recentActivities = <ActivityLog>[];
      try {
        final roleActivities = await activityLogProvider.getActivitiesByType(
          ActivityType.roleCreated,
          limit: 5,
        );
        final userActivities = await activityLogProvider.getActivitiesByType(
          ActivityType.userRoleAssigned,
          limit: 5,
        );
        recentActivities.addAll(roleActivities);
        recentActivities.addAll(userActivities);

        // Sort by timestamp
        recentActivities.sort((a, b) => b.timestamp.compareTo(a.timestamp));

        // Take only the most recent 10
        if (recentActivities.length > 10) {
          recentActivities.removeRange(10, recentActivities.length);
        }
      } catch (e) {
        debugPrint('Error loading recent activities: $e');
      }

      setState(() {
        _stats = {
          'roleStats': roleStats,
          'activityStats': activityStats,
        };
        _recentActivities = recentActivities;
      });
    } catch (e) {
      debugPrint('Error loading reports data: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isRTL ? 'تقارير الصلاحيات' : 'Permissions Reports',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: isRTL ? 'تحديث' : 'Refresh',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(
              icon: const Icon(Icons.pie_chart),
              text: isRTL ? 'الأدوار' : 'Roles',
            ),
            Tab(
              icon: const Icon(Icons.people),
              text: isRTL ? 'المستخدمين' : 'Users',
            ),
            Tab(
              icon: const Icon(Icons.timeline),
              text: isRTL ? 'الأنشطة' : 'Activities',
            ),
            Tab(
              icon: const Icon(Icons.analytics),
              text: isRTL ? 'الإحصائيات' : 'Statistics',
            ),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildRoleUsageReport(isRTL),
                _buildUserPermissionsReport(isRTL),
                _buildActivityReport(isRTL),
                _buildStatisticsReport(isRTL),
              ],
            ),
    );
  }

  Widget _buildRoleUsageReport(bool isRTL) {
    return Consumer<PermissionProvider>(
      builder: (context, permissionProvider, child) {
        final roles = permissionProvider.roles;
        final roleStats = _stats['roleStats'] as Map<String, int>? ?? {};

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Summary Card
              Card(
                elevation: 4,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.admin_panel_settings,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            isRTL ? 'ملخص الأدوار' : 'Roles Summary',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: _buildStatCard(
                              isRTL ? 'إجمالي الأدوار' : 'Total Roles',
                              roles.length.toString(),
                              Icons.security,
                              Colors.blue,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: _buildStatCard(
                              isRTL ? 'أدوار النظام' : 'System Roles',
                              roles.where((r) => r.isSystemRole).length.toString(),
                              Icons.settings,
                              Colors.green,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: _buildStatCard(
                              isRTL ? 'أدوار مخصصة' : 'Custom Roles',
                              roles.where((r) => !r.isSystemRole).length.toString(),
                              Icons.tune,
                              Colors.orange,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Role Usage Details
              Card(
                elevation: 4,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isRTL ? 'استخدام الأدوار' : 'Role Usage',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ...roles.map((role) {
                        final userCount = roleStats[role.id] ?? 0;
                        return _buildRoleUsageItem(role, userCount, isRTL);
                      }),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildUserPermissionsReport(bool isRTL) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.people,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        isRTL ? 'صلاحيات المستخدمين' : 'User Permissions',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    isRTL
                        ? 'تقرير مفصل عن صلاحيات جميع المستخدمين في النظام'
                        : 'Detailed report of all user permissions in the system',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurface.withAlpha(178),
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.file_download),
                    label: Text(isRTL ? 'تصدير التقرير' : 'Export Report'),
                    onPressed: () => _exportUserPermissionsReport(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityReport(bool isRTL) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.timeline,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        isRTL ? 'سجل الأنشطة' : 'Activity Log',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  if (_recentActivities.isEmpty)
                    Center(
                      child: Text(
                        isRTL ? 'لا توجد أنشطة حديثة' : 'No recent activities',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface.withAlpha(178),
                        ),
                      ),
                    )
                  else
                    ..._recentActivities.map((activity) {
                      return _buildActivityItem(activity, isRTL);
                    }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsReport(bool isRTL) {
    final activityStats = _stats['activityStats'] as Map<String, dynamic>? ?? {};
    final totalActivities = activityStats['totalActivities'] ?? 0;
    final typeCount = activityStats['typeCount'] as Map<String, dynamic>? ?? {};

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Overall Statistics
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.analytics,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        isRTL ? 'الإحصائيات العامة' : 'Overall Statistics',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildStatCard(
                    isRTL ? 'إجمالي الأنشطة' : 'Total Activities',
                    totalActivities.toString(),
                    Icons.analytics,
                    Colors.purple,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Activity Types Breakdown
          if (typeCount.isNotEmpty)
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isRTL ? 'تفصيل الأنشطة' : 'Activity Breakdown',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ...typeCount.entries.map((entry) {
                      return _buildActivityTypeItem(entry.key, entry.value, isRTL);
                    }),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withAlpha(25),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withAlpha(51)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRoleUsageItem(perm.UserRole role, int userCount, bool isRTL) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).colorScheme.outline.withAlpha(51)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: role.isSystemRole
                  ? Theme.of(context).colorScheme.primary.withAlpha(25)
                  : Theme.of(context).colorScheme.secondary.withAlpha(25),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              role.isSystemRole ? Icons.security : Icons.admin_panel_settings,
              color: role.isSystemRole
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.secondary,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isRTL ? role.nameAr : role.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  isRTL ? role.descriptionAr : role.description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurface.withAlpha(178),
                  ),
                ),
              ],
            ),
          ),
          Column(
            children: [
              Text(
                userCount.toString(),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              Text(
                isRTL ? 'مستخدم' : 'users',
                style: TextStyle(
                  fontSize: 10,
                  color: Theme.of(context).colorScheme.onSurface.withAlpha(178),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem(ActivityLog activity, bool isRTL) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).colorScheme.outline.withAlpha(51)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: activity.getTypeColor().withAlpha(25),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              activity.getTypeIcon(),
              color: activity.getTypeColor(),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity.getDisplayDescription(isRTL),
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '${isRTL ? 'بواسطة' : 'by'} ${activity.performedByName}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurface.withAlpha(178),
                  ),
                ),
              ],
            ),
          ),
          Text(
            activity.getFormattedTimestamp(isRTL),
            style: TextStyle(
              fontSize: 10,
              color: Theme.of(context).colorScheme.onSurface.withAlpha(178),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityTypeItem(String type, int count, bool isRTL) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).colorScheme.outline.withAlpha(51)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              _getActivityTypeDisplayName(type, isRTL),
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ),
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  String _getActivityTypeDisplayName(String type, bool isRTL) {
    switch (type) {
      case 'ActivityType.roleCreated':
        return isRTL ? 'إنشاء الأدوار' : 'Role Creation';
      case 'ActivityType.roleUpdated':
        return isRTL ? 'تحديث الأدوار' : 'Role Updates';
      case 'ActivityType.roleDeleted':
        return isRTL ? 'حذف الأدوار' : 'Role Deletion';
      case 'ActivityType.userRoleAssigned':
        return isRTL ? 'تعيين الأدوار' : 'Role Assignment';
      case 'ActivityType.permissionGranted':
        return isRTL ? 'منح الصلاحيات' : 'Permission Granted';
      case 'ActivityType.permissionRevoked':
        return isRTL ? 'سحب الصلاحيات' : 'Permission Revoked';
      default:
        return type.split('.').last;
    }
  }

  Future<void> _exportUserPermissionsReport() async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isRTL ? 'ستتم إضافة ميزة التصدير قريباً' : 'Export feature coming soon',
        ),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
