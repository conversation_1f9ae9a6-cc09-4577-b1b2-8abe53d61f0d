import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/device_category_model.dart';
import '../models/device_error_model.dart';
import '../models/category_model.dart' as cat;
import '../models/user_model.dart';

class FirestoreService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Device Categories
  Future<List<DeviceCategory>> getDeviceCategories() async {
    try {
      final snapshot = await _firestore.collection('deviceCategories').get();
      return snapshot.docs
          .map((doc) => DeviceCategory.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('Error getting device categories: $e');
      return [];
    }
  }

  Future<DeviceCategory?> getDeviceCategory(String categoryId) async {
    try {
      final doc = await _firestore.collection('deviceCategories').doc(categoryId).get();
      if (doc.exists) {
        return DeviceCategory.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting device category: $e');
      return null;
    }
  }

  Future<String> addDeviceCategory(DeviceCategory category) async {
    try {
      final docRef = await _firestore.collection('deviceCategories').add(category.toMap());
      return docRef.id;
    } catch (e) {
      debugPrint('Error adding device category: $e');
      rethrow;
    }
  }

  Future<void> updateDeviceCategory(DeviceCategory category) async {
    try {
      await _firestore
          .collection('deviceCategories')
          .doc(category.id)
          .update(category.toMap());
    } catch (e) {
      debugPrint('Error updating device category: $e');
      rethrow;
    }
  }

  Future<void> deleteDeviceCategory(String categoryId) async {
    try {
      // First check if there are any devices in this category
      final devicesSnapshot = await _firestore
          .collection('faults')
          .where('categoryId', isEqualTo: categoryId)
          .get();

      if (devicesSnapshot.docs.isNotEmpty) {
        throw Exception('Cannot delete category with existing devices');
      }

      await _firestore.collection('deviceCategories').doc(categoryId).delete();
    } catch (e) {
      debugPrint('Error deleting device category: $e');
      rethrow;
    }
  }

  // Device Errors (Faults)
  Future<List<DeviceError>> getDeviceErrors({
    String? categoryId,
    String? manufacturer,
    String? model,
    String? errorCode,
  }) async {
    try {
      Query query = _firestore.collection('faults');
      bool hasFilters = false;

      // Only apply filters if they are provided
      if (categoryId != null && categoryId.isNotEmpty) {
        query = query.where('categoryId', isEqualTo: categoryId);
        hasFilters = true;
      }
      if (manufacturer != null && manufacturer.isNotEmpty) {
        query = query.where('manufacturer', isEqualTo: manufacturer);
        hasFilters = true;
      }
      if (model != null && model.isNotEmpty) {
        query = query.where('model', isEqualTo: model);
        hasFilters = true;
      }
      if (errorCode != null && errorCode.isNotEmpty) {
        query = query.where('errorCode', isEqualTo: errorCode);
        hasFilters = true;
      }

      // Only add orderBy if we don't have complex filters to avoid index issues
      // For complex queries, we'll sort in memory
      if (!hasFilters || (categoryId != null && manufacturer == null && model == null && errorCode == null)) {
        query = query.orderBy('createdAt', descending: true);
      }

      final snapshot = await query.get();
      List<DeviceError> errors = snapshot.docs
          .map((doc) => DeviceError.fromFirestore(doc))
          .toList();

      // If we couldn't sort in the query due to index limitations, sort in memory
      if (hasFilters && !(categoryId != null && manufacturer == null && model == null && errorCode == null)) {
        errors.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      }

      return errors;
    } catch (e) {
      debugPrint('Error getting device errors: $e');
      // If there's an index error, try a simpler query
      if (e.toString().contains('index') || e.toString().contains('failed-precondition')) {
        return await _getDeviceErrorsSimple(categoryId: categoryId);
      }
      return [];
    }
  }

  // Simplified query method for when complex queries fail
  Future<List<DeviceError>> _getDeviceErrorsSimple({String? categoryId}) async {
    try {
      Query query = _firestore.collection('faults');

      // Only filter by category if provided
      if (categoryId != null && categoryId.isNotEmpty) {
        query = query.where('categoryId', isEqualTo: categoryId);
        query = query.orderBy('createdAt', descending: true);
      } else {
        // For all errors, just get them without ordering to avoid index issues
        // We'll sort in memory
      }

      final snapshot = await query.get();
      List<DeviceError> errors = snapshot.docs
          .map((doc) => DeviceError.fromFirestore(doc))
          .toList();

      // Sort in memory if we couldn't sort in query
      if (categoryId == null || categoryId.isEmpty) {
        errors.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      }

      return errors;
    } catch (e) {
      debugPrint('Error in simple device errors query: $e');
      return [];
    }
  }

  Future<DeviceError?> getDeviceError(String errorId) async {
    try {
      final doc = await _firestore.collection('faults').doc(errorId).get();
      if (doc.exists) {
        return DeviceError.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting device error: $e');
      return null;
    }
  }

  Future<String> addDeviceError(DeviceError error) async {
    try {
      final docRef = await _firestore.collection('faults').add(error.toMap());

      // Update device count in category
      await _updateCategoryDeviceCount(error.categoryId, 1);

      return docRef.id;
    } catch (e) {
      debugPrint('Error adding device error: $e');
      rethrow;
    }
  }

  Future<void> updateDeviceError(DeviceError error, String? oldCategoryId) async {
    try {
      await _firestore
          .collection('faults')
          .doc(error.id)
          .update(error.toMap());

      // If category changed, update device counts
      if (oldCategoryId != null && oldCategoryId != error.categoryId) {
        await _updateCategoryDeviceCount(oldCategoryId, -1);
        await _updateCategoryDeviceCount(error.categoryId, 1);
      }
    } catch (e) {
      debugPrint('Error updating device error: $e');
      rethrow;
    }
  }

  Future<void> deleteDeviceError(DeviceError error) async {
    try {
      await _firestore.collection('faults').doc(error.id).delete();

      // Update device count in category
      await _updateCategoryDeviceCount(error.categoryId, -1);
    } catch (e) {
      debugPrint('Error deleting device error: $e');
      rethrow;
    }
  }

  // Helper method to update device count in category
  Future<void> _updateCategoryDeviceCount(String categoryId, int change) async {
    try {
      final categoryDoc = await _firestore.collection('deviceCategories').doc(categoryId).get();
      if (categoryDoc.exists) {
        final category = DeviceCategory.fromFirestore(categoryDoc);
        final newCount = category.deviceCount + change;
        await _firestore.collection('deviceCategories').doc(categoryId).update({
          'deviceCount': newCount < 0 ? 0 : newCount,
        });
      }
    } catch (e) {
      debugPrint('Error updating category device count: $e');
    }
  }

  // Search functionality
  Future<List<DeviceError>> searchDeviceErrors(String query) async {
    try {
      // This is a simple search implementation
      // For more advanced search, consider using Algolia or ElasticSearch
      final snapshot = await _firestore.collection('faults').get();
      final allErrors = snapshot.docs
          .map((doc) => DeviceError.fromFirestore(doc))
          .toList();

      // Filter errors based on query
      return allErrors.where((error) {
        final searchableText =
            '${error.manufacturer} ${error.model} ${error.errorCode} ${error.description} ${error.solution}'.toLowerCase();
        return searchableText.contains(query.toLowerCase());
      }).toList();
    } catch (e) {
      debugPrint('Error searching device errors: $e');
      return [];
    }
  }

  // Get unique manufacturers
  Future<List<String>> getUniqueManufacturers() async {
    try {
      // In the updated schema, we should use the manufacturers collection
      final snapshot = await _firestore.collection('manufacturers').get();
      final manufacturers = snapshot.docs.map((doc) {
        final data = doc.data();
        return data['name'] as String;
      }).toList();

      manufacturers.sort();
      return manufacturers;
    } catch (e) {
      // Fallback to the old method if the manufacturers collection doesn't exist
      try {
        final snapshot = await _firestore.collection('faults').get();
        final allErrors = snapshot.docs
            .map((doc) => DeviceError.fromFirestore(doc))
            .toList();

        final manufacturers = allErrors.map((e) => e.manufacturer).toSet().toList();
        manufacturers.sort();
        return manufacturers;
      } catch (e2) {
        debugPrint('Error getting unique manufacturers: $e2');
        return [];
      }
    }
  }

  // Get unique models for a manufacturer
  Future<List<String>> getUniqueModels(String manufacturer) async {
    try {
      // First try to get from models collection
      try {
        final snapshot = await _firestore
            .collection('models')
            .where('manufacturer', isEqualTo: manufacturer)
            .get();

        final models = snapshot.docs.map((doc) {
          final data = doc.data();
          return data['name'] as String;
        }).toList();

        models.sort();
        return models;
      } catch (e) {
        // Fallback to the old method
        final snapshot = await _firestore
            .collection('faults')
            .where('manufacturer', isEqualTo: manufacturer)
            .get();

        final allErrors = snapshot.docs
            .map((doc) => DeviceError.fromFirestore(doc))
            .toList();

        final models = allErrors.map((e) => e.model).toSet().toList();
        models.sort();
        return models;
      }
    } catch (e) {
      debugPrint('Error getting unique models: $e');
      return [];
    }
  }

  // Favorites functionality
  Future<void> updateDeviceErrorFavorites(String errorId, List<String> favoriteUsers) async {
    try {
      await _firestore
          .collection('faults')
          .doc(errorId)
          .update({'favoriteUsers': favoriteUsers});
    } catch (e) {
      debugPrint('Error updating device error favorites: $e');
      rethrow;
    }
  }

  Future<List<DeviceError>> getFavoriteErrors(String userId) async {
    try {
      // We need to use array-contains query to find errors where the user is in favoriteUsers
      final snapshot = await _firestore
          .collection('faults')
          .where('favoriteUsers', arrayContains: userId)
          .get();

      return snapshot.docs
          .map((doc) => DeviceError.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('Error getting favorite errors: $e');
      return [];
    }
  }

  // Categories (using Category model)
  Future<List<cat.Category>> getCategories() async {
    try {
      final snapshot = await _firestore.collection('categories').get();
      return snapshot.docs
          .map((doc) => cat.Category.fromMap(doc.data(), doc.id))
          .toList();
    } catch (e) {
      debugPrint('Error getting categories: $e');
      return [];
    }
  }

  Future<String> addCategory(cat.Category category) async {
    try {
      final docRef = await _firestore.collection('categories').add(category.toMap());
      return docRef.id;
    } catch (e) {
      debugPrint('Error adding category: $e');
      rethrow;
    }
  }

  // Manufacturers
  Future<List<String>> getManufacturers() async {
    try {
      final snapshot = await _firestore.collection('manufacturers').get();
      return snapshot.docs.map((doc) => doc.data()['name'] as String).toList();
    } catch (e) {
      debugPrint('Error getting manufacturers: $e');
      return [];
    }
  }

  Future<void> addManufacturer(String name) async {
    try {
      await _firestore.collection('manufacturers').add({'name': name});
    } catch (e) {
      debugPrint('Error adding manufacturer: $e');
      rethrow;
    }
  }

  Future<List<String>> getModelsForManufacturer(String manufacturer) async {
    try {
      final snapshot = await _firestore
          .collection('models')
          .where('manufacturer', isEqualTo: manufacturer)
          .get();
      return snapshot.docs.map((doc) => doc.data()['name'] as String).toList();
    } catch (e) {
      debugPrint('Error getting models for manufacturer: $e');
      return [];
    }
  }

  Future<void> addModel(String manufacturer, String modelName) async {
    try {
      await _firestore.collection('models').add({
        'manufacturer': manufacturer,
        'name': modelName,
      });
    } catch (e) {
      debugPrint('Error adding model: $e');
      rethrow;
    }
  }

  // Users
  Future<List<UserModel>> getUsers() async {
    try {
      final snapshot = await _firestore.collection('users').get();
      return snapshot.docs
          .map((doc) => UserModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      debugPrint('Error getting users: $e');
      return [];
    }
  }

  Future<void> addUser(UserModel user) async {
    try {
      await _firestore.collection('users').doc(user.id).set(user.toMap());
    } catch (e) {
      debugPrint('Error adding user: $e');
      rethrow;
    }
  }

  // Clear data methods (for restore)
  Future<void> clearAllErrors() async {
    try {
      final snapshot = await _firestore.collection('faults').get();
      final batch = _firestore.batch();
      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }
      await batch.commit();
    } catch (e) {
      debugPrint('Error clearing all errors: $e');
      rethrow;
    }
  }

  Future<void> clearAllCategories() async {
    try {
      final snapshot = await _firestore.collection('categories').get();
      final batch = _firestore.batch();
      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }
      await batch.commit();
    } catch (e) {
      debugPrint('Error clearing all categories: $e');
      rethrow;
    }
  }

  Future<void> clearAllManufacturers() async {
    try {
      // Clear manufacturers
      final manufacturersSnapshot = await _firestore.collection('manufacturers').get();
      final batch1 = _firestore.batch();
      for (final doc in manufacturersSnapshot.docs) {
        batch1.delete(doc.reference);
      }
      await batch1.commit();

      // Clear models
      final modelsSnapshot = await _firestore.collection('models').get();
      final batch2 = _firestore.batch();
      for (final doc in modelsSnapshot.docs) {
        batch2.delete(doc.reference);
      }
      await batch2.commit();
    } catch (e) {
      debugPrint('Error clearing all manufacturers: $e');
      rethrow;
    }
  }
}
