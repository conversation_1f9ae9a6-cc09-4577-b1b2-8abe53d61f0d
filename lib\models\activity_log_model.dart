import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

enum ActivityType {
  // Role Management
  roleCreated,
  roleUpdated,
  roleDeleted,

  // User Role Assignment
  userRoleAssigned,
  userRoleChanged,
  userRoleRemoved,

  // Permission Management
  permissionGranted,
  permissionRevoked,
  customPermissionAdded,
  customPermissionRemoved,

  // User Management
  userCreated,
  userUpdated,
  userDeleted,
  userActivated,
  userDeactivated,

  // System Activities
  systemBackup,
  systemRestore,
  dataExport,
  dataImport,

  // Authentication
  userLogin,
  userLogout,
  passwordChanged,
  passwordReset,
}

class ActivityLog {
  final String id;
  final ActivityType type;
  final String performedBy; // User ID who performed the action
  final String performedByName; // User name for display
  final String? targetUserId; // User ID affected by the action (if applicable)
  final String? targetUserName; // User name affected by the action
  final String? roleId; // Role ID involved (if applicable)
  final String? roleName; // Role name for display
  final String? permissionType; // Permission type involved (if applicable)
  final Map<String, dynamic> details; // Additional details about the action
  final String? description; // Human-readable description
  final String? descriptionAr; // Arabic description
  final DateTime timestamp;
  final String? ipAddress;
  final String? deviceInfo;
  final String? location;

  const ActivityLog({
    required this.id,
    required this.type,
    required this.performedBy,
    required this.performedByName,
    this.targetUserId,
    this.targetUserName,
    this.roleId,
    this.roleName,
    this.permissionType,
    this.details = const {},
    this.description,
    this.descriptionAr,
    required this.timestamp,
    this.ipAddress,
    this.deviceInfo,
    this.location,
  });

  factory ActivityLog.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ActivityLog(
      id: doc.id,
      type: ActivityType.values.firstWhere(
        (type) => type.toString() == data['type'],
        orElse: () => ActivityType.userLogin,
      ),
      performedBy: data['performedBy'] ?? '',
      performedByName: data['performedByName'] ?? '',
      targetUserId: data['targetUserId'],
      targetUserName: data['targetUserName'],
      roleId: data['roleId'],
      roleName: data['roleName'],
      permissionType: data['permissionType'],
      details: Map<String, dynamic>.from(data['details'] ?? {}),
      description: data['description'],
      descriptionAr: data['descriptionAr'],
      timestamp: (data['timestamp'] as Timestamp).toDate(),
      ipAddress: data['ipAddress'],
      deviceInfo: data['deviceInfo'],
      location: data['location'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'type': type.toString(),
      'performedBy': performedBy,
      'performedByName': performedByName,
      'targetUserId': targetUserId,
      'targetUserName': targetUserName,
      'roleId': roleId,
      'roleName': roleName,
      'permissionType': permissionType,
      'details': details,
      'description': description,
      'descriptionAr': descriptionAr,
      'timestamp': Timestamp.fromDate(timestamp),
      'ipAddress': ipAddress,
      'deviceInfo': deviceInfo,
      'location': location,
    };
  }

  ActivityLog copyWith({
    String? id,
    ActivityType? type,
    String? performedBy,
    String? performedByName,
    String? targetUserId,
    String? targetUserName,
    String? roleId,
    String? roleName,
    String? permissionType,
    Map<String, dynamic>? details,
    String? description,
    String? descriptionAr,
    DateTime? timestamp,
    String? ipAddress,
    String? deviceInfo,
    String? location,
  }) {
    return ActivityLog(
      id: id ?? this.id,
      type: type ?? this.type,
      performedBy: performedBy ?? this.performedBy,
      performedByName: performedByName ?? this.performedByName,
      targetUserId: targetUserId ?? this.targetUserId,
      targetUserName: targetUserName ?? this.targetUserName,
      roleId: roleId ?? this.roleId,
      roleName: roleName ?? this.roleName,
      permissionType: permissionType ?? this.permissionType,
      details: details ?? this.details,
      description: description ?? this.description,
      descriptionAr: descriptionAr ?? this.descriptionAr,
      timestamp: timestamp ?? this.timestamp,
      ipAddress: ipAddress ?? this.ipAddress,
      deviceInfo: deviceInfo ?? this.deviceInfo,
      location: location ?? this.location,
    );
  }

  // Helper methods for UI display
  IconData getTypeIcon() {
    switch (type) {
      case ActivityType.roleCreated:
      case ActivityType.roleUpdated:
      case ActivityType.roleDeleted:
        return Icons.admin_panel_settings;
      case ActivityType.userRoleAssigned:
      case ActivityType.userRoleChanged:
      case ActivityType.userRoleRemoved:
        return Icons.person_add;
      case ActivityType.permissionGranted:
      case ActivityType.permissionRevoked:
      case ActivityType.customPermissionAdded:
      case ActivityType.customPermissionRemoved:
        return Icons.security;
      case ActivityType.userCreated:
      case ActivityType.userUpdated:
      case ActivityType.userDeleted:
      case ActivityType.userActivated:
      case ActivityType.userDeactivated:
        return Icons.person;
      case ActivityType.systemBackup:
        return Icons.backup;
      case ActivityType.systemRestore:
        return Icons.restore;
      case ActivityType.dataExport:
        return Icons.file_download;
      case ActivityType.dataImport:
        return Icons.file_upload;
      case ActivityType.userLogin:
        return Icons.login;
      case ActivityType.userLogout:
        return Icons.logout;
      case ActivityType.passwordChanged:
      case ActivityType.passwordReset:
        return Icons.lock;
    }
  }

  Color getTypeColor() {
    switch (type) {
      case ActivityType.roleCreated:
      case ActivityType.userCreated:
      case ActivityType.systemBackup:
      case ActivityType.userActivated:
        return Colors.green;
      case ActivityType.roleUpdated:
      case ActivityType.userUpdated:
      case ActivityType.userRoleChanged:
        return Colors.blue;
      case ActivityType.roleDeleted:
      case ActivityType.userDeleted:
      case ActivityType.permissionRevoked:
      case ActivityType.userRoleRemoved:
      case ActivityType.customPermissionRemoved:
      case ActivityType.userDeactivated:
        return Colors.red;
      case ActivityType.userRoleAssigned:
      case ActivityType.permissionGranted:
      case ActivityType.customPermissionAdded:
        return Colors.orange;
      case ActivityType.userLogin:
        return Colors.teal;
      case ActivityType.userLogout:
        return Colors.grey;
      case ActivityType.passwordChanged:
      case ActivityType.passwordReset:
        return Colors.purple;
      case ActivityType.dataExport:
      case ActivityType.dataImport:
        return Colors.indigo;
      case ActivityType.systemRestore:
        return Colors.amber;
    }
  }

  String getDisplayDescription(bool isArabic) {
    if (description != null && descriptionAr != null) {
      return isArabic ? descriptionAr! : description!;
    }

    return ActivityDescriptions.getDescription(
      type,
      isArabic,
      targetUserName: targetUserName,
      roleName: roleName,
      permissionType: permissionType,
      details: details,
    );
  }

  String getFormattedTimestamp(bool isArabic) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return isArabic ? 'الآن' : 'Now';
    } else if (difference.inMinutes < 60) {
      return isArabic
          ? 'منذ ${difference.inMinutes} دقيقة'
          : '${difference.inMinutes} minutes ago';
    } else if (difference.inHours < 24) {
      return isArabic
          ? 'منذ ${difference.inHours} ساعة'
          : '${difference.inHours} hours ago';
    } else if (difference.inDays < 7) {
      return isArabic
          ? 'منذ ${difference.inDays} يوم'
          : '${difference.inDays} days ago';
    } else {
      return isArabic
          ? '${timestamp.day}/${timestamp.month}/${timestamp.year}'
          : '${timestamp.month}/${timestamp.day}/${timestamp.year}';
    }
  }
}

// Helper class for activity descriptions
class ActivityDescriptions {
  static String getDescription(ActivityType type, bool isArabic, {
    String? targetUserName,
    String? roleName,
    String? permissionType,
    Map<String, dynamic>? details,
  }) {
    switch (type) {
      case ActivityType.roleCreated:
        return isArabic
            ? 'تم إنشاء دور جديد: ${roleName ?? 'غير محدد'}'
            : 'Created new role: ${roleName ?? 'Unknown'}';

      case ActivityType.roleUpdated:
        return isArabic
            ? 'تم تحديث الدور: ${roleName ?? 'غير محدد'}'
            : 'Updated role: ${roleName ?? 'Unknown'}';

      case ActivityType.roleDeleted:
        return isArabic
            ? 'تم حذف الدور: ${roleName ?? 'غير محدد'}'
            : 'Deleted role: ${roleName ?? 'Unknown'}';

      case ActivityType.userRoleAssigned:
        return isArabic
            ? 'تم تعيين الدور "${roleName ?? 'غير محدد'}" للمستخدم ${targetUserName ?? 'غير محدد'}'
            : 'Assigned role "${roleName ?? 'Unknown'}" to user ${targetUserName ?? 'Unknown'}';

      case ActivityType.userRoleChanged:
        return isArabic
            ? 'تم تغيير دور المستخدم ${targetUserName ?? 'غير محدد'} إلى "${roleName ?? 'غير محدد'}"'
            : 'Changed user ${targetUserName ?? 'Unknown'} role to "${roleName ?? 'Unknown'}"';

      case ActivityType.userRoleRemoved:
        return isArabic
            ? 'تم إزالة الدور من المستخدم ${targetUserName ?? 'غير محدد'}'
            : 'Removed role from user ${targetUserName ?? 'Unknown'}';

      case ActivityType.permissionGranted:
        return isArabic
            ? 'تم منح الصلاحية "${permissionType ?? 'غير محدد'}" للمستخدم ${targetUserName ?? 'غير محدد'}'
            : 'Granted permission "${permissionType ?? 'Unknown'}" to user ${targetUserName ?? 'Unknown'}';

      case ActivityType.permissionRevoked:
        return isArabic
            ? 'تم إلغاء الصلاحية "${permissionType ?? 'غير محدد'}" من المستخدم ${targetUserName ?? 'غير محدد'}'
            : 'Revoked permission "${permissionType ?? 'Unknown'}" from user ${targetUserName ?? 'Unknown'}';

      case ActivityType.customPermissionAdded:
        return isArabic
            ? 'تم إضافة صلاحية مخصصة "${permissionType ?? 'غير محدد'}" للمستخدم ${targetUserName ?? 'غير محدد'}'
            : 'Added custom permission "${permissionType ?? 'Unknown'}" to user ${targetUserName ?? 'Unknown'}';

      case ActivityType.customPermissionRemoved:
        return isArabic
            ? 'تم إزالة الصلاحية المخصصة "${permissionType ?? 'غير محدد'}" من المستخدم ${targetUserName ?? 'غير محدد'}'
            : 'Removed custom permission "${permissionType ?? 'Unknown'}" from user ${targetUserName ?? 'Unknown'}';

      case ActivityType.userCreated:
        return isArabic
            ? 'تم إنشاء مستخدم جديد: ${targetUserName ?? 'غير محدد'}'
            : 'Created new user: ${targetUserName ?? 'Unknown'}';

      case ActivityType.userUpdated:
        return isArabic
            ? 'تم تحديث بيانات المستخدم: ${targetUserName ?? 'غير محدد'}'
            : 'Updated user: ${targetUserName ?? 'Unknown'}';

      case ActivityType.userDeleted:
        return isArabic
            ? 'تم حذف المستخدم: ${targetUserName ?? 'غير محدد'}'
            : 'Deleted user: ${targetUserName ?? 'Unknown'}';

      case ActivityType.userActivated:
        return isArabic
            ? 'تم تفعيل المستخدم: ${targetUserName ?? 'غير محدد'}'
            : 'Activated user: ${targetUserName ?? 'Unknown'}';

      case ActivityType.userDeactivated:
        return isArabic
            ? 'تم إلغاء تفعيل المستخدم: ${targetUserName ?? 'غير محدد'}'
            : 'Deactivated user: ${targetUserName ?? 'Unknown'}';

      case ActivityType.systemBackup:
        return isArabic
            ? 'تم إنشاء نسخة احتياطية من النظام'
            : 'Created system backup';

      case ActivityType.systemRestore:
        return isArabic
            ? 'تم استعادة النظام من النسخة الاحتياطية'
            : 'Restored system from backup';

      case ActivityType.dataExport:
        return isArabic
            ? 'تم تصدير البيانات'
            : 'Exported data';

      case ActivityType.dataImport:
        return isArabic
            ? 'تم استيراد البيانات'
            : 'Imported data';

      case ActivityType.userLogin:
        return isArabic
            ? 'تم تسجيل الدخول'
            : 'User logged in';

      case ActivityType.userLogout:
        return isArabic
            ? 'تم تسجيل الخروج'
            : 'User logged out';

      case ActivityType.passwordChanged:
        return isArabic
            ? 'تم تغيير كلمة المرور'
            : 'Password changed';

      case ActivityType.passwordReset:
        return isArabic
            ? 'تم إعادة تعيين كلمة المرور للمستخدم ${targetUserName ?? 'غير محدد'}'
            : 'Reset password for user ${targetUserName ?? 'Unknown'}';
    }
  }

  static String getActivityTypeDisplayName(ActivityType type, bool isArabic) {
    switch (type) {
      case ActivityType.roleCreated:
        return isArabic ? 'إنشاء دور' : 'Role Created';
      case ActivityType.roleUpdated:
        return isArabic ? 'تحديث دور' : 'Role Updated';
      case ActivityType.roleDeleted:
        return isArabic ? 'حذف دور' : 'Role Deleted';
      case ActivityType.userRoleAssigned:
        return isArabic ? 'تعيين دور' : 'Role Assigned';
      case ActivityType.userRoleChanged:
        return isArabic ? 'تغيير دور' : 'Role Changed';
      case ActivityType.userRoleRemoved:
        return isArabic ? 'إزالة دور' : 'Role Removed';
      case ActivityType.permissionGranted:
        return isArabic ? 'منح صلاحية' : 'Permission Granted';
      case ActivityType.permissionRevoked:
        return isArabic ? 'إلغاء صلاحية' : 'Permission Revoked';
      case ActivityType.customPermissionAdded:
        return isArabic ? 'إضافة صلاحية مخصصة' : 'Custom Permission Added';
      case ActivityType.customPermissionRemoved:
        return isArabic ? 'إزالة صلاحية مخصصة' : 'Custom Permission Removed';
      case ActivityType.userCreated:
        return isArabic ? 'إنشاء مستخدم' : 'User Created';
      case ActivityType.userUpdated:
        return isArabic ? 'تحديث مستخدم' : 'User Updated';
      case ActivityType.userDeleted:
        return isArabic ? 'حذف مستخدم' : 'User Deleted';
      case ActivityType.userActivated:
        return isArabic ? 'تفعيل مستخدم' : 'User Activated';
      case ActivityType.userDeactivated:
        return isArabic ? 'إلغاء تفعيل مستخدم' : 'User Deactivated';
      case ActivityType.systemBackup:
        return isArabic ? 'نسخ احتياطي' : 'System Backup';
      case ActivityType.systemRestore:
        return isArabic ? 'استعادة النظام' : 'System Restore';
      case ActivityType.dataExport:
        return isArabic ? 'تصدير البيانات' : 'Data Export';
      case ActivityType.dataImport:
        return isArabic ? 'استيراد البيانات' : 'Data Import';
      case ActivityType.userLogin:
        return isArabic ? 'تسجيل دخول' : 'User Login';
      case ActivityType.userLogout:
        return isArabic ? 'تسجيل خروج' : 'User Logout';
      case ActivityType.passwordChanged:
        return isArabic ? 'تغيير كلمة المرور' : 'Password Changed';
      case ActivityType.passwordReset:
        return isArabic ? 'إعادة تعيين كلمة المرور' : 'Password Reset';
    }
  }
}
