import 'package:flutter/foundation.dart';
import '../services/app_update_service.dart';

class UpdateProvider extends ChangeNotifier {
  UpdateInfo? _updateInfo;
  bool _isChecking = false;
  String? _error;
  DateTime? _lastCheckTime;

  // Getters
  UpdateInfo? get updateInfo => _updateInfo;
  bool get isChecking => _isChecking;
  String? get error => _error;
  DateTime? get lastCheckTime => _lastCheckTime;
  bool get hasUpdate => _updateInfo != null && _updateInfo!.type != UpdateType.maintenance;
  bool get isUpdateRequired => _updateInfo?.isRequired ?? false;
  bool get isMaintenanceMode => _updateInfo?.type == UpdateType.maintenance;

  /// Check for updates
  Future<void> checkForUpdates({bool forceCheck = false, bool showLoading = true}) async {
    if (_isChecking) return;

    if (showLoading) {
      _setLoading(true);
    }
    _clearError();

    try {
      final updateInfo = await AppUpdateService.checkForUpdates(forceCheck: forceCheck);

      _updateInfo = updateInfo;
      _lastCheckTime = DateTime.now();

      // Log the check event
      await AppUpdateService.logUpdateEvent('update_check', {
        'force_check': forceCheck,
        'has_update': updateInfo != null,
        'update_type': updateInfo?.type.toString(),
        'current_version': updateInfo?.currentVersion,
        'latest_version': updateInfo?.latestVersion,
      });

      // Only log completion if there's an update or it's a forced check
      if (updateInfo != null || forceCheck) {
        debugPrint('Update check completed: ${updateInfo != null ? 'Update available' : 'No update'}');
      }
    } catch (e) {
      _setError(e.toString());
      debugPrint('Error checking for updates: $e');
    } finally {
      if (showLoading) {
        _setLoading(false);
      }
    }
  }

  /// Launch update
  Future<bool> launchUpdate() async {
    if (_updateInfo?.updateUrl == null) {
      _setError('Update URL not available');
      return false;
    }

    _clearError();

    try {
      final success = await AppUpdateService.launchUpdate(_updateInfo!.updateUrl!);

      // Log the launch event
      await AppUpdateService.logUpdateEvent('update_launched', {
        'update_url': _updateInfo!.updateUrl,
        'update_type': _updateInfo!.type.toString(),
        'success': success,
      });

      if (!success) {
        _setError('Failed to launch update');
      }

      return success;
    } catch (e) {
      _setError(e.toString());
      debugPrint('Error launching update: $e');
      return false;
    }
  }

  /// Skip current version
  Future<void> skipVersion() async {
    if (_updateInfo?.latestVersion == null) return;

    try {
      await AppUpdateService.skipVersion(_updateInfo!.latestVersion!);

      // Log the skip event
      await AppUpdateService.logUpdateEvent('update_skipped', {
        'skipped_version': _updateInfo!.latestVersion,
        'update_type': _updateInfo!.type.toString(),
      });

      // Clear the update info since user skipped it
      _updateInfo = null;
      notifyListeners();

      debugPrint('Version ${_updateInfo!.latestVersion} skipped');
    } catch (e) {
      _setError(e.toString());
      debugPrint('Error skipping version: $e');
    }
  }

  /// Dismiss update (for optional updates)
  void dismissUpdate() {
    if (_updateInfo?.isRequired == true) {
      debugPrint('Cannot dismiss required update');
      return;
    }

    _updateInfo = null;
    notifyListeners();
  }

  /// Clear skipped version
  Future<void> clearSkippedVersion() async {
    try {
      await AppUpdateService.clearSkippedVersion();
      debugPrint('Cleared skipped version');
    } catch (e) {
      _setError(e.toString());
      debugPrint('Error clearing skipped version: $e');
    }
  }

  /// Get app information
  Future<Map<String, dynamic>> getAppInfo() async {
    try {
      return await AppUpdateService.getAppInfo();
    } catch (e) {
      debugPrint('Error getting app info: $e');
      return {};
    }
  }

  /// Get update statistics
  Future<Map<String, dynamic>> getUpdateStatistics() async {
    try {
      return await AppUpdateService.getUpdateStatistics();
    } catch (e) {
      debugPrint('Error getting update statistics: $e');
      return {};
    }
  }

  /// Schedule automatic update checks
  void scheduleAutomaticChecks() {
    // Check for updates every 12 hours
    Future.delayed(const Duration(hours: 12), () {
      if (!_isChecking) {
        checkForUpdates(showLoading: false);
        scheduleAutomaticChecks(); // Schedule next check
      }
    });
  }

  /// Initialize update provider
  Future<void> initialize() async {
    try {
      await AppUpdateService.initialize();

      // Perform initial update check
      await checkForUpdates(showLoading: false);

      // Schedule automatic checks
      scheduleAutomaticChecks();

      debugPrint('Update provider initialized successfully');
    } catch (e) {
      debugPrint('Error initializing update provider: $e');
      _setError(e.toString());
    }
  }

  /// Force refresh update info
  Future<void> refresh() async {
    await checkForUpdates(forceCheck: true);
  }

  /// Check if should show update dialog
  bool shouldShowUpdateDialog() {
    if (_updateInfo == null) return false;

    // Always show for required updates and maintenance
    if (_updateInfo!.isRequired || _updateInfo!.type == UpdateType.maintenance) {
      return true;
    }

    // Show for optional updates (user can dismiss)
    return _updateInfo!.type == UpdateType.optional;
  }

  /// Get update priority
  UpdatePriority getUpdatePriority() {
    if (_updateInfo == null) return UpdatePriority.none;

    switch (_updateInfo!.type) {
      case UpdateType.required:
        return UpdatePriority.critical;
      case UpdateType.maintenance:
        return UpdatePriority.critical;
      case UpdateType.optional:
        return UpdatePriority.normal;
    }
  }

  /// Private helper methods
  void _setLoading(bool loading) {
    _isChecking = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }


}

/// Update priority levels
enum UpdatePriority {
  none,
  normal,
  critical,
}
