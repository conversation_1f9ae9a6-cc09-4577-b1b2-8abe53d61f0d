@echo off
echo Copying logo files from Downloads folder...
echo.

set SOURCE_DIR=C:\Users\<USER>\Downloads\ic_launcher
set PROJECT_DIR=%~dp0..

REM Check if source directory exists
if not exist "%SOURCE_DIR%" (
    echo Error: Source directory not found: %SOURCE_DIR%
    echo Please make sure the ic_launcher folder exists in your Downloads.
    pause
    exit /b 1
)

echo Source directory: %SOURCE_DIR%
echo Project directory: %PROJECT_DIR%
echo.

REM List files in source directory
echo Files in source directory:
dir "%SOURCE_DIR%" /b
echo.

REM Copy Android launcher icons
echo Copying Android launcher icons...
if exist "%SOURCE_DIR%\mipmap-mdpi" (
    xcopy "%SOURCE_DIR%\mipmap-mdpi\*" "%PROJECT_DIR%\android\app\src\main\res\mipmap-mdpi\" /Y /I
    echo - Copied mipmap-mdpi
)

if exist "%SOURCE_DIR%\mipmap-hdpi" (
    xcopy "%SOURCE_DIR%\mipmap-hdpi\*" "%PROJECT_DIR%\android\app\src\main\res\mipmap-hdpi\" /Y /I
    echo - Copied mipmap-hdpi
)

if exist "%SOURCE_DIR%\mipmap-xhdpi" (
    xcopy "%SOURCE_DIR%\mipmap-xhdpi\*" "%PROJECT_DIR%\android\app\src\main\res\mipmap-xhdpi\" /Y /I
    echo - Copied mipmap-xhdpi
)

if exist "%SOURCE_DIR%\mipmap-xxhdpi" (
    xcopy "%SOURCE_DIR%\mipmap-xxhdpi\*" "%PROJECT_DIR%\android\app\src\main\res\mipmap-xxhdpi\" /Y /I
    echo - Copied mipmap-xxhdpi
)

if exist "%SOURCE_DIR%\mipmap-xxxhdpi" (
    xcopy "%SOURCE_DIR%\mipmap-xxxhdpi\*" "%PROJECT_DIR%\android\app\src\main\res\mipmap-xxxhdpi\" /Y /I
    echo - Copied mipmap-xxxhdpi
)

REM Copy drawable files
echo.
echo Copying drawable files...
if exist "%SOURCE_DIR%\drawable" (
    xcopy "%SOURCE_DIR%\drawable\*" "%PROJECT_DIR%\android\app\src\main\res\drawable\" /Y /I
    echo - Copied drawable files
)

REM Copy iOS icons
echo.
echo Copying iOS icons...
if exist "%SOURCE_DIR%\ios" (
    xcopy "%SOURCE_DIR%\ios\*" "%PROJECT_DIR%\ios\Runner\Assets.xcassets\AppIcon.appiconset\" /Y /I
    echo - Copied iOS icons
)

REM Copy web icons
echo.
echo Copying web icons...
if exist "%SOURCE_DIR%\web" (
    xcopy "%SOURCE_DIR%\web\*" "%PROJECT_DIR%\web\icons\" /Y /I
    echo - Copied web icons
)

if exist "%SOURCE_DIR%\favicon.png" (
    copy "%SOURCE_DIR%\favicon.png" "%PROJECT_DIR%\web\favicon.png" /Y
    echo - Copied favicon
)

REM Copy macOS icons
echo.
echo Copying macOS icons...
if exist "%SOURCE_DIR%\macos" (
    xcopy "%SOURCE_DIR%\macos\*" "%PROJECT_DIR%\macos\Runner\Assets.xcassets\AppIcon.appiconset\" /Y /I
    echo - Copied macOS icons
)

echo.
echo ✅ Logo files copied successfully!
echo.
echo Next steps:
echo 1. Clean and rebuild your Flutter project
echo 2. Test the app to verify new icons appear correctly
echo.
echo Commands to run:
echo   flutter clean
echo   flutter pub get
echo   flutter build apk
echo.
pause
