# HM Device Errors

A comprehensive Flutter application for tracking and managing device errors with multi-language support and cloud integration.

## 🚀 Quick Start

```bash
# Clone the repository
git clone <repository-url>
cd hm_device_errors

# Install dependencies
flutter pub get

# Run the application
flutter run
```

## 📚 Documentation

All project documentation is located in the [`docs/`](./docs/) directory:

- **[Complete Documentation](./docs/COMPLETE_PROJECT_DOCUMENTATION.md)** - Full project overview
- **[Setup Guide](./docs/GOOGLE_DRIVE_REMOTE_CONFIG.md)** - Google Drive integration
- **[Changelog](./docs/CHANGELOG.md)** - Version history
- **[Release Notes](./docs/RELEASE_NOTES_v1.0.1.md)** - Latest release details

👉 **[View All Documentation](./docs/README.md)**

## ✨ Features

- 🌍 **Multi-language Support** (Arabic/English)
- 🔐 **User Authentication** with role-based access
- 📱 **Device Error Tracking** with detailed categorization
- 📎 **File Attachments** with cloud storage
- ⚙️ **Admin Panel** for system management
- 🔄 **Remote Configuration** via Firebase
- 📊 **Analytics and Reporting**
- 🌐 **Offline Support** with data synchronization

## 🛠️ Tech Stack

- **Flutter/Dart** - Cross-platform development
- **Firebase** - Backend services
- **Google Drive API** - Cloud storage
- **Provider** - State management

## 📱 Platforms

- ✅ Android (Primary)
- ✅ iOS (Secondary)
- ✅ Windows (Development)
- 🔄 Web (Limited)

## 🔧 Development

### Prerequisites
- Flutter SDK (latest stable)
- Firebase project
- Google Cloud Platform account
- Android Studio / VS Code

### Setup
1. Follow the [Complete Documentation](./docs/COMPLETE_PROJECT_DOCUMENTATION.md)
2. Configure [Google Drive Integration](./docs/GOOGLE_DRIVE_REMOTE_CONFIG.md)
3. Run `flutter pub get`
4. Run `flutter run`

## 📄 License

Proprietary software. All rights reserved.

---

For detailed documentation, please visit the [`docs/`](./docs/) directory.
