package com.mohamedrady.hmdeviceerrors

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private val SIGNATURE_CHANNEL = "com.mohamedrady.hmdeviceerrors/signature"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Register the security plugin with proper method channel
        try {
            val securityPlugin = SecurityPlugin()
            MethodChannel(flutterEngine.dartExecutor.binaryMessenger, SIGNATURE_CHANNEL)
                .setMethodCallHandler(securityPlugin)
        } catch (e: Exception) {
            println("Error registering SecurityPlugin: ${e.message}")
        }
    }
}
