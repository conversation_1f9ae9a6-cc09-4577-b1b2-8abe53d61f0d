import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/update_provider.dart';
import '../../providers/locale_provider.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/update_dialog.dart';
import 'app_update_management_screen.dart';

class UpdateManagementScreen extends StatefulWidget {
  const UpdateManagementScreen({super.key});

  @override
  State<UpdateManagementScreen> createState() => _UpdateManagementScreenState();
}

class _UpdateManagementScreenState extends State<UpdateManagementScreen> {
  Map<String, dynamic> _appInfo = {};
  Map<String, dynamic> _updateStats = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final updateProvider = Provider.of<UpdateProvider>(context, listen: false);

      final appInfo = await updateProvider.getAppInfo();
      final updateStats = await updateProvider.getUpdateStatistics();

      setState(() {
        _appInfo = appInfo;
        _updateStats = updateStats;
      });
    } catch (e) {
      debugPrint('Error loading update data: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;
    final authProvider = Provider.of<AuthProvider>(context);

    // Check if user is admin
    if (!authProvider.isAdmin) {
      return Scaffold(
        appBar: AppBar(
          title: Text(
            isRTL ? 'إدارة التحديثات' : 'Update Management',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
          elevation: 4,
          iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
          centerTitle: true,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.lock,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                isRTL ? 'غير مصرح لك بالوصول' : 'Access Denied',
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                isRTL
                  ? 'هذه الصفحة متاحة للمديرين فقط'
                  : 'This page is only available to administrators',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isRTL ? 'إدارة التحديثات' : 'Update Management',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const AppUpdateManagementScreen(),
              ),
            ),
            icon: const Icon(Icons.settings),
            tooltip: isRTL ? 'إدارة التكوين البعيد' : 'Remote Config Management',
          ),
          IconButton(
            onPressed: _loadData,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadData,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildCurrentVersionCard(isRTL),
                    const SizedBox(height: 16),
                    _buildUpdateStatusCard(isRTL),
                    const SizedBox(height: 16),
                    _buildUpdateActionsCard(isRTL),
                    const SizedBox(height: 16),
                    _buildUpdateStatisticsCard(isRTL),
                    const SizedBox(height: 16),
                    _buildRemoteConfigCard(isRTL),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildCurrentVersionCard(bool isRTL) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Colors.blue[600]),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'معلومات التطبيق' : 'App Information',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              isRTL ? 'اسم التطبيق:' : 'App Name:',
              _appInfo['app_name'] ?? 'N/A',
              isRTL,
            ),
            _buildInfoRow(
              isRTL ? 'الإصدار:' : 'Version:',
              _appInfo['version'] ?? 'N/A',
              isRTL,
            ),
            _buildInfoRow(
              isRTL ? 'رقم البناء:' : 'Build Number:',
              _appInfo['build_number'] ?? 'N/A',
              isRTL,
            ),
            _buildInfoRow(
              isRTL ? 'اسم الحزمة:' : 'Package Name:',
              _appInfo['package_name'] ?? 'N/A',
              isRTL,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUpdateStatusCard(bool isRTL) {
    return Consumer<UpdateProvider>(
      builder: (context, updateProvider, child) {
        final hasUpdate = updateProvider.hasUpdate;
        final updateInfo = updateProvider.updateInfo;

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      hasUpdate ? Icons.update : Icons.check_circle,
                      color: hasUpdate ? Colors.orange[600] : Colors.green[600],
                    ),
                    const SizedBox(width: 8),
                    Text(
                      isRTL ? 'حالة التحديث' : 'Update Status',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (hasUpdate && updateInfo != null) ...[
                  UpdateNotificationWidget(
                    updateInfo: updateInfo,
                    onTap: () => UpdateDialog.show(context, updateInfo),
                  ),
                ] else ...[
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.green[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green[200]!),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.check_circle, color: Colors.green[600]),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            isRTL
                              ? 'التطبيق محدث إلى أحدث إصدار'
                              : 'App is up to date',
                            style: TextStyle(
                              color: Colors.green[800],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                if (updateProvider.lastCheckTime != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    isRTL
                      ? 'آخر فحص: ${_formatDateTime(updateProvider.lastCheckTime!)}'
                      : 'Last check: ${_formatDateTime(updateProvider.lastCheckTime!)}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildUpdateActionsCard(bool isRTL) {
    return Consumer<UpdateProvider>(
      builder: (context, updateProvider, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.settings, color: Colors.blue[600]),
                    const SizedBox(width: 8),
                    Text(
                      isRTL ? 'إجراءات التحديث' : 'Update Actions',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: updateProvider.isChecking
                            ? null
                            : () => updateProvider.checkForUpdates(forceCheck: true),
                        icon: updateProvider.isChecking
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Icon(Icons.refresh),
                        label: Text(isRTL ? 'فحص التحديثات' : 'Check Updates'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => updateProvider.clearSkippedVersion(),
                        icon: const Icon(Icons.clear),
                        label: Text(isRTL ? 'مسح التخطي' : 'Clear Skip'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey[600],
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildUpdateStatisticsCard(bool isRTL) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: Colors.purple[600]),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'إحصائيات التحديث' : 'Update Statistics',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_updateStats.isNotEmpty) ...[
              _buildInfoRow(
                isRTL ? 'إجمالي الأحداث:' : 'Total Events:',
                '${_updateStats['total_events'] ?? 0}',
                isRTL,
              ),
              if (_updateStats['event_breakdown'] != null) ...[
                const SizedBox(height: 8),
                Text(
                  isRTL ? 'تفصيل الأحداث:' : 'Event Breakdown:',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 8),
                ...(_updateStats['event_breakdown'] as Map<String, dynamic>)
                    .entries
                    .map((entry) => Padding(
                          padding: const EdgeInsets.only(left: 16, bottom: 4),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(entry.key),
                              Text('${entry.value}'),
                            ],
                          ),
                        )),
              ],
            ] else ...[
              Text(
                isRTL ? 'لا توجد إحصائيات متاحة' : 'No statistics available',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRemoteConfigCard(bool isRTL) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.cloud, color: Colors.green[600]),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'التكوين البعيد' : 'Remote Configuration',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              isRTL
                ? 'يمكن إدارة إعدادات التحديث من خلال Firebase Remote Config'
                : 'Update settings can be managed through Firebase Remote Config',
              style: TextStyle(color: Colors.grey[700]),
            ),
            const SizedBox(height: 12),
            ElevatedButton.icon(
              onPressed: () => _showRemoteConfigInfo(isRTL),
              icon: const Icon(Icons.info),
              label: Text(isRTL ? 'عرض المعلومات' : 'View Info'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, bool isRTL) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Flexible(
            child: Text(
              value,
              style: TextStyle(color: Colors.grey[700]),
              textAlign: isRTL ? TextAlign.left : TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showRemoteConfigInfo(bool isRTL) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'إعدادات Remote Config' : 'Remote Config Settings'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                isRTL ? 'المفاتيح المطلوبة:' : 'Required Keys:',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ...[
                'latest_version',
                'minimum_version',
                'update_required',
                'update_url_android',
                'update_url_ios',
                'update_enabled',
                'maintenance_mode',
              ].map((key) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Text('• $key'),
                  )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'موافق' : 'OK'),
          ),
        ],
      ),
    );
  }
}
