import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;
import '../models/attachment_model.dart';

/// Enhanced attachment model that includes file data
class EnhancedAttachmentModel extends AttachmentModel {
  final Uint8List? fileData;

  EnhancedAttachmentModel({
    required super.id,
    required super.fileName,
    required super.originalFileName,
    required super.type,
    required super.url,
    super.thumbnailUrl,
    required super.fileSize,
    required super.mimeType,
    required super.uploadedAt,
    required super.uploadedBy,
    super.metadata,
    this.fileData,
  });

  @override
  EnhancedAttachmentModel copyWith({
    String? id,
    String? fileName,
    String? originalFileName,
    AttachmentType? type,
    String? url,
    String? thumbnailUrl,
    int? fileSize,
    String? mimeType,
    DateTime? uploadedAt,
    String? uploadedBy,
    Map<String, dynamic>? metadata,
    Uint8List? fileData,
  }) {
    return EnhancedAttachmentModel(
      id: id ?? this.id,
      fileName: fileName ?? this.fileName,
      originalFileName: originalFileName ?? this.originalFileName,
      type: type ?? this.type,
      url: url ?? this.url,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      fileSize: fileSize ?? this.fileSize,
      mimeType: mimeType ?? this.mimeType,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      uploadedBy: uploadedBy ?? this.uploadedBy,
      metadata: metadata ?? this.metadata,
      fileData: fileData ?? this.fileData,
    );
  }
}

/// Enhanced service for handling file attachments with file data
class EnhancedAttachmentService {
  static final ImagePicker _imagePicker = ImagePicker();

  /// Pick images from gallery or camera with file data
  static Future<List<EnhancedAttachmentModel>> pickImages({
    bool allowMultiple = true,
    ImageSource source = ImageSource.gallery,
  }) async {
    try {
      final List<EnhancedAttachmentModel> attachments = [];

      if (allowMultiple && source == ImageSource.gallery) {
        // Use pickMultipleMedia for multiple images
        final List<XFile> images = await _imagePicker.pickMultipleMedia(
          maxWidth: 1920,
          maxHeight: 1080,
          imageQuality: 85,
        );

        for (final image in images) {
          final attachment = await _createEnhancedAttachmentFromXFile(
            image,
            AttachmentType.image,
          );
          if (attachment != null) {
            attachments.add(attachment);
          }
        }
      } else {
        final XFile? image = await _imagePicker.pickImage(
          source: source,
          maxWidth: 1920,
          maxHeight: 1080,
          imageQuality: 85,
        );

        if (image != null) {
          final attachment = await _createEnhancedAttachmentFromXFile(
            image,
            AttachmentType.image,
          );
          if (attachment != null) {
            attachments.add(attachment);
          }
        }
      }

      return attachments;
    } catch (e) {
      debugPrint('Error picking images: $e');
      throw Exception('Failed to pick images: $e');
    }
  }

  /// Pick videos from gallery or camera with file data
  static Future<List<EnhancedAttachmentModel>> pickVideos({
    bool allowMultiple = false,
    ImageSource source = ImageSource.gallery,
  }) async {
    try {
      final List<EnhancedAttachmentModel> attachments = [];

      final XFile? video = await _imagePicker.pickVideo(
        source: source,
        maxDuration: const Duration(minutes: 5), // 5 minute limit
      );

      if (video != null) {
        final attachment = await _createEnhancedAttachmentFromXFile(
          video,
          AttachmentType.video,
        );
        if (attachment != null) {
          attachments.add(attachment);
        }
      }

      return attachments;
    } catch (e) {
      debugPrint('Error picking videos: $e');
      throw Exception('Failed to pick videos: $e');
    }
  }

  /// Pick documents using file picker with file data
  static Future<List<EnhancedAttachmentModel>> pickDocuments({
    bool allowMultiple = true,
  }) async {
    try {
      final List<EnhancedAttachmentModel> attachments = [];

      final FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: AttachmentModel.getSupportedExtensions(AttachmentType.document),
        allowMultiple: allowMultiple,
        withData: true, // Always load data
      );

      if (result != null) {
        for (final file in result.files) {
          final attachment = await _createEnhancedAttachmentFromPlatformFile(
            file,
            AttachmentType.document,
          );
          if (attachment != null) {
            attachments.add(attachment);
          }
        }
      }

      return attachments;
    } catch (e) {
      debugPrint('Error picking documents: $e');
      throw Exception('Failed to pick documents: $e');
    }
  }

  /// Create enhanced attachment from XFile (images/videos)
  static Future<EnhancedAttachmentModel?> _createEnhancedAttachmentFromXFile(
    XFile file,
    AttachmentType type,
  ) async {
    try {
      final String fileName = path.basename(file.path);
      final String extension = path.extension(fileName).toLowerCase();
      final int fileSize = await file.length();

      // Validate file size based on type
      int maxSize;
      String sizeLabel;

      switch (type) {
        case AttachmentType.video:
          maxSize = 50 * 1024 * 1024; // 50MB for videos
          sizeLabel = '50MB';
          break;
        case AttachmentType.image:
          maxSize = 10 * 1024 * 1024; // 10MB for images
          sizeLabel = '10MB';
          break;
        case AttachmentType.document:
          maxSize = 25 * 1024 * 1024; // 25MB for documents
          sizeLabel = '25MB';
          break;
      }

      if (fileSize > maxSize) {
        throw Exception('File size too large. Maximum $sizeLabel allowed for ${type.toString().split('.').last}s.');
      }

      // Get file data
      Uint8List fileData;
      if (kIsWeb) {
        fileData = await file.readAsBytes();
      } else {
        fileData = await File(file.path).readAsBytes();
      }

      // Generate unique file name
      final String uniqueFileName = _generateUniqueFileName(fileName);

      // Determine MIME type
      final String mimeType = _getMimeTypeFromExtension(extension);

      return EnhancedAttachmentModel(
        id: '', // Will be set when uploaded
        fileName: uniqueFileName,
        originalFileName: fileName,
        type: type,
        url: '', // Will be set when uploaded
        fileSize: fileData.length,
        mimeType: mimeType,
        uploadedAt: DateTime.now(),
        uploadedBy: '', // Will be set when uploaded
        metadata: type == AttachmentType.video ? {'originalSize': fileSize} : null,
        fileData: fileData,
      );
    } catch (e) {
      debugPrint('Error creating enhanced attachment from XFile: $e');
      return null;
    }
  }

  /// Create enhanced attachment from PlatformFile (documents)
  static Future<EnhancedAttachmentModel?> _createEnhancedAttachmentFromPlatformFile(
    PlatformFile file,
    AttachmentType type,
  ) async {
    try {
      final String fileName = file.name;
      final String extension = path.extension(fileName).toLowerCase();
      final int fileSize = file.size;

      // Validate file size based on type
      int maxSize;
      String sizeLabel;

      switch (type) {
        case AttachmentType.video:
          maxSize = 50 * 1024 * 1024; // 50MB for videos
          sizeLabel = '50MB';
          break;
        case AttachmentType.image:
          maxSize = 10 * 1024 * 1024; // 10MB for images
          sizeLabel = '10MB';
          break;
        case AttachmentType.document:
          maxSize = 25 * 1024 * 1024; // 25MB for documents
          sizeLabel = '25MB';
          break;
      }

      if (fileSize > maxSize) {
        throw Exception('File size too large. Maximum $sizeLabel allowed for ${type.toString().split('.').last}s.');
      }

      // Get file data
      Uint8List? fileData = file.bytes;
      if (fileData == null && file.path != null) {
        fileData = await File(file.path!).readAsBytes();
      }

      if (fileData == null) {
        throw Exception('Could not read file data');
      }

      // Generate unique file name
      final String uniqueFileName = _generateUniqueFileName(fileName);

      // Determine MIME type
      final String mimeType = _getMimeTypeFromExtension(extension);

      return EnhancedAttachmentModel(
        id: '', // Will be set when uploaded
        fileName: uniqueFileName,
        originalFileName: fileName,
        type: type,
        url: '', // Will be set when uploaded
        fileSize: fileData.length,
        mimeType: mimeType,
        uploadedAt: DateTime.now(),
        uploadedBy: '', // Will be set when uploaded
        fileData: fileData,
      );
    } catch (e) {
      debugPrint('Error creating enhanced attachment from PlatformFile: $e');
      return null;
    }
  }

  /// Generate unique file name with HM prefix and date
  static String _generateUniqueFileName(String originalFileName) {
    final now = DateTime.now();
    final dateStr = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
    final timeStr = '${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}';
    final extension = path.extension(originalFileName);

    // Generate sequential number for the day
    final sequentialNumber = now.millisecondsSinceEpoch % 10000;

    return 'HM_${dateStr}_${timeStr}_${sequentialNumber.toString().padLeft(4, '0')}$extension';
  }

  /// Get MIME type from file extension
  static String _getMimeTypeFromExtension(String extension) {
    switch (extension.toLowerCase()) {
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.png':
        return 'image/png';
      case '.gif':
        return 'image/gif';
      case '.webp':
        return 'image/webp';
      case '.bmp':
        return 'image/bmp';
      case '.mp4':
        return 'video/mp4';
      case '.mov':
        return 'video/quicktime';
      case '.avi':
        return 'video/x-msvideo';
      case '.mkv':
        return 'video/x-matroska';
      case '.webm':
        return 'video/webm';
      case '.3gp':
        return 'video/3gpp';
      case '.pdf':
        return 'application/pdf';
      case '.doc':
        return 'application/msword';
      case '.docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case '.txt':
        return 'text/plain';
      case '.rtf':
        return 'application/rtf';
      case '.odt':
        return 'application/vnd.oasis.opendocument.text';
      default:
        return 'application/octet-stream';
    }
  }
}
