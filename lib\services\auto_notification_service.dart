import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة الإشعارات التلقائية
class AutoNotificationService {
  static AutoNotificationService? _instance;
  static AutoNotificationService get instance => _instance ??= AutoNotificationService._();

  AutoNotificationService._();

  static const String _autoNotificationKey = 'auto_notification_settings';

  AutoNotificationSettings _settings = const AutoNotificationSettings();
  bool _isInitialized = false;

  /// تهيئة خدمة الإشعارات التلقائية
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadSettings();

      if (_settings.isEnabled) {
        await _setupAutoNotifications();
      }

      _isInitialized = true;
      debugPrint('AutoNotificationService initialized');
    } catch (e) {
      debugPrint('Error initializing AutoNotificationService: $e');
    }
  }

  /// تحميل إعدادات الإشعارات التلقائية
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_autoNotificationKey);

      if (settingsJson != null) {
        final settingsMap = jsonDecode(settingsJson) as Map<String, dynamic>;
        _settings = AutoNotificationSettings.fromMap(settingsMap);
      }
    } catch (e) {
      debugPrint('Error loading auto notification settings: $e');
    }
  }

  /// حفظ إعدادات الإشعارات التلقائية
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_autoNotificationKey, jsonEncode(_settings.toMap()));
    } catch (e) {
      debugPrint('Error saving auto notification settings: $e');
    }
  }

  /// إعداد الإشعارات التلقائية
  Future<void> _setupAutoNotifications() async {
    try {
      // إعداد مراقبة الأخطاء الجديدة
      if (_settings.notifyOnNewErrors) {
        _setupNewErrorNotifications();
      }

      // إعداد الإشعارات الدورية
      if (_settings.enablePeriodicNotifications) {
        _setupPeriodicNotifications();
      }

      // إعداد إشعارات التحديثات
      if (_settings.notifyOnUpdates) {
        _setupUpdateNotifications();
      }

      debugPrint('Auto notifications setup completed');
    } catch (e) {
      debugPrint('Error setting up auto notifications: $e');
    }
  }

  /// إعداد مراقبة الأخطاء الجديدة
  void _setupNewErrorNotifications() {
    FirebaseFirestore.instance
        .collection('device_errors')
        .orderBy('createdAt', descending: true)
        .limit(1)
        .snapshots()
        .listen((snapshot) async {
      if (snapshot.docs.isNotEmpty) {
        final latestError = snapshot.docs.first;
        final errorData = latestError.data();

        // التحقق من أن هذا خطأ جديد وليس تحديث
        final createdAt = errorData['createdAt'] as Timestamp?;
        if (createdAt != null) {
          final errorTime = createdAt.toDate();
          final now = DateTime.now();

          // إرسال إشعار فقط إذا كان الخطأ جديد (خلال آخر 5 دقائق)
          if (now.difference(errorTime).inMinutes <= 5) {
            await _sendNewErrorNotification(errorData);
          }
        }
      }
    });
  }

  /// إرسال إشعار خطأ جديد
  Future<void> _sendNewErrorNotification(Map<String, dynamic> errorData) async {
    try {
      final manufacturer = errorData['manufacturer'] ?? 'Unknown';
      final model = errorData['model'] ?? 'Unknown';
      final errorCode = errorData['errorCode'] ?? 'N/A';

      await FirebaseFirestore.instance.collection('notifications').add({
        'type': 'new_error_auto',
        'title_ar': 'عطل جديد تم إضافته',
        'title_en': 'New Error Added',
        'body_ar': 'تم إضافة عطل جديد: $manufacturer $model (كود الخطأ: $errorCode)',
        'body_en': 'New error added: $manufacturer $model (Error Code: $errorCode)',
        'error_id': errorData['id'],
        'error_code': errorCode,
        'manufacturer': manufacturer,
        'model': model,
        'created_at': FieldValue.serverTimestamp(),
        'target_audience': 'all_users',
        'auto_generated': true,
        'priority': 'high',
      });

      debugPrint('Auto notification sent for new error: $manufacturer $model');
    } catch (e) {
      debugPrint('Error sending new error notification: $e');
    }
  }

  /// إعداد الإشعارات الدورية
  void _setupPeriodicNotifications() {
    // سيتم تنفيذ هذا عبر Cloud Functions
    // هنا نحفظ فقط الإعدادات
    debugPrint('Periodic notifications enabled');
  }

  /// إعداد إشعارات التحديثات
  void _setupUpdateNotifications() {
    // مراقبة تحديثات التطبيق
    FirebaseFirestore.instance
        .collection('app_updates')
        .orderBy('version_code', descending: true)
        .limit(1)
        .snapshots()
        .listen((snapshot) async {
      if (snapshot.docs.isNotEmpty) {
        final latestUpdate = snapshot.docs.first;
        final updateData = latestUpdate.data();

        await _sendUpdateNotification(updateData);
      }
    });
  }

  /// إرسال إشعار تحديث
  Future<void> _sendUpdateNotification(Map<String, dynamic> updateData) async {
    try {
      final version = updateData['version_name'] ?? 'Unknown';
      final isForced = updateData['is_forced'] ?? false;

      await FirebaseFirestore.instance.collection('notifications').add({
        'type': 'app_update_auto',
        'title_ar': isForced ? 'تحديث إجباري متاح' : 'تحديث جديد متاح',
        'title_en': isForced ? 'Mandatory Update Available' : 'New Update Available',
        'body_ar': 'إصدار جديد $version متاح للتحميل',
        'body_en': 'New version $version is available for download',
        'version': version,
        'is_forced': isForced,
        'created_at': FieldValue.serverTimestamp(),
        'target_audience': 'all_users',
        'auto_generated': true,
        'priority': isForced ? 'critical' : 'normal',
      });

      debugPrint('Auto notification sent for app update: $version');
    } catch (e) {
      debugPrint('Error sending update notification: $e');
    }
  }

  /// إرسال إشعار مخصص لجميع المستخدمين
  Future<void> sendCustomNotificationToAll({
    required String titleAr,
    required String titleEn,
    required String bodyAr,
    required String bodyEn,
    String priority = 'normal',
    Map<String, dynamic>? data,
  }) async {
    try {
      await FirebaseFirestore.instance.collection('notifications').add({
        'type': 'custom_broadcast',
        'title_ar': titleAr,
        'title_en': titleEn,
        'body_ar': bodyAr,
        'body_en': bodyEn,
        'target_audience': 'all_users',
        'priority': priority,
        'data': data ?? {},
        'created_at': FieldValue.serverTimestamp(),
        'auto_generated': false,
      });

      debugPrint('Custom broadcast notification sent');
    } catch (e) {
      debugPrint('Error sending custom broadcast notification: $e');
    }
  }

  /// إرسال إشعار لمجموعة محددة من المستخدمين
  Future<void> sendNotificationToGroup({
    required String groupType, // 'admins', 'active_users', 'expired_users', etc.
    required String titleAr,
    required String titleEn,
    required String bodyAr,
    required String bodyEn,
    String priority = 'normal',
    Map<String, dynamic>? data,
  }) async {
    try {
      await FirebaseFirestore.instance.collection('notifications').add({
        'type': 'group_notification',
        'title_ar': titleAr,
        'title_en': titleEn,
        'body_ar': bodyAr,
        'body_en': bodyEn,
        'target_group': groupType,
        'priority': priority,
        'data': data ?? {},
        'created_at': FieldValue.serverTimestamp(),
        'auto_generated': false,
      });

      debugPrint('Group notification sent to: $groupType');
    } catch (e) {
      debugPrint('Error sending group notification: $e');
    }
  }

  /// إرسال إشعار صيانة
  Future<void> sendMaintenanceNotification({
    required DateTime startTime,
    required DateTime endTime,
    required String reasonAr,
    required String reasonEn,
  }) async {
    try {
      await FirebaseFirestore.instance.collection('notifications').add({
        'type': 'maintenance',
        'title_ar': 'إشعار صيانة',
        'title_en': 'Maintenance Notice',
        'body_ar': 'سيتم إجراء صيانة للتطبيق من ${_formatDateTime(startTime)} إلى ${_formatDateTime(endTime)}. السبب: $reasonAr',
        'body_en': 'App maintenance scheduled from ${_formatDateTime(startTime)} to ${_formatDateTime(endTime)}. Reason: $reasonEn',
        'maintenance_start': Timestamp.fromDate(startTime),
        'maintenance_end': Timestamp.fromDate(endTime),
        'reason_ar': reasonAr,
        'reason_en': reasonEn,
        'target_audience': 'all_users',
        'priority': 'high',
        'created_at': FieldValue.serverTimestamp(),
        'auto_generated': false,
      });

      debugPrint('Maintenance notification sent');
    } catch (e) {
      debugPrint('Error sending maintenance notification: $e');
    }
  }

  /// تحديث إعدادات الإشعارات التلقائية
  Future<void> updateSettings(AutoNotificationSettings newSettings) async {
    _settings = newSettings;
    await _saveSettings();

    if (_settings.isEnabled) {
      await _setupAutoNotifications();
    }

    debugPrint('Auto notification settings updated');
  }

  /// الحصول على الإعدادات الحالية
  AutoNotificationSettings get settings => _settings;

  /// الحصول على إحصائيات الإشعارات
  Future<Map<String, dynamic>> getNotificationStats() async {
    try {
      final now = DateTime.now();
      final last24Hours = now.subtract(const Duration(hours: 24));
      final last7Days = now.subtract(const Duration(days: 7));

      // إحصائيات آخر 24 ساعة
      final last24HoursQuery = await FirebaseFirestore.instance
          .collection('notifications')
          .where('created_at', isGreaterThan: Timestamp.fromDate(last24Hours))
          .get();

      // إحصائيات آخر 7 أيام
      final last7DaysQuery = await FirebaseFirestore.instance
          .collection('notifications')
          .where('created_at', isGreaterThan: Timestamp.fromDate(last7Days))
          .get();

      // إحصائيات الإشعارات التلقائية
      final autoNotificationsQuery = await FirebaseFirestore.instance
          .collection('notifications')
          .where('auto_generated', isEqualTo: true)
          .where('created_at', isGreaterThan: Timestamp.fromDate(last7Days))
          .get();

      return {
        'last_24_hours': last24HoursQuery.docs.length,
        'last_7_days': last7DaysQuery.docs.length,
        'auto_generated_last_7_days': autoNotificationsQuery.docs.length,
        'manual_notifications_last_7_days': last7DaysQuery.docs.length - autoNotificationsQuery.docs.length,
        'settings': _settings.toMap(),
      };
    } catch (e) {
      debugPrint('Error getting notification stats: $e');
      return {};
    }
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

/// إعدادات الإشعارات التلقائية
class AutoNotificationSettings {
  final bool isEnabled;
  final bool notifyOnNewErrors;
  final bool notifyOnUpdates;
  final bool enablePeriodicNotifications;
  final int periodicIntervalHours;
  final bool notifyOnMaintenance;
  final bool notifyOnSecurityAlerts;
  final List<String> enabledNotificationTypes;

  const AutoNotificationSettings({
    this.isEnabled = true,
    this.notifyOnNewErrors = true,
    this.notifyOnUpdates = true,
    this.enablePeriodicNotifications = false,
    this.periodicIntervalHours = 24,
    this.notifyOnMaintenance = true,
    this.notifyOnSecurityAlerts = true,
    this.enabledNotificationTypes = const [
      'new_error',
      'app_update',
      'maintenance',
      'security_alert',
    ],
  });

  Map<String, dynamic> toMap() {
    return {
      'isEnabled': isEnabled,
      'notifyOnNewErrors': notifyOnNewErrors,
      'notifyOnUpdates': notifyOnUpdates,
      'enablePeriodicNotifications': enablePeriodicNotifications,
      'periodicIntervalHours': periodicIntervalHours,
      'notifyOnMaintenance': notifyOnMaintenance,
      'notifyOnSecurityAlerts': notifyOnSecurityAlerts,
      'enabledNotificationTypes': enabledNotificationTypes,
    };
  }

  factory AutoNotificationSettings.fromMap(Map<String, dynamic> map) {
    return AutoNotificationSettings(
      isEnabled: map['isEnabled'] ?? true,
      notifyOnNewErrors: map['notifyOnNewErrors'] ?? true,
      notifyOnUpdates: map['notifyOnUpdates'] ?? true,
      enablePeriodicNotifications: map['enablePeriodicNotifications'] ?? false,
      periodicIntervalHours: map['periodicIntervalHours'] ?? 24,
      notifyOnMaintenance: map['notifyOnMaintenance'] ?? true,
      notifyOnSecurityAlerts: map['notifyOnSecurityAlerts'] ?? true,
      enabledNotificationTypes: List<String>.from(map['enabledNotificationTypes'] ?? [
        'new_error',
        'app_update',
        'maintenance',
        'security_alert',
      ]),
    );
  }

  AutoNotificationSettings copyWith({
    bool? isEnabled,
    bool? notifyOnNewErrors,
    bool? notifyOnUpdates,
    bool? enablePeriodicNotifications,
    int? periodicIntervalHours,
    bool? notifyOnMaintenance,
    bool? notifyOnSecurityAlerts,
    List<String>? enabledNotificationTypes,
  }) {
    return AutoNotificationSettings(
      isEnabled: isEnabled ?? this.isEnabled,
      notifyOnNewErrors: notifyOnNewErrors ?? this.notifyOnNewErrors,
      notifyOnUpdates: notifyOnUpdates ?? this.notifyOnUpdates,
      enablePeriodicNotifications: enablePeriodicNotifications ?? this.enablePeriodicNotifications,
      periodicIntervalHours: periodicIntervalHours ?? this.periodicIntervalHours,
      notifyOnMaintenance: notifyOnMaintenance ?? this.notifyOnMaintenance,
      notifyOnSecurityAlerts: notifyOnSecurityAlerts ?? this.notifyOnSecurityAlerts,
      enabledNotificationTypes: enabledNotificationTypes ?? this.enabledNotificationTypes,
    );
  }
}
