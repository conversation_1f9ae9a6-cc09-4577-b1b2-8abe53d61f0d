import 'package:flutter/material.dart';

/// Widget للاختيار المتعدد مع إمكانية البحث
class MultiSelectSearchableField extends StatefulWidget {
  final String label;
  final String hint;
  final List<String> options;
  final List<String> selectedValues;
  final Function(List<String>) onChanged;
  final IconData? prefixIcon;
  final bool allowAddNew;
  final Function(String)? onAddNew;
  final String addNewLabel;
  final int maxSelections;

  const MultiSelectSearchableField({
    super.key,
    required this.label,
    required this.hint,
    required this.options,
    required this.selectedValues,
    required this.onChanged,
    this.prefixIcon,
    this.allowAddNew = false,
    this.onAddNew,
    this.addNewLabel = 'إضافة جديد',
    this.maxSelections = 10,
  });

  @override
  State<MultiSelectSearchableField> createState() => _MultiSelectSearchableFieldState();
}

class _MultiSelectSearchableFieldState extends State<MultiSelectSearchableField> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  List<String> _filteredOptions = [];
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _filteredOptions = widget.options;
    _searchController.addListener(_filterOptions);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _filterOptions() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredOptions = widget.options
          .where((option) => option.toLowerCase().contains(query))
          .toList();
    });
  }

  void _toggleSelection(String value) {
    final newSelection = List<String>.from(widget.selectedValues);

    if (newSelection.contains(value)) {
      newSelection.remove(value);
    } else if (newSelection.length < widget.maxSelections) {
      newSelection.add(value);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يمكن اختيار ${widget.maxSelections} عناصر كحد أقصى'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    widget.onChanged(newSelection);
  }

  void _removeSelection(String value) {
    final newSelection = List<String>.from(widget.selectedValues);
    newSelection.remove(value);
    widget.onChanged(newSelection);
  }

  void _addNewItem() {
    final value = _searchController.text.trim();
    if (value.isNotEmpty && !widget.options.contains(value)) {
      if (widget.onAddNew != null) {
        widget.onAddNew!(value);
      }
      _toggleSelection(value);
      _searchController.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Selected items display
        if (widget.selectedValues.isNotEmpty) ...[
          Text(
            '${widget.label} المختارة:',
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: widget.selectedValues.map((value) {
              return Chip(
                label: Text(value),
                deleteIcon: const Icon(Icons.close, size: 18),
                onDeleted: () => _removeSelection(value),
                backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                labelStyle: TextStyle(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              );
            }).toList(),
          ),
          const SizedBox(height: 16),
        ],

        // Search field
        TextFormField(
          controller: _searchController,
          focusNode: _focusNode,
          decoration: InputDecoration(
            labelText: widget.label,
            hintText: widget.hint,
            border: const OutlineInputBorder(),
            prefixIcon: widget.prefixIcon != null ? Icon(widget.prefixIcon) : null,
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (_searchController.text.isNotEmpty)
                  IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      _searchController.clear();
                      setState(() {
                        _isExpanded = false;
                      });
                    },
                  ),
                IconButton(
                  icon: Icon(_isExpanded ? Icons.expand_less : Icons.expand_more),
                  onPressed: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                    if (_isExpanded) {
                      _focusNode.requestFocus();
                    }
                  },
                ),
              ],
            ),
          ),
          onTap: () {
            setState(() {
              _isExpanded = true;
            });
          },
          onChanged: (value) {
            setState(() {
              _isExpanded = value.isNotEmpty || _focusNode.hasFocus;
            });
          },
          // validator removed - validation handled in parent widget
        ),

        // Options dropdown
        if (_isExpanded) ...[
          const SizedBox(height: 8),
          Container(
            constraints: const BoxConstraints(maxHeight: 200),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Available options
                if (_filteredOptions.isNotEmpty)
                  Flexible(
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: _filteredOptions.length,
                      itemBuilder: (context, index) {
                        final option = _filteredOptions[index];
                        final isSelected = widget.selectedValues.contains(option);

                        return ListTile(
                          dense: true,
                          title: Text(option),
                          trailing: isSelected
                              ? const Icon(Icons.check, color: Colors.green)
                              : null,
                          onTap: () => _toggleSelection(option),
                          tileColor: isSelected
                              ? Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3)
                              : null,
                        );
                      },
                    ),
                  ),

                // Add new option
                if (widget.allowAddNew && _searchController.text.isNotEmpty) ...[
                  const Divider(height: 1),
                  ListTile(
                    dense: true,
                    leading: const Icon(Icons.add),
                    title: Text('${widget.addNewLabel}: "${_searchController.text}"'),
                    onTap: _addNewItem,
                  ),
                ],

                // No options found
                if (_filteredOptions.isEmpty && _searchController.text.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      'لا توجد نتائج',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ],
    );
  }
}
