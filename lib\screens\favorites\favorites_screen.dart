import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/error_provider.dart';
import '../../providers/locale_provider.dart';
import '../../models/device_error_model.dart';
import '../../widgets/gradient_background.dart';
import '../../widgets/enhanced_error_card.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> {
  bool _isLoading = false;
  final TextEditingController _searchController = TextEditingController();
  List<DeviceError> _filteredErrors = [];
  bool _isSearchExpanded = false;
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _loadFavorites();
    _searchController.addListener(_filterErrors);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  Future<void> _loadFavorites() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await Provider.of<ErrorProvider>(context, listen: false).fetchFavoriteErrors();
      _filterErrors();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading favorites: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _filterErrors() {
    final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
    final favoriteErrors = errorProvider.favoriteErrors;
    final searchQuery = _searchController.text.toLowerCase();

    if (searchQuery.isEmpty) {
      _filteredErrors = List.from(favoriteErrors);
    } else {
      _filteredErrors = favoriteErrors.where((error) {
        return error.manufacturer.toLowerCase().contains(searchQuery) ||
               error.model.toLowerCase().contains(searchQuery) ||
               error.errorCode.toLowerCase().contains(searchQuery) ||
               error.description.toLowerCase().contains(searchQuery) ||
               error.solution.toLowerCase().contains(searchQuery);
      }).toList();
    }

    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _toggleFavorite(DeviceError error) async {
    final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    try {
      final success = await errorProvider.toggleFavorite(error);

      if (success && mounted) {
        // Reload favorites
        _loadFavorites();

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL ? 'تمت إزالة العطل من المفضلة' : 'Removed from favorites',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _toggleSearch() {
    setState(() {
      _isSearchExpanded = !_isSearchExpanded;
      if (_isSearchExpanded) {
        _searchFocusNode.requestFocus();
      } else {
        _searchFocusNode.unfocus();
        _searchController.clear();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    return GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          title: Text(
            isRTL ? 'المفضلة' : 'Favorites',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          actions: [
            // Search toggle button
            IconButton(
              icon: Icon(
                _isSearchExpanded ? Icons.close : Icons.search,
                size: 26,
              ),
              onPressed: _toggleSearch,
              tooltip: isRTL ? 'بحث' : 'Search',
            ),
            const SizedBox(width: 8),
          ],
        ),
        body: Column(
          children: [
            // Expandable search bar
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              height: _isSearchExpanded ? 80 : 0,
              child: _isSearchExpanded
                  ? Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      child: TextField(
                        controller: _searchController,
                        focusNode: _searchFocusNode,
                        decoration: InputDecoration(
                          hintText: isRTL ? 'ابحث في المفضلة...' : 'Search favorites...',
                          hintStyle: const TextStyle(fontSize: 16),
                          prefixIcon: const Icon(Icons.search, size: 22),
                          suffixIcon: _searchController.text.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(Icons.clear, size: 20),
                                  onPressed: () {
                                    _searchController.clear();
                                  },
                                )
                              : null,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(24),
                            borderSide: BorderSide(
                              color: Theme.of(context).colorScheme.outline.withAlpha(100),
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(24),
                            borderSide: BorderSide(
                              color: Theme.of(context).colorScheme.outline.withAlpha(100),
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(24),
                            borderSide: BorderSide(
                              color: Theme.of(context).colorScheme.primary,
                              width: 2,
                            ),
                          ),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          filled: true,
                          fillColor: Theme.of(context).colorScheme.surface.withAlpha(200),
                        ),
                      ),
                    )
                  : const SizedBox.shrink(),
            ),

            // Content
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _filteredErrors.isEmpty
                      ? _buildEmptyState(context, isRTL)
                      : _buildFavoritesList(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, bool isRTL) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withAlpha(20),
                shape: BoxShape.circle,
              ),
              child: Icon(
                _searchController.text.isNotEmpty ? Icons.search_off : Icons.favorite_border,
                size: 64,
                color: Theme.of(context).colorScheme.primary.withAlpha(180),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              _searchController.text.isNotEmpty
                  ? (isRTL ? 'لا توجد نتائج للبحث' : 'No search results')
                  : (isRTL ? 'لا توجد أعطال في المفضلة' : 'No favorite errors yet'),
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              _searchController.text.isNotEmpty
                  ? (isRTL ? 'جرب كلمات بحث أخرى' : 'Try different search terms')
                  : (isRTL
                      ? 'أضف أعطالًا إلى المفضلة لتظهر هنا'
                      : 'Add errors to favorites to see them here'),
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withAlpha(180),
              ),
            ),
            if (_searchController.text.isNotEmpty) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () {
                  _searchController.clear();
                },
                icon: const Icon(Icons.clear, size: 20),
                label: Text(isRTL ? 'مسح البحث' : 'Clear Search'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFavoritesList(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: _filteredErrors.length,
      itemBuilder: (context, index) {
        final error = _filteredErrors[index];
        return Padding(
          padding: EdgeInsets.only(
            bottom: index == _filteredErrors.length - 1 ? 24 : 12,
          ),
          child: EnhancedErrorCard(
            error: error,
            isCompact: false,
            showFavoriteButton: true,
            onFavoritePressed: () => _toggleFavorite(error),
          ),
        );
      },
    );
  }
}
