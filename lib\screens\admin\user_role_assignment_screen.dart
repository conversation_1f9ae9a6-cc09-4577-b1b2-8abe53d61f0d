import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/locale_provider.dart';
import '../../providers/permission_provider.dart';
import '../../providers/activity_log_provider.dart';
import '../../models/user_model.dart';
import '../../models/permission_model.dart' as perm;


class UserRoleAssignmentScreen extends StatefulWidget {
  final UserModel user;

  const UserRoleAssignmentScreen({super.key, required this.user});

  @override
  State<UserRoleAssignmentScreen> createState() => _UserRoleAssignmentScreenState();
}

class _UserRoleAssignmentScreenState extends State<UserRoleAssignmentScreen> {
  String? _selectedRoleId;
  Set<perm.PermissionType> _customPermissions = {};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<PermissionProvider>(context, listen: false).loadRoles();
    });
  }

  void _loadUserData() {
    _selectedRoleId = widget.user.roleId;
    _customPermissions = Set.from(widget.user.customPermissions);
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isRTL ? 'تعيين الأدوار والصلاحيات' : 'Assign Roles & Permissions',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          if (!_isLoading)
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveChanges,
              tooltip: isRTL ? 'حفظ التغييرات' : 'Save Changes',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildUserInfoCard(isRTL),
                  const SizedBox(height: 16),
                  _buildRoleSelectionCard(isRTL),
                  const SizedBox(height: 16),
                  _buildCustomPermissionsCard(isRTL),
                  const SizedBox(height: 24),
                  _buildSaveButton(isRTL),
                ],
              ),
            ),
    );
  }

  Widget _buildUserInfoCard(bool isRTL) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  backgroundImage: widget.user.profileImageUrl != null
                      ? NetworkImage(widget.user.profileImageUrl!)
                      : null,
                  child: widget.user.profileImageUrl == null
                      ? Text(
                          widget.user.displayName?.substring(0, 1).toUpperCase() ??
                              widget.user.email.substring(0, 1).toUpperCase(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                      : null,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.user.displayName ?? widget.user.email,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.user.email,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface.withAlpha(178),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: widget.user.role == UserRole.admin
                              ? Colors.red.withAlpha(25)
                              : Colors.blue.withAlpha(25),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: widget.user.role == UserRole.admin ? Colors.red : Colors.blue,
                          ),
                        ),
                        child: Text(
                          isRTL ? widget.user.effectiveRoleNameAr : widget.user.effectiveRoleName,
                          style: TextStyle(
                            color: widget.user.role == UserRole.admin ? Colors.red : Colors.blue,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleSelectionCard(bool isRTL) {
    return Consumer<PermissionProvider>(
      builder: (context, permissionProvider, child) {
        if (permissionProvider.isLoading) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Center(child: CircularProgressIndicator()),
            ),
          );
        }

        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.admin_panel_settings,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      isRTL ? 'تعيين الدور' : 'Role Assignment',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Role selection dropdown
                DropdownButtonFormField<String>(
                  value: _selectedRoleId,
                  decoration: InputDecoration(
                    labelText: isRTL ? 'اختر الدور' : 'Select Role',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    prefixIcon: const Icon(Icons.security),
                  ),
                  items: [
                    DropdownMenuItem<String>(
                      value: null,
                      child: Text(isRTL ? 'بدون دور محدد' : 'No specific role'),
                    ),
                    ...permissionProvider.roles.map((role) {
                      return DropdownMenuItem<String>(
                        value: role.id,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              isRTL ? role.nameAr : role.name,
                              style: const TextStyle(fontWeight: FontWeight.w600),
                            ),
                            Text(
                              isRTL ? role.descriptionAr : role.description,
                              style: TextStyle(
                                fontSize: 12,
                                color: Theme.of(context).colorScheme.onSurface.withAlpha(178),
                              ),
                            ),
                          ],
                        ),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedRoleId = value;
                    });
                  },
                ),

                if (_selectedRoleId != null) ...[
                  const SizedBox(height: 16),
                  _buildSelectedRoleInfo(permissionProvider, isRTL),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSelectedRoleInfo(PermissionProvider permissionProvider, bool isRTL) {
    final selectedRole = permissionProvider.getRoleById(_selectedRoleId!);
    if (selectedRole == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isRTL ? 'صلاحيات هذا الدور:' : 'Role Permissions:',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isRTL
                ? 'عدد الصلاحيات: ${selectedRole.permissions.length}'
                : 'Permissions count: ${selectedRole.permissions.length}',
            style: TextStyle(
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
          if (selectedRole.permissions.isNotEmpty) ...[
            const SizedBox(height: 8),
            Wrap(
              spacing: 4,
              runSpacing: 4,
              children: selectedRole.permissions.take(5).map((permission) {
                return Chip(
                  label: Text(
                    perm.PermissionHelper.getPermissionName(permission, isRTL),
                    style: const TextStyle(fontSize: 10),
                  ),
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                );
              }).toList(),
            ),
            if (selectedRole.permissions.length > 5)
              Text(
                isRTL
                    ? 'و ${selectedRole.permissions.length - 5} صلاحيات أخرى...'
                    : 'and ${selectedRole.permissions.length - 5} more...',
                style: TextStyle(
                  fontSize: 12,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
          ],
        ],
      ),
    );
  }

  Widget _buildCustomPermissionsCard(bool isRTL) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.tune,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'صلاحيات إضافية' : 'Additional Permissions',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  isRTL
                      ? 'المحددة: ${_customPermissions.length}'
                      : 'Selected: ${_customPermissions.length}',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              isRTL
                  ? 'صلاحيات إضافية خاصة بهذا المستخدم فقط'
                  : 'Additional permissions specific to this user only',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface.withAlpha(178),
              ),
            ),
            const SizedBox(height: 16),

            // Custom permissions list
            _buildCustomPermissionsList(isRTL),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomPermissionsList(bool isRTL) {
    // Group permissions by category
    final Map<String, List<perm.PermissionType>> groupedPermissions = {};

    for (final permission in perm.PermissionType.values) {
      final category = perm.PermissionHelper.getPermissionCategory(permission, isRTL);
      groupedPermissions.putIfAbsent(category, () => []).add(permission);
    }

    return Column(
      children: groupedPermissions.entries.map((entry) {
        final category = entry.key;
        final permissions = entry.value;
        final selectedInCategory = permissions.where((p) => _customPermissions.contains(p)).length;

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ExpansionTile(
            title: Text(
              category,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            subtitle: Text(
              isRTL
                  ? 'محدد: $selectedInCategory من ${permissions.length}'
                  : 'Selected: $selectedInCategory of ${permissions.length}',
            ),
            children: permissions.map((permission) {
              return CheckboxListTile(
                title: Text(perm.PermissionHelper.getPermissionName(permission, isRTL)),
                value: _customPermissions.contains(permission),
                onChanged: (value) {
                  setState(() {
                    if (value == true) {
                      _customPermissions.add(permission);
                    } else {
                      _customPermissions.remove(permission);
                    }
                  });
                },
                dense: true,
              );
            }).toList(),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSaveButton(bool isRTL) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton.icon(
        icon: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Icon(Icons.save),
        label: Text(
          _isLoading
              ? (isRTL ? 'جاري الحفظ...' : 'Saving...')
              : (isRTL ? 'حفظ التغييرات' : 'Save Changes'),
          style: const TextStyle(fontSize: 16),
        ),
        onPressed: _isLoading ? null : _saveChanges,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  Future<void> _saveChanges() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final permissionProvider = Provider.of<PermissionProvider>(context, listen: false);
      final activityLogProvider = Provider.of<ActivityLogProvider>(context, listen: false);
      final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
      final isRTL = localeProvider.isRTL;

      // Update user role
      if (_selectedRoleId != widget.user.roleId) {
        final success = await permissionProvider.assignRoleToUser(widget.user.id, _selectedRoleId ?? '');
        if (success) {
          final roleName = _selectedRoleId != null
              ? permissionProvider.getRoleById(_selectedRoleId!)?.name ?? 'Unknown'
              : 'No Role';

          await activityLogProvider.logUserRoleAssigned(
            authProvider.user!.id,
            authProvider.user!.displayName ?? authProvider.user!.email,
            widget.user.id,
            widget.user.displayName ?? widget.user.email,
            _selectedRoleId ?? '',
            roleName,
          );
        }
      }

      // Update custom permissions
      final currentPermissions = Set.from(widget.user.customPermissions);
      final addedPermissions = _customPermissions.difference(currentPermissions);
      final removedPermissions = currentPermissions.difference(_customPermissions);

      // Add new permissions
      for (final permission in addedPermissions) {
        await permissionProvider.addCustomPermissionToUser(widget.user.id, permission);
        await activityLogProvider.logPermissionGranted(
          authProvider.user!.id,
          authProvider.user!.displayName ?? authProvider.user!.email,
          widget.user.id,
          widget.user.displayName ?? widget.user.email,
          permission.toString(),
        );
      }

      // Remove permissions
      for (final permission in removedPermissions) {
        await permissionProvider.removeCustomPermissionFromUser(widget.user.id, permission);
        await activityLogProvider.logPermissionRevoked(
          authProvider.user!.id,
          authProvider.user!.displayName ?? authProvider.user!.email,
          widget.user.id,
          widget.user.displayName ?? widget.user.email,
          permission.toString(),
        );
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL ? 'تم حفظ التغييرات بنجاح' : 'Changes saved successfully',
            ),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true); // Return true to indicate changes were made
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
