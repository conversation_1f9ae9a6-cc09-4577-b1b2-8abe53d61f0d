import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';
import '../models/user_model.dart';
import '../models/login_log_model.dart';
import '../models/permission_model.dart' as perm;
import '../services/auth_service.dart';
import '../services/login_history_service.dart';
import '../services/session_service.dart';
import '../services/notification_service.dart';
import '../services/error_translation_service.dart';
import '../providers/login_log_provider.dart';

class AuthProvider with ChangeNotifier {
  final AuthService _authService = AuthService();
  final LoginLogProvider _loginLogProvider = LoginLogProvider();

  UserModel? _user;
  bool _isLoading = false;
  String? _error;
  Function(String)? _onUserSignedIn;

  UserModel? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _user != null;
  bool get isAdmin => _user?.role == UserRole.admin;

  // Set callback for when user signs in
  void setOnUserSignedInCallback(Function(String) callback) {
    _onUserSignedIn = callback;
  }

  // Save login credentials
  Future<void> _saveLoginCredentials(String email, String password, bool rememberMe) async {
    final prefs = await SharedPreferences.getInstance();
    if (rememberMe) {
      await prefs.setString('saved_email', email);
      await prefs.setString('saved_password', password);
      await prefs.setBool('remember_me', true);
    } else {
      await prefs.remove('saved_email');
      await prefs.remove('saved_password');
      await prefs.setBool('remember_me', false);
    }
  }

  // Get saved login credentials
  Future<Map<String, dynamic>> getSavedCredentials() async {
    final prefs = await SharedPreferences.getInstance();
    return {
      'email': prefs.getString('saved_email') ?? '',
      'password': prefs.getString('saved_password') ?? '',
      'remember_me': prefs.getBool('remember_me') ?? false,
    };
  }

  // Clear saved credentials
  Future<void> clearSavedCredentials() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('saved_email');
    await prefs.remove('saved_password');
    await prefs.setBool('remember_me', false);
  }

  // Initialize auth state
  Future<void> initializeAuth() async {
    _setLoading(true);
    try {
      // Check and create default admin user if needed
      await _authService.checkAndCreateDefaultAdmin();

      // Listen to auth state changes
      _authService.authStateChanges.listen((User? firebaseUser) async {
        if (firebaseUser != null) {
          try {
            // User is signed in, get user data from Firestore
            final userData = await _authService.getUserData();
            _user = userData;

            // Special handling for main admin user
            if (_user != null && _user!.email == '<EMAIL>' && !_user!.isMainAdmin) {
              // Update the isMainAdmin flag if needed
              debugPrint('Updating main admin flag for user');
              // This will be handled by the AuthService.checkAndCreateDefaultAdmin method
            }

            // Call the callback to initialize ErrorProvider
            if (_user != null && _onUserSignedIn != null) {
              _onUserSignedIn!(_user!.id);
            }
          } catch (e) {
            debugPrint('Error getting user data: $e');
            // Create a default user model if we can't get data from Firestore
            final isMainAdmin = firebaseUser.email == '<EMAIL>';
            _user = UserModel(
              id: firebaseUser.uid,
              email: firebaseUser.email ?? '<EMAIL>',
              displayName: firebaseUser.displayName,
              role: isMainAdmin ? UserRole.admin : UserRole.user, // Main admin is always admin
              createdAt: DateTime.now(),
              isMainAdmin: isMainAdmin,
            );

            // Call the callback to initialize ErrorProvider
            if (_user != null && _onUserSignedIn != null) {
              _onUserSignedIn!(_user!.id);
            }
          }
        } else {
          // User is signed out
          _user = null;
        }

        // Use SchedulerBinding to avoid setState during build
        SchedulerBinding.instance.addPostFrameCallback((_) {
          notifyListeners();
        });
      });
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Sign in with email and password
  Future<bool> signIn(String email, String password, {bool rememberMe = false}) async {
    _setLoading(true);
    _clearError();

    try {
      // Sign in (AuthService will check user validity first)
      await _authService.signInWithEmailAndPassword(email, password);

      // Get user data after successful sign in
      final userData = await _authService.getUserData();
      if (userData != null) {
        _user = userData;

        // Record login history with enhanced information
        try {
          await LoginHistoryService.recordLogin(
            userId: userData.id,
            userEmail: userData.email,
            userName: userData.displayName,
            isSuccessful: true,
          );
        } catch (e) {
          debugPrint('Error recording login history: $e');
          // Don't fail login if history recording fails
        }

        notifyListeners();
      }

      // Save credentials if remember me is checked
      await _saveLoginCredentials(email, password, rememberMe);

      // Log the login attempt and setup session monitoring
      if (_user != null) {
        try {
          // Record in new login history service
          await LoginHistoryService.recordLogin(
            userId: _user!.id,
            userEmail: email,
            userName: _user!.displayName,
            isSuccessful: true,
          );

          // Also record in old system for compatibility
          final loginLog = LoginLog(
            id: '',
            userId: _user!.id,
            userEmail: email,
            deviceInfo: kIsWeb ? 'Web Browser' : Platform.operatingSystem,
            ipAddress: 'Not collected',
            location: 'Not collected',
            timestamp: DateTime.now(),
            success: true,
          );

          await _loginLogProvider.addLog(loginLog);

          // Start session monitoring
          SessionService.startSessionMonitoring(
            onSessionExpired: () {
              _handleSessionExpired();
            },
            onUserDeactivated: () {
              _handleUserDeactivated();
            },
          );

          // Subscribe user to notifications
          await NotificationService.subscribeUser(_user!.id);

          // Set user as online
          await SessionService.setUserOnline();

        } catch (logError) {
          debugPrint('Error logging successful login: $logError');
        }
      }

      return true;
    } catch (e) {
      // Log failed login attempt (simplified)
      try {
        final loginLog = LoginLog(
          id: '',
          userId: '',
          userEmail: email,
          deviceInfo: kIsWeb ? 'Web Browser' : Platform.operatingSystem,
          ipAddress: 'Not collected',
          location: 'Not collected',
          timestamp: DateTime.now(),
          success: false,
        );

        await _loginLogProvider.addLog(loginLog);
      } catch (logError) {
        debugPrint('Error logging failed login: $logError');
      }

      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Register with email and password
  Future<bool> register(String email, String password, String? displayName) async {
    _setLoading(true);
    _clearError();

    try {
      await _authService.registerWithEmailAndPassword(email, password, displayName);
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Sign out
  Future<void> signOut() async {
    _setLoading(true);
    _clearError();

    try {
      // Stop session monitoring
      SessionService.stopSessionMonitoring();

      // Unsubscribe from notifications
      if (_user != null) {
        await NotificationService.unsubscribeUser(_user!.id);
        await SessionService.setUserOffline();
      }

      await _authService.signOut();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    _setLoading(true);
    _clearError();

    try {
      await _authService.resetPassword(email);
    } catch (e) {
      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Change password
  Future<void> changePassword(String email, String currentPassword, String newPassword) async {
    _setLoading(true);
    _clearError();

    try {
      await _authService.changePassword(email, currentPassword, newPassword);
    } catch (e) {
      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Get all users (admin only)
  Future<List<UserModel>> getAllUsers() async {
    if (!isAdmin) return [];

    _setLoading(true);
    _clearError();

    try {
      return await _authService.getAllUsers();
    } catch (e) {
      _setError(e.toString());
      return [];
    } finally {
      _setLoading(false);
    }
  }

  // Update user role (admin only)
  Future<bool> updateUserRole(String userId, UserRole role) async {
    if (!isAdmin) return false;

    _setLoading(true);
    _clearError();

    try {
      await _authService.updateUserRole(userId, role);
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Helper methods
  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  void _setError(String error) {
    _error = ErrorTranslationService.translateError(error);
    if (kDebugMode) {
      ErrorTranslationService.logError(error, _error!);
    }
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  // Permission-based helper methods
  bool hasPermission(perm.PermissionType permission) {
    return _user?.hasPermission(permission) ?? false;
  }

  bool hasAnyPermission(List<perm.PermissionType> permissions) {
    return _user?.hasAnyPermission(permissions) ?? false;
  }

  bool hasAllPermissions(List<perm.PermissionType> permissions) {
    return _user?.hasAllPermissions(permissions) ?? false;
  }

  // Convenience getters for common permissions
  bool get canViewErrors => hasPermission(perm.PermissionType.viewErrors);
  bool get canAddErrors => hasPermission(perm.PermissionType.addErrors);
  bool get canEditErrors => hasPermission(perm.PermissionType.editErrors);
  bool get canDeleteErrors => hasPermission(perm.PermissionType.deleteErrors);
  bool get canExportErrors => hasPermission(perm.PermissionType.exportErrors);
  bool get canImportErrors => hasPermission(perm.PermissionType.importErrors);

  bool get canViewCategories => hasPermission(perm.PermissionType.viewCategories);
  bool get canAddCategories => hasPermission(perm.PermissionType.addCategories);
  bool get canEditCategories => hasPermission(perm.PermissionType.editCategories);
  bool get canDeleteCategories => hasPermission(perm.PermissionType.deleteCategories);

  bool get canViewManufacturers => hasPermission(perm.PermissionType.viewManufacturers);
  bool get canAddManufacturers => hasPermission(perm.PermissionType.addManufacturers);
  bool get canEditManufacturers => hasPermission(perm.PermissionType.editManufacturers);
  bool get canDeleteManufacturers => hasPermission(perm.PermissionType.deleteManufacturers);

  bool get canViewModels => hasPermission(perm.PermissionType.viewModels);
  bool get canAddModels => hasPermission(perm.PermissionType.addModels);
  bool get canEditModels => hasPermission(perm.PermissionType.editModels);
  bool get canDeleteModels => hasPermission(perm.PermissionType.deleteModels);

  bool get canViewUsers => hasPermission(perm.PermissionType.viewUsers);
  bool get canAddUsers => hasPermission(perm.PermissionType.addUsers);
  bool get canEditUsers => hasPermission(perm.PermissionType.editUsers);
  bool get canDeleteUsers => hasPermission(perm.PermissionType.deleteUsers);
  bool get canChangeUserPasswords => hasPermission(perm.PermissionType.changeUserPasswords);
  bool get canViewUserLoginHistory => hasPermission(perm.PermissionType.viewUserLoginHistory);

  bool get canViewAdminPanel => hasPermission(perm.PermissionType.viewAdminPanel);
  bool get canManageBackups => hasPermission(perm.PermissionType.manageBackups);
  bool get canManageCloudStorage => hasPermission(perm.PermissionType.manageCloudStorage);
  bool get canViewSystemLogs => hasPermission(perm.PermissionType.viewSystemLogs);
  bool get canManageAppSettings => hasPermission(perm.PermissionType.manageAppSettings);

  bool get canCreateBackups => hasPermission(perm.PermissionType.createBackups);
  bool get canRestoreBackups => hasPermission(perm.PermissionType.restoreBackups);
  bool get canExportAllData => hasPermission(perm.PermissionType.exportAllData);
  bool get canImportAllData => hasPermission(perm.PermissionType.importAllData);

  bool get canManageFavorites => hasPermission(perm.PermissionType.manageFavorites);
  bool get canViewReports => hasPermission(perm.PermissionType.viewReports);
  bool get canManageNotifications => hasPermission(perm.PermissionType.manageNotifications);

  // Get user's effective role name
  String get userRoleName => _user?.effectiveRoleName ?? 'Unknown';
  String get userRoleNameAr => _user?.effectiveRoleNameAr ?? 'غير معروف';

  // Get all user permissions
  List<perm.PermissionType> get userPermissions => _user?.allPermissions ?? [];

  // Handle session expired
  void _handleSessionExpired() {
    debugPrint('User session expired, signing out');
    _setError('انتهت صلاحية جلستك. يرجى تسجيل الدخول مرة أخرى');
    signOut();
  }

  // Handle user deactivated
  void _handleUserDeactivated() {
    debugPrint('User account deactivated, signing out');
    _setError('تم إيقاف حسابك. يرجى التواصل مع المدير');
    signOut();
  }

  // Validate current session
  Future<bool> validateSession() async {
    return await SessionService.isSessionValid();
  }

  // Refresh session
  Future<void> refreshSession() async {
    await SessionService.refreshSession();
  }
}
