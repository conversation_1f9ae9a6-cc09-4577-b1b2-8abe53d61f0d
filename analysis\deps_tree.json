{"root": "hm_device_errors", "packages": [{"name": "hm_device_errors", "version": "1.0.6+8", "kind": "root", "source": "root", "dependencies": ["flutter", "flutter_localizations", "cupertino_icons", "firebase_core", "firebase_auth", "cloud_firestore", "firebase_storage", "firebase_messaging", "firebase_remote_config", "flutter_local_notifications", "provider", "flutter_svg", "cached_network_image", "image_picker", "file_picker", "flutter_staggered_grid_view", "device_info_plus", "package_info_plus", "googlea<PERSON>", "googleapis_auth", "url_launcher", "http", "shared_preferences", "geolocator", "permission_handler", "path_provider", "path", "flutter_spinkit", "shimmer", "intl", "flutter_localization", "excel", "share_plus", "encrypt", "crypto", "flutter_launcher_icons", "flutter_image_compress", "image", "flutter_test", "flutter_lints"], "directDependencies": ["flutter", "flutter_localizations", "cupertino_icons", "firebase_core", "firebase_auth", "cloud_firestore", "firebase_storage", "firebase_messaging", "firebase_remote_config", "flutter_local_notifications", "provider", "flutter_svg", "cached_network_image", "image_picker", "file_picker", "flutter_staggered_grid_view", "device_info_plus", "package_info_plus", "googlea<PERSON>", "googleapis_auth", "url_launcher", "http", "shared_preferences", "geolocator", "permission_handler", "path_provider", "path", "flutter_spinkit", "shimmer", "intl", "flutter_localization", "excel", "share_plus", "encrypt", "crypto", "flutter_launcher_icons", "flutter_image_compress", "image"], "devDependencies": ["flutter_test", "flutter_lints"]}, {"name": "flutter_lints", "version": "5.0.0", "kind": "dev", "source": "hosted", "dependencies": ["lints"], "directDependencies": ["lints"]}, {"name": "lints", "version": "5.1.1", "kind": "transitive", "source": "hosted", "dependencies": [], "directDependencies": []}, {"name": "flutter_test", "version": "0.0.0", "kind": "dev", "source": "sdk", "dependencies": ["flutter", "test_api", "matcher", "path", "fake_async", "clock", "stack_trace", "vector_math", "leak_tracker_flutter_testing", "async", "boolean_selector", "characters", "collection", "leak_tracker", "leak_tracker_testing", "material_color_utilities", "meta", "source_span", "stream_channel", "string_scanner", "term_glyph", "vm_service"], "directDependencies": ["flutter", "test_api", "matcher", "path", "fake_async", "clock", "stack_trace", "vector_math", "leak_tracker_flutter_testing", "async", "boolean_selector", "characters", "collection", "leak_tracker", "leak_tracker_testing", "material_color_utilities", "meta", "source_span", "stream_channel", "string_scanner", "term_glyph", "vm_service"]}, {"name": "vm_service", "version": "14.3.1", "kind": "transitive", "source": "hosted", "dependencies": [], "directDependencies": []}, {"name": "term_glyph", "version": "1.2.2", "kind": "transitive", "source": "hosted", "dependencies": [], "directDependencies": []}, {"name": "string_scanner", "version": "1.4.1", "kind": "transitive", "source": "hosted", "dependencies": ["source_span"], "directDependencies": ["source_span"]}, {"name": "source_span", "version": "1.10.1", "kind": "transitive", "source": "hosted", "dependencies": ["collection", "path", "term_glyph"], "directDependencies": ["collection", "path", "term_glyph"]}, {"name": "path", "version": "1.9.1", "kind": "direct", "source": "hosted", "dependencies": [], "directDependencies": []}, {"name": "collection", "version": "1.19.1", "kind": "transitive", "source": "hosted", "dependencies": [], "directDependencies": []}, {"name": "stream_channel", "version": "2.1.4", "kind": "transitive", "source": "hosted", "dependencies": ["async"], "directDependencies": ["async"]}, {"name": "async", "version": "2.12.0", "kind": "transitive", "source": "hosted", "dependencies": ["collection", "meta"], "directDependencies": ["collection", "meta"]}, {"name": "meta", "version": "1.16.0", "kind": "transitive", "source": "hosted", "dependencies": [], "directDependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "kind": "transitive", "source": "hosted", "dependencies": ["collection"], "directDependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "kind": "transitive", "source": "hosted", "dependencies": ["leak_tracker", "matcher", "meta"], "directDependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "matcher", "version": "0.12.17", "kind": "transitive", "source": "hosted", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"], "directDependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "kind": "transitive", "source": "hosted", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"], "directDependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "stack_trace", "version": "1.12.1", "kind": "transitive", "source": "hosted", "dependencies": ["path"], "directDependencies": ["path"]}, {"name": "boolean_selector", "version": "2.1.2", "kind": "transitive", "source": "hosted", "dependencies": ["source_span", "string_scanner"], "directDependencies": ["source_span", "string_scanner"]}, {"name": "leak_tracker", "version": "10.0.8", "kind": "transitive", "source": "hosted", "dependencies": ["clock", "collection", "meta", "path", "vm_service"], "directDependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "clock", "version": "1.1.2", "kind": "transitive", "source": "hosted", "dependencies": [], "directDependencies": []}, {"name": "characters", "version": "1.4.0", "kind": "transitive", "source": "hosted", "dependencies": [], "directDependencies": []}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"], "directDependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "flutter", "version": "0.0.0", "kind": "direct", "source": "sdk", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "vector_math", "sky_engine"], "directDependencies": ["characters", "collection", "material_color_utilities", "meta", "vector_math", "sky_engine"]}, {"name": "sky_engine", "version": "0.0.0", "kind": "transitive", "source": "sdk", "dependencies": [], "directDependencies": []}, {"name": "vector_math", "version": "2.1.4", "kind": "transitive", "source": "hosted", "dependencies": [], "directDependencies": []}, {"name": "fake_async", "version": "1.3.2", "kind": "transitive", "source": "hosted", "dependencies": ["clock", "collection"], "directDependencies": ["clock", "collection"]}, {"name": "image", "version": "4.3.0", "kind": "direct", "source": "hosted", "dependencies": ["archive", "meta", "xml"], "directDependencies": ["archive", "meta", "xml"]}, {"name": "xml", "version": "6.5.0", "kind": "transitive", "source": "hosted", "dependencies": ["collection", "meta", "petitparser"], "directDependencies": ["collection", "meta", "petitparser"]}, {"name": "petitparser", "version": "6.1.0", "kind": "transitive", "source": "hosted", "dependencies": ["meta", "collection"], "directDependencies": ["meta", "collection"]}, {"name": "archive", "version": "3.6.1", "kind": "transitive", "source": "hosted", "dependencies": ["crypto", "path"], "directDependencies": ["crypto", "path"]}, {"name": "crypto", "version": "3.0.6", "kind": "direct", "source": "hosted", "dependencies": ["typed_data"], "directDependencies": ["typed_data"]}, {"name": "typed_data", "version": "1.4.0", "kind": "transitive", "source": "hosted", "dependencies": ["collection"], "directDependencies": ["collection"]}, {"name": "flutter_image_compress", "version": "2.4.0", "kind": "direct", "source": "hosted", "dependencies": ["flutter", "flutter_image_compress_platform_interface", "flutter_image_compress_common", "flutter_image_compress_web", "flutter_image_compress_macos", "flutter_image_compress_ohos"], "directDependencies": ["flutter", "flutter_image_compress_platform_interface", "flutter_image_compress_common", "flutter_image_compress_web", "flutter_image_compress_macos", "flutter_image_compress_ohos"]}, {"name": "flutter_image_compress_ohos", "version": "0.0.3", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "flutter_image_compress_platform_interface"], "directDependencies": ["flutter", "flutter_image_compress_platform_interface"]}, {"name": "flutter_image_compress_platform_interface", "version": "1.0.5", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "plugin_platform_interface", "cross_file"], "directDependencies": ["flutter", "plugin_platform_interface", "cross_file"]}, {"name": "cross_file", "version": "0.3.4+2", "kind": "transitive", "source": "hosted", "dependencies": ["meta", "web"], "directDependencies": ["meta", "web"]}, {"name": "web", "version": "1.1.1", "kind": "transitive", "source": "hosted", "dependencies": [], "directDependencies": []}, {"name": "plugin_platform_interface", "version": "2.1.8", "kind": "transitive", "source": "hosted", "dependencies": ["meta"], "directDependencies": ["meta"]}, {"name": "flutter_image_compress_macos", "version": "1.0.3", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "flutter_image_compress_platform_interface"], "directDependencies": ["flutter", "flutter_image_compress_platform_interface"]}, {"name": "flutter_image_compress_web", "version": "0.1.5", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "flutter_web_plugins", "flutter_image_compress_platform_interface", "web"], "directDependencies": ["flutter", "flutter_web_plugins", "flutter_image_compress_platform_interface", "web"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "kind": "transitive", "source": "sdk", "dependencies": ["flutter", "characters", "collection", "material_color_utilities", "meta", "vector_math"], "directDependencies": ["flutter", "characters", "collection", "material_color_utilities", "meta", "vector_math"]}, {"name": "flutter_image_compress_common", "version": "1.0.6", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "flutter_image_compress_platform_interface"], "directDependencies": ["flutter", "flutter_image_compress_platform_interface"]}, {"name": "flutter_launcher_icons", "version": "0.13.1", "kind": "direct", "source": "hosted", "dependencies": ["args", "checked_yaml", "cli_util", "image", "json_annotation", "path", "yaml"], "directDependencies": ["args", "checked_yaml", "cli_util", "image", "json_annotation", "path", "yaml"]}, {"name": "yaml", "version": "3.1.3", "kind": "transitive", "source": "hosted", "dependencies": ["collection", "source_span", "string_scanner"], "directDependencies": ["collection", "source_span", "string_scanner"]}, {"name": "json_annotation", "version": "4.9.0", "kind": "transitive", "source": "hosted", "dependencies": ["meta"], "directDependencies": ["meta"]}, {"name": "cli_util", "version": "0.4.2", "kind": "transitive", "source": "hosted", "dependencies": ["meta", "path"], "directDependencies": ["meta", "path"]}, {"name": "checked_yaml", "version": "2.0.3", "kind": "transitive", "source": "hosted", "dependencies": ["json_annotation", "source_span", "yaml"], "directDependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "args", "version": "2.7.0", "kind": "transitive", "source": "hosted", "dependencies": [], "directDependencies": []}, {"name": "encrypt", "version": "5.0.3", "kind": "direct", "source": "hosted", "dependencies": ["args", "asn1lib", "clock", "collection", "crypto", "pointycastle"], "directDependencies": ["args", "asn1lib", "clock", "collection", "crypto", "pointycastle"]}, {"name": "pointycastle", "version": "3.9.1", "kind": "transitive", "source": "hosted", "dependencies": ["collection", "convert", "js"], "directDependencies": ["collection", "convert", "js"]}, {"name": "js", "version": "0.7.2", "kind": "transitive", "source": "hosted", "dependencies": [], "directDependencies": []}, {"name": "convert", "version": "3.1.2", "kind": "transitive", "source": "hosted", "dependencies": ["typed_data"], "directDependencies": ["typed_data"]}, {"name": "asn1lib", "version": "1.6.4", "kind": "transitive", "source": "hosted", "dependencies": [], "directDependencies": []}, {"name": "share_plus", "version": "7.2.2", "kind": "direct", "source": "hosted", "dependencies": ["cross_file", "meta", "mime", "flutter", "flutter_web_plugins", "share_plus_platform_interface", "file", "url_launcher_web", "url_launcher_windows", "url_launcher_linux", "url_launcher_platform_interface", "ffi", "win32"], "directDependencies": ["cross_file", "meta", "mime", "flutter", "flutter_web_plugins", "share_plus_platform_interface", "file", "url_launcher_web", "url_launcher_windows", "url_launcher_linux", "url_launcher_platform_interface", "ffi", "win32"]}, {"name": "win32", "version": "5.13.0", "kind": "transitive", "source": "hosted", "dependencies": ["ffi"], "directDependencies": ["ffi"]}, {"name": "ffi", "version": "2.1.4", "kind": "transitive", "source": "hosted", "dependencies": [], "directDependencies": []}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "plugin_platform_interface"], "directDependencies": ["flutter", "plugin_platform_interface"]}, {"name": "url_launcher_linux", "version": "3.2.1", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "url_launcher_platform_interface"], "directDependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_windows", "version": "3.1.4", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "url_launcher_platform_interface"], "directDependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_web", "version": "2.4.1", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"], "directDependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "file", "version": "7.0.1", "kind": "transitive", "source": "hosted", "dependencies": ["meta", "path"], "directDependencies": ["meta", "path"]}, {"name": "share_plus_platform_interface", "version": "3.4.0", "kind": "transitive", "source": "hosted", "dependencies": ["cross_file", "flutter", "meta", "mime", "plugin_platform_interface", "path_provider", "uuid"], "directDependencies": ["cross_file", "flutter", "meta", "mime", "plugin_platform_interface", "path_provider", "uuid"]}, {"name": "uuid", "version": "4.5.1", "kind": "transitive", "source": "hosted", "dependencies": ["crypto", "sprintf", "meta", "fixnum"], "directDependencies": ["crypto", "sprintf", "meta", "fixnum"]}, {"name": "fixnum", "version": "1.1.1", "kind": "transitive", "source": "hosted", "dependencies": [], "directDependencies": []}, {"name": "sprintf", "version": "7.0.0", "kind": "transitive", "source": "hosted", "dependencies": [], "directDependencies": []}, {"name": "path_provider", "version": "2.1.5", "kind": "direct", "source": "hosted", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"], "directDependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "path_provider_windows", "version": "2.3.0", "kind": "transitive", "source": "hosted", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"], "directDependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "platform", "plugin_platform_interface"], "directDependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "platform", "version": "3.1.6", "kind": "transitive", "source": "hosted", "dependencies": [], "directDependencies": []}, {"name": "path_provider_linux", "version": "2.2.1", "kind": "transitive", "source": "hosted", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"], "directDependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "xdg_directories", "version": "1.1.0", "kind": "transitive", "source": "hosted", "dependencies": ["meta", "path"], "directDependencies": ["meta", "path"]}, {"name": "path_provider_foundation", "version": "2.4.1", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "path_provider_platform_interface"], "directDependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.17", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "path_provider_platform_interface"], "directDependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "mime", "version": "1.0.6", "kind": "transitive", "source": "hosted", "dependencies": [], "directDependencies": []}, {"name": "excel", "version": "4.0.6", "kind": "direct", "source": "hosted", "dependencies": ["archive", "xml", "collection", "equatable"], "directDependencies": ["archive", "xml", "collection", "equatable"]}, {"name": "equatable", "version": "2.0.7", "kind": "transitive", "source": "hosted", "dependencies": ["collection", "meta"], "directDependencies": ["collection", "meta"]}, {"name": "flutter_localization", "version": "0.2.1", "kind": "direct", "source": "hosted", "dependencies": ["flutter", "flutter_web_plugins", "flutter_localizations", "plugin_platform_interface", "shared_preferences"], "directDependencies": ["flutter", "flutter_web_plugins", "flutter_localizations", "plugin_platform_interface", "shared_preferences"]}, {"name": "shared_preferences", "version": "2.5.3", "kind": "direct", "source": "hosted", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"], "directDependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "kind": "transitive", "source": "hosted", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"], "directDependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "plugin_platform_interface"], "directDependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"], "directDependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "kind": "transitive", "source": "hosted", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"], "directDependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "shared_preferences_platform_interface"], "directDependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.10", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "shared_preferences_platform_interface"], "directDependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "flutter_localizations", "version": "0.0.0", "kind": "direct", "source": "sdk", "dependencies": ["flutter", "intl", "characters", "clock", "collection", "material_color_utilities", "meta", "path", "vector_math"], "directDependencies": ["flutter", "intl", "characters", "clock", "collection", "material_color_utilities", "meta", "path", "vector_math"]}, {"name": "intl", "version": "0.19.0", "kind": "direct", "source": "hosted", "dependencies": ["clock", "meta", "path"], "directDependencies": ["clock", "meta", "path"]}, {"name": "shimmer", "version": "3.0.0", "kind": "direct", "source": "hosted", "dependencies": ["flutter"], "directDependencies": ["flutter"]}, {"name": "flutter_spinkit", "version": "5.2.1", "kind": "direct", "source": "hosted", "dependencies": ["flutter"], "directDependencies": ["flutter"]}, {"name": "permission_handler", "version": "11.4.0", "kind": "direct", "source": "hosted", "dependencies": ["flutter", "meta", "permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows", "permission_handler_platform_interface"], "directDependencies": ["flutter", "meta", "permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows", "permission_handler_platform_interface"]}, {"name": "permission_handler_platform_interface", "version": "4.3.0", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "meta", "plugin_platform_interface"], "directDependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_windows", "version": "0.2.1", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "permission_handler_platform_interface"], "directDependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_html", "version": "0.1.3+5", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "flutter_web_plugins", "permission_handler_platform_interface", "web"], "directDependencies": ["flutter", "flutter_web_plugins", "permission_handler_platform_interface", "web"]}, {"name": "permission_handler_apple", "version": "9.4.7", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "permission_handler_platform_interface"], "directDependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_android", "version": "12.1.0", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "permission_handler_platform_interface"], "directDependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "geolocator", "version": "10.1.1", "kind": "direct", "source": "hosted", "dependencies": ["flutter", "geolocator_platform_interface", "geolocator_android", "geolocator_apple", "geolocator_web", "geolocator_windows"], "directDependencies": ["flutter", "geolocator_platform_interface", "geolocator_android", "geolocator_apple", "geolocator_web", "geolocator_windows"]}, {"name": "geolocator_windows", "version": "0.2.5", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "geolocator_platform_interface"], "directDependencies": ["flutter", "geolocator_platform_interface"]}, {"name": "geolocator_platform_interface", "version": "4.2.6", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "plugin_platform_interface", "vector_math", "meta"], "directDependencies": ["flutter", "plugin_platform_interface", "vector_math", "meta"]}, {"name": "geolocator_web", "version": "2.2.1", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "flutter_web_plugins", "geolocator_platform_interface"], "directDependencies": ["flutter", "flutter_web_plugins", "geolocator_platform_interface"]}, {"name": "geolocator_apple", "version": "2.3.13", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "geolocator_platform_interface"], "directDependencies": ["flutter", "geolocator_platform_interface"]}, {"name": "geolocator_android", "version": "4.6.2", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "geolocator_platform_interface", "meta", "uuid"], "directDependencies": ["flutter", "geolocator_platform_interface", "meta", "uuid"]}, {"name": "http", "version": "1.4.0", "kind": "direct", "source": "hosted", "dependencies": ["async", "http_parser", "meta", "web"], "directDependencies": ["async", "http_parser", "meta", "web"]}, {"name": "http_parser", "version": "4.1.2", "kind": "transitive", "source": "hosted", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"], "directDependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "url_launcher", "version": "6.3.1", "kind": "direct", "source": "hosted", "dependencies": ["flutter", "url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows"], "directDependencies": ["flutter", "url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_macos", "version": "3.2.2", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "url_launcher_platform_interface"], "directDependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_ios", "version": "6.3.3", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "url_launcher_platform_interface"], "directDependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_android", "version": "6.3.16", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "url_launcher_platform_interface"], "directDependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "googleapis_auth", "version": "1.6.0", "kind": "direct", "source": "hosted", "dependencies": ["args", "crypto", "google_identity_services_web", "http", "http_parser"], "directDependencies": ["args", "crypto", "google_identity_services_web", "http", "http_parser"]}, {"name": "google_identity_services_web", "version": "0.3.3+1", "kind": "transitive", "source": "hosted", "dependencies": ["meta", "web"], "directDependencies": ["meta", "web"]}, {"name": "googlea<PERSON>", "version": "12.0.0", "kind": "direct", "source": "hosted", "dependencies": ["_discoveryapis_commons", "http"], "directDependencies": ["_discoveryapis_commons", "http"]}, {"name": "_discoveryapis_commons", "version": "1.0.7", "kind": "transitive", "source": "hosted", "dependencies": ["http", "http_parser", "meta"], "directDependencies": ["http", "http_parser", "meta"]}, {"name": "package_info_plus", "version": "8.3.0", "kind": "direct", "source": "hosted", "dependencies": ["ffi", "flutter", "flutter_web_plugins", "http", "meta", "path", "package_info_plus_platform_interface", "web", "win32", "clock"], "directDependencies": ["ffi", "flutter", "flutter_web_plugins", "http", "meta", "path", "package_info_plus_platform_interface", "web", "win32", "clock"]}, {"name": "package_info_plus_platform_interface", "version": "3.2.0", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "meta", "plugin_platform_interface"], "directDependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "device_info_plus", "version": "9.1.2", "kind": "direct", "source": "hosted", "dependencies": ["device_info_plus_platform_interface", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "win32", "win32_registry"], "directDependencies": ["device_info_plus_platform_interface", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "win32", "win32_registry"]}, {"name": "win32_registry", "version": "1.1.5", "kind": "transitive", "source": "hosted", "dependencies": ["ffi", "win32"], "directDependencies": ["ffi", "win32"]}, {"name": "device_info_plus_platform_interface", "version": "7.0.2", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "meta", "plugin_platform_interface"], "directDependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "flutter_staggered_grid_view", "version": "0.7.0", "kind": "direct", "source": "hosted", "dependencies": ["flutter"], "directDependencies": ["flutter"]}, {"name": "file_picker", "version": "8.3.7", "kind": "direct", "source": "hosted", "dependencies": ["flutter", "flutter_web_plugins", "flutter_plugin_android_lifecycle", "plugin_platform_interface", "ffi", "path", "win32", "cross_file", "web"], "directDependencies": ["flutter", "flutter_web_plugins", "flutter_plugin_android_lifecycle", "plugin_platform_interface", "ffi", "path", "win32", "cross_file", "web"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.28", "kind": "transitive", "source": "hosted", "dependencies": ["flutter"], "directDependencies": ["flutter"]}, {"name": "image_picker", "version": "1.1.2", "kind": "direct", "source": "hosted", "dependencies": ["flutter", "image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_platform_interface", "image_picker_windows"], "directDependencies": ["flutter", "image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_platform_interface", "image_picker_windows"]}, {"name": "image_picker_windows", "version": "0.2.1+1", "kind": "transitive", "source": "hosted", "dependencies": ["file_selector_platform_interface", "file_selector_windows", "flutter", "image_picker_platform_interface"], "directDependencies": ["file_selector_platform_interface", "file_selector_windows", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_platform_interface", "version": "2.10.1", "kind": "transitive", "source": "hosted", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"], "directDependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "file_selector_windows", "version": "0.9.3+4", "kind": "transitive", "source": "hosted", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"], "directDependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_platform_interface", "version": "2.6.2", "kind": "transitive", "source": "hosted", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"], "directDependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "image_picker_macos", "version": "0.2.1+2", "kind": "transitive", "source": "hosted", "dependencies": ["file_selector_macos", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"], "directDependencies": ["file_selector_macos", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "file_selector_macos", "version": "0.9.4+2", "kind": "transitive", "source": "hosted", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"], "directDependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "image_picker_linux", "version": "0.2.1+2", "kind": "transitive", "source": "hosted", "dependencies": ["file_selector_linux", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"], "directDependencies": ["file_selector_linux", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "file_selector_linux", "version": "0.9.3+2", "kind": "transitive", "source": "hosted", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"], "directDependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "image_picker_ios", "version": "0.8.12+2", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "image_picker_platform_interface"], "directDependencies": ["flutter", "image_picker_platform_interface"]}, {"name": "image_picker_for_web", "version": "3.0.6", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "flutter_web_plugins", "image_picker_platform_interface", "mime", "web"], "directDependencies": ["flutter", "flutter_web_plugins", "image_picker_platform_interface", "mime", "web"]}, {"name": "image_picker_android", "version": "0.8.12+23", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "image_picker_platform_interface"], "directDependencies": ["flutter", "flutter_plugin_android_lifecycle", "image_picker_platform_interface"]}, {"name": "cached_network_image", "version": "3.4.0", "kind": "direct", "source": "hosted", "dependencies": ["cached_network_image_platform_interface", "cached_network_image_web", "flutter", "flutter_cache_manager", "octo_image"], "directDependencies": ["cached_network_image_platform_interface", "cached_network_image_web", "flutter", "flutter_cache_manager", "octo_image"]}, {"name": "octo_image", "version": "2.1.0", "kind": "transitive", "source": "hosted", "dependencies": ["flutter"], "directDependencies": ["flutter"]}, {"name": "flutter_cache_manager", "version": "3.4.1", "kind": "transitive", "source": "hosted", "dependencies": ["clock", "collection", "file", "flutter", "http", "path", "path_provider", "rxdart", "sqflite", "uuid"], "directDependencies": ["clock", "collection", "file", "flutter", "http", "path", "path_provider", "rxdart", "sqflite", "uuid"]}, {"name": "sqflite", "version": "2.4.2", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "sqflite_android", "sqflite_darwin", "sqflite_platform_interface", "sqflite_common", "path"], "directDependencies": ["flutter", "sqflite_android", "sqflite_darwin", "sqflite_platform_interface", "sqflite_common", "path"]}, {"name": "sqflite_common", "version": "2.5.5", "kind": "transitive", "source": "hosted", "dependencies": ["synchronized", "path", "meta"], "directDependencies": ["synchronized", "path", "meta"]}, {"name": "synchronized", "version": "3.3.1", "kind": "transitive", "source": "hosted", "dependencies": [], "directDependencies": []}, {"name": "sqflite_platform_interface", "version": "2.4.0", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "platform", "sqflite_common", "plugin_platform_interface", "meta"], "directDependencies": ["flutter", "platform", "sqflite_common", "plugin_platform_interface", "meta"]}, {"name": "sqflite_darwin", "version": "2.4.2", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "sqflite_platform_interface", "meta", "sqflite_common", "path"], "directDependencies": ["flutter", "sqflite_platform_interface", "meta", "sqflite_common", "path"]}, {"name": "sqflite_android", "version": "2.4.1", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "sqflite_common", "path", "sqflite_platform_interface"], "directDependencies": ["flutter", "sqflite_common", "path", "sqflite_platform_interface"]}, {"name": "rxdart", "version": "0.28.0", "kind": "transitive", "source": "hosted", "dependencies": [], "directDependencies": []}, {"name": "cached_network_image_web", "version": "1.3.0", "kind": "transitive", "source": "hosted", "dependencies": ["cached_network_image_platform_interface", "flutter", "flutter_cache_manager"], "directDependencies": ["cached_network_image_platform_interface", "flutter", "flutter_cache_manager"]}, {"name": "cached_network_image_platform_interface", "version": "4.1.1", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "flutter_cache_manager"], "directDependencies": ["flutter", "flutter_cache_manager"]}, {"name": "flutter_svg", "version": "2.1.0", "kind": "direct", "source": "hosted", "dependencies": ["flutter", "http", "vector_graphics", "vector_graphics_codec", "vector_graphics_compiler"], "directDependencies": ["flutter", "http", "vector_graphics", "vector_graphics_codec", "vector_graphics_compiler"]}, {"name": "vector_graphics_compiler", "version": "1.1.17", "kind": "transitive", "source": "hosted", "dependencies": ["args", "meta", "path", "path_parsing", "vector_graphics_codec", "xml"], "directDependencies": ["args", "meta", "path", "path_parsing", "vector_graphics_codec", "xml"]}, {"name": "vector_graphics_codec", "version": "1.1.13", "kind": "transitive", "source": "hosted", "dependencies": [], "directDependencies": []}, {"name": "path_parsing", "version": "1.1.0", "kind": "transitive", "source": "hosted", "dependencies": ["meta", "vector_math"], "directDependencies": ["meta", "vector_math"]}, {"name": "vector_graphics", "version": "1.1.18", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "http", "vector_graphics_codec"], "directDependencies": ["flutter", "http", "vector_graphics_codec"]}, {"name": "provider", "version": "6.1.5", "kind": "direct", "source": "hosted", "dependencies": ["collection", "flutter", "nested"], "directDependencies": ["collection", "flutter", "nested"]}, {"name": "nested", "version": "1.0.0", "kind": "transitive", "source": "hosted", "dependencies": ["flutter"], "directDependencies": ["flutter"]}, {"name": "flutter_local_notifications", "version": "17.2.4", "kind": "direct", "source": "hosted", "dependencies": ["clock", "flutter", "flutter_local_notifications_linux", "flutter_local_notifications_platform_interface", "timezone"], "directDependencies": ["clock", "flutter", "flutter_local_notifications_linux", "flutter_local_notifications_platform_interface", "timezone"]}, {"name": "timezone", "version": "0.9.4", "kind": "transitive", "source": "hosted", "dependencies": ["path"], "directDependencies": ["path"]}, {"name": "flutter_local_notifications_platform_interface", "version": "7.2.0", "kind": "transitive", "source": "hosted", "dependencies": ["flutter", "plugin_platform_interface"], "directDependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_local_notifications_linux", "version": "4.0.1", "kind": "transitive", "source": "hosted", "dependencies": ["dbus", "ffi", "flutter", "flutter_local_notifications_platform_interface", "path", "xdg_directories"], "directDependencies": ["dbus", "ffi", "flutter", "flutter_local_notifications_platform_interface", "path", "xdg_directories"]}, {"name": "dbus", "version": "0.7.11", "kind": "transitive", "source": "hosted", "dependencies": ["args", "ffi", "meta", "xml"], "directDependencies": ["args", "ffi", "meta", "xml"]}, {"name": "firebase_remote_config", "version": "5.4.4", "kind": "direct", "source": "hosted", "dependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_remote_config_platform_interface", "firebase_remote_config_web", "flutter"], "directDependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_remote_config_platform_interface", "firebase_remote_config_web", "flutter"]}, {"name": "firebase_remote_config_web", "version": "1.8.4", "kind": "transitive", "source": "hosted", "dependencies": ["_flutterfire_internals", "firebase_core", "firebase_core_web", "firebase_remote_config_platform_interface", "flutter", "flutter_web_plugins"], "directDependencies": ["_flutterfire_internals", "firebase_core", "firebase_core_web", "firebase_remote_config_platform_interface", "flutter", "flutter_web_plugins"]}, {"name": "firebase_remote_config_platform_interface", "version": "1.5.4", "kind": "transitive", "source": "hosted", "dependencies": ["_flutterfire_internals", "firebase_core", "flutter", "meta", "plugin_platform_interface"], "directDependencies": ["_flutterfire_internals", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_core", "version": "3.13.1", "kind": "direct", "source": "hosted", "dependencies": ["firebase_core_platform_interface", "firebase_core_web", "flutter", "meta"], "directDependencies": ["firebase_core_platform_interface", "firebase_core_web", "flutter", "meta"]}, {"name": "firebase_core_web", "version": "2.23.0", "kind": "transitive", "source": "hosted", "dependencies": ["firebase_core_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"], "directDependencies": ["firebase_core_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "firebase_core_platform_interface", "version": "5.4.0", "kind": "transitive", "source": "hosted", "dependencies": ["collection", "flutter", "flutter_test", "meta", "plugin_platform_interface"], "directDependencies": ["collection", "flutter", "flutter_test", "meta", "plugin_platform_interface"]}, {"name": "_flutterfire_internals", "version": "1.3.55", "kind": "transitive", "source": "hosted", "dependencies": ["collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"], "directDependencies": ["collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "firebase_messaging", "version": "15.2.6", "kind": "direct", "source": "hosted", "dependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_messaging_platform_interface", "firebase_messaging_web", "flutter", "meta"], "directDependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_messaging_platform_interface", "firebase_messaging_web", "flutter", "meta"]}, {"name": "firebase_messaging_web", "version": "3.10.6", "kind": "transitive", "source": "hosted", "dependencies": ["_flutterfire_internals", "firebase_core", "firebase_core_web", "firebase_messaging_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"], "directDependencies": ["_flutterfire_internals", "firebase_core", "firebase_core_web", "firebase_messaging_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "firebase_messaging_platform_interface", "version": "4.6.6", "kind": "transitive", "source": "hosted", "dependencies": ["_flutterfire_internals", "firebase_core", "flutter", "meta", "plugin_platform_interface"], "directDependencies": ["_flutterfire_internals", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_storage", "version": "12.4.6", "kind": "direct", "source": "hosted", "dependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_storage_platform_interface", "firebase_storage_web", "flutter"], "directDependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_storage_platform_interface", "firebase_storage_web", "flutter"]}, {"name": "firebase_storage_web", "version": "3.10.13", "kind": "transitive", "source": "hosted", "dependencies": ["_flutterfire_internals", "async", "firebase_core", "firebase_core_web", "firebase_storage_platform_interface", "flutter", "flutter_web_plugins", "http", "meta", "web"], "directDependencies": ["_flutterfire_internals", "async", "firebase_core", "firebase_core_web", "firebase_storage_platform_interface", "flutter", "flutter_web_plugins", "http", "meta", "web"]}, {"name": "firebase_storage_platform_interface", "version": "5.2.6", "kind": "transitive", "source": "hosted", "dependencies": ["_flutterfire_internals", "collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"], "directDependencies": ["_flutterfire_internals", "collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "cloud_firestore", "version": "5.6.8", "kind": "direct", "source": "hosted", "dependencies": ["cloud_firestore_platform_interface", "cloud_firestore_web", "collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"], "directDependencies": ["cloud_firestore_platform_interface", "cloud_firestore_web", "collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "cloud_firestore_web", "version": "4.4.8", "kind": "transitive", "source": "hosted", "dependencies": ["_flutterfire_internals", "cloud_firestore_platform_interface", "collection", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins"], "directDependencies": ["_flutterfire_internals", "cloud_firestore_platform_interface", "collection", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins"]}, {"name": "cloud_firestore_platform_interface", "version": "6.6.8", "kind": "transitive", "source": "hosted", "dependencies": ["_flutterfire_internals", "collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"], "directDependencies": ["_flutterfire_internals", "collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_auth", "version": "5.5.4", "kind": "direct", "source": "hosted", "dependencies": ["firebase_auth_platform_interface", "firebase_auth_web", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"], "directDependencies": ["firebase_auth_platform_interface", "firebase_auth_web", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "firebase_auth_web", "version": "5.14.3", "kind": "transitive", "source": "hosted", "dependencies": ["firebase_auth_platform_interface", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins", "http_parser", "meta", "web"], "directDependencies": ["firebase_auth_platform_interface", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins", "http_parser", "meta", "web"]}, {"name": "firebase_auth_platform_interface", "version": "7.6.3", "kind": "transitive", "source": "hosted", "dependencies": ["_flutterfire_internals", "collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"], "directDependencies": ["_flutterfire_internals", "collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "cupertino_icons", "version": "1.0.8", "kind": "direct", "source": "hosted", "dependencies": [], "directDependencies": []}], "sdks": [{"name": "Dart", "version": "3.7.2"}, {"name": "Flutter", "version": "3.29.2"}], "executables": ["encrypt:secure-random", "flutter_launcher_icons", "flutter_launcher_icons:generate", "flutter_launcher_icons:main"]}