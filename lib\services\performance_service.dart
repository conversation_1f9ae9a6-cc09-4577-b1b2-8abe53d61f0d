import 'dart:io';
import 'dart:isolate';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image/image.dart' as img;
import '../utils/path_utils.dart';
import '../utils/platform_utils.dart';

class PerformanceService {
  static const int _maxCacheSize = 30 * 1024 * 1024; // Reduced to 30 MB for better performance
  static const int _maxCacheItems = 500; // Reduced cache items for faster access
  static final Map<String, dynamic> _memoryCache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};

  // Performance monitoring
  static final List<double> _frameRates = [];
  static Timer? _performanceTimer;
  static DateTime? _lastFrameTime;
  static int _frameCount = 0;

  // تحسين الذاكرة
  static void optimizeMemory() {
    // تنظيف الكاش القديم
    _cleanOldCache();

    // إجبار garbage collection
    if (!kReleaseMode) {
      // فقط في وضع التطوير
      debugPrint('Memory optimization triggered');
    }
  }

  // تنظيف الكاش القديم
  static void _cleanOldCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    _cacheTimestamps.forEach((key, timestamp) {
      if (now.difference(timestamp).inMinutes > 30) {
        expiredKeys.add(key);
      }
    });

    for (final key in expiredKeys) {
      _memoryCache.remove(key);
      _cacheTimestamps.remove(key);
    }

    // إذا كان الكاش كبير جداً، احذف الأقدم
    if (_memoryCache.length > _maxCacheItems) {
      final sortedKeys = _cacheTimestamps.keys.toList()
        ..sort((a, b) => _cacheTimestamps[a]!.compareTo(_cacheTimestamps[b]!));

      final keysToRemove = sortedKeys.take(_memoryCache.length - _maxCacheItems);
      for (final key in keysToRemove) {
        _memoryCache.remove(key);
        _cacheTimestamps.remove(key);
      }
    }
  }

  // حفظ في الكاش
  static void cacheData(String key, dynamic data) {
    // التحقق من حجم الكاش
    final currentSize = _estimateMemoryUsage();
    if (currentSize > _maxCacheSize) {
      _cleanOldCache();
    }

    _memoryCache[key] = data;
    _cacheTimestamps[key] = DateTime.now();

    // تنظيف دوري
    if (_memoryCache.length % 100 == 0) {
      _cleanOldCache();
    }
  }

  // استرجاع من الكاش
  static T? getCachedData<T>(String key) {
    if (_memoryCache.containsKey(key)) {
      final timestamp = _cacheTimestamps[key];
      if (timestamp != null && DateTime.now().difference(timestamp).inMinutes < 30) {
        return _memoryCache[key] as T?;
      } else {
        // انتهت صلاحية الكاش
        _memoryCache.remove(key);
        _cacheTimestamps.remove(key);
      }
    }
    return null;
  }

  // مسح الكاش
  static void clearCache() {
    _memoryCache.clear();
    _cacheTimestamps.clear();
  }

  // ضغط الصور المتقدم
  static Future<Uint8List> compressImage(Uint8List imageData, {int quality = 85, int? maxWidth, int? maxHeight}) async {
    try {
      // استخدام isolate لضغط الصور في خيط منفصل
      final result = await Isolate.run(() => _compressImageInIsolate(imageData, quality, maxWidth, maxHeight));
      return result;
    } catch (e) {
      debugPrint('Error compressing image: $e');
      return imageData; // إرجاع الصورة الأصلية في حالة الخطأ
    }
  }

  // ضغط الصورة في isolate منفصل
  static Uint8List _compressImageInIsolate(Uint8List imageData, int quality, int? maxWidth, int? maxHeight) {
    try {
      // فك تشفير الصورة
      final image = img.decodeImage(imageData);
      if (image == null) {
        return imageData;
      }

      // تغيير حجم الصورة إذا لزم الأمر
      img.Image resizedImage = image;
      if (maxWidth != null || maxHeight != null) {
        final targetWidth = maxWidth ?? image.width;
        final targetHeight = maxHeight ?? image.height;

        // حساب النسبة للحفاظ على نسبة العرض إلى الارتفاع
        final widthRatio = targetWidth / image.width;
        final heightRatio = targetHeight / image.height;
        final ratio = widthRatio < heightRatio ? widthRatio : heightRatio;

        final newWidth = (image.width * ratio).round();
        final newHeight = (image.height * ratio).round();

        resizedImage = img.copyResize(image, width: newWidth, height: newHeight);
      }

      // ضغط الصورة
      final compressedData = img.encodeJpg(resizedImage, quality: quality);
      return Uint8List.fromList(compressedData);
    } catch (e) {
      debugPrint('Error in image compression isolate: $e');
      return imageData;
    }
  }

  // ضغط صورة من ملف
  static Future<Uint8List?> compressImageFile(String filePath, {int quality = 85, int? maxWidth, int? maxHeight}) async {
    try {
      final result = await FlutterImageCompress.compressWithFile(
        filePath,
        quality: quality,
        minWidth: maxWidth ?? 1920,
        minHeight: maxHeight ?? 1080,
      );
      return result;
    } catch (e) {
      debugPrint('Error compressing image file: $e');
      return null;
    }
  }

  // ضغط متعدد الصور
  static Future<List<Uint8List>> compressMultipleImages(List<Uint8List> images, {int quality = 85}) async {
    try {
      final results = await Future.wait(
        images.map((imageData) => compressImage(imageData, quality: quality)),
      );
      return results;
    } catch (e) {
      debugPrint('Error compressing multiple images: $e');
      return images;
    }
  }

  // تحسين قاعدة البيانات
  static Future<void> optimizeDatabase() async {
    try {
      // تنظيف البيانات المؤقتة
      await _cleanTempData();

      // ضغط قاعدة البيانات (إذا كانت SQLite)
      // await _compressDatabase();

      debugPrint('Database optimization completed');
    } catch (e) {
      debugPrint('Error optimizing database: $e');
    }
  }

  // تنظيف البيانات المؤقتة
  static Future<void> _cleanTempData() async {
    if (PlatformUtils.isWeb) {
      // Web doesn't support file operations
      return;
    }

    try {
      final tempDir = await PathUtils.getTemporaryDirectory();
      if (await tempDir.exists()) {
        await for (final entity in tempDir.list()) {
          if (entity is File) {
            final stat = await entity.stat();
            // حذف الملفات الأقدم من يوم واحد
            if (DateTime.now().difference(stat.modified).inDays > 1) {
              await entity.delete();
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error cleaning temp data: $e');
    }
  }

  // تحسين الشبكة
  static Future<T?> optimizedNetworkCall<T>(
    Future<T> Function() networkCall, {
    String? cacheKey,
    Duration cacheDuration = const Duration(minutes: 5),
  }) async {
    // التحقق من الكاش أولاً
    if (cacheKey != null) {
      final cached = getCachedData<T>(cacheKey);
      if (cached != null) {
        return cached;
      }
    }

    try {
      final result = await networkCall();

      // حفظ في الكاش
      if (cacheKey != null && result != null) {
        cacheData(cacheKey, result);
      }

      return result;
    } catch (e) {
      debugPrint('Network call failed: $e');
      return null;
    }
  }

  // تحسين تحميل الصور
  static Future<Uint8List?> optimizedImageLoad(String url, {String? cacheKey}) async {
    final key = cacheKey ?? 'image_$url';

    // التحقق من الكاش
    final cached = getCachedData<Uint8List>(key);
    if (cached != null) {
      return cached;
    }

    try {
      // تحميل الصورة
      final response = await HttpClient().getUrl(Uri.parse(url));
      final httpResponse = await response.close();
      final bytes = await consolidateHttpClientResponseBytes(httpResponse);

      // ضغط الصورة
      final compressedBytes = await compressImage(bytes);

      // حفظ في الكاش
      cacheData(key, compressedBytes);

      return compressedBytes;
    } catch (e) {
      debugPrint('Error loading image: $e');
      return null;
    }
  }

  // تحسين الأداء العام
  static Future<void> performGeneralOptimization() async {
    try {
      // تحسين الذاكرة
      optimizeMemory();

      // تحسين قاعدة البيانات
      await optimizeDatabase();

      // تنظيف الملفات المؤقتة
      await _cleanTempData();

      debugPrint('General optimization completed');
    } catch (e) {
      debugPrint('Error during general optimization: $e');
    }
  }

  // مراقبة الأداء
  static Map<String, dynamic> getPerformanceMetrics() {
    return {
      'cache_size': _memoryCache.length,
      'cache_items': _memoryCache.keys.length,
      'memory_usage': _estimateMemoryUsage(),
      'last_optimization': DateTime.now().toIso8601String(),
    };
  }

  // تقدير استخدام الذاكرة
  static int _estimateMemoryUsage() {
    int totalSize = 0;

    _memoryCache.forEach((key, value) {
      if (value is String) {
        totalSize += value.length * 2; // UTF-16
      } else if (value is Uint8List) {
        totalSize += value.length;
      } else if (value is List) {
        totalSize += value.length * 8; // تقدير
      } else {
        totalSize += 100; // تقدير للكائنات الأخرى
      }
    });

    return totalSize;
  }

  // تحسين البناء
  static Future<void> optimizeBuild() async {
    try {
      // تنظيف الكاش
      clearCache();

      // تحسين الذاكرة
      optimizeMemory();

      // تحضير البيانات المهمة
      await _preloadCriticalData();

      debugPrint('Build optimization completed');
    } catch (e) {
      debugPrint('Error during build optimization: $e');
    }
  }

  // تحميل البيانات المهمة مسبقاً
  static Future<void> _preloadCriticalData() async {
    try {
      // تحميل الخطوط
      await _preloadFonts();

      // تحميل الصور المهمة
      await _preloadImages();

    } catch (e) {
      debugPrint('Error preloading critical data: $e');
    }
  }

  // تحميل الخطوط مسبقاً
  static Future<void> _preloadFonts() async {
    try {
      // تحميل خطوط AwanZaman المتوفرة فقط
      final fontFiles = [
        'assets/fonts/AwanZaman-Regular.ttf',
        'assets/fonts/AwanZaman-Bold.ttf',
        // تم إزالة Light و Medium لتقليل حجم التطبيق
      ];

      for (final fontFile in fontFiles) {
        try {
          await rootBundle.load(fontFile);
          debugPrint('Preloaded font: $fontFile');
        } catch (e) {
          debugPrint('Error preloading font $fontFile: $e');
        }
      }
    } catch (e) {
      debugPrint('Error preloading fonts: $e');
    }
  }

  // تحميل الصور المهمة مسبقاً
  static Future<void> _preloadImages() async {
    try {
      // تحميل اللوجو
      await rootBundle.load('assets/images/logo.svg');
    } catch (e) {
      debugPrint('Error preloading images: $e');
    }
  }

  // تحسين حجم التطبيق
  static Future<Map<String, dynamic>> getAppSizeInfo() async {
    try {
      if (PlatformUtils.isWeb) {
        // Web doesn't support directory size calculation
        return {
          'app_size': 0,
          'cache_size': 0,
          'total_size': 0,
          'formatted_app_size': '0 B',
          'formatted_cache_size': '0 B',
          'formatted_total_size': '0 B',
        };
      }

      final appDir = await PathUtils.getApplicationDocumentsDirectory();
      final cacheDir = await PathUtils.getTemporaryDirectory();

      final appSize = await PathUtils.calculateDirectorySize(appDir);
      final cacheSize = await PathUtils.calculateDirectorySize(cacheDir);

      return {
        'app_size': appSize,
        'cache_size': cacheSize,
        'total_size': appSize + cacheSize,
        'formatted_app_size': _formatBytes(appSize),
        'formatted_cache_size': _formatBytes(cacheSize),
        'formatted_total_size': _formatBytes(appSize + cacheSize),
      };
    } catch (e) {
      debugPrint('Error getting app size info: $e');
      return {};
    }
  }



  // تنسيق البايتات
  static String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  // Start performance monitoring
  static void startPerformanceMonitoring() {
    _performanceTimer?.cancel();
    _performanceTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _recordFrameRate();
    });
    _lastFrameTime = DateTime.now();
    _frameCount = 0;
  }

  // Stop performance monitoring
  static void stopPerformanceMonitoring() {
    _performanceTimer?.cancel();
    _performanceTimer = null;
  }

  // Record frame rate
  static void _recordFrameRate() {
    final now = DateTime.now();
    if (_lastFrameTime != null) {
      final timeDiff = now.difference(_lastFrameTime!).inMilliseconds;
      if (timeDiff > 0) {
        final fps = 1000.0 / timeDiff;
        _frameRates.add(fps);

        // Keep only last 60 measurements
        if (_frameRates.length > 60) {
          _frameRates.removeAt(0);
        }
      }
    }
    _lastFrameTime = now;
    _frameCount++;
  }

  // Get average frame rate
  static double getAverageFrameRate() {
    if (_frameRates.isEmpty) return 0.0;
    return _frameRates.reduce((a, b) => a + b) / _frameRates.length;
  }

  // Get performance report
  static Map<String, dynamic> getPerformanceReport() {
    final metrics = getPerformanceMetrics();
    return {
      ...metrics,
      'average_fps': getAverageFrameRate(),
      'frame_count': _frameCount,
      'frame_rates': List.from(_frameRates),
      'is_monitoring': _performanceTimer != null,
    };
  }

  // Optimize app for low-end devices
  static void optimizeForLowEndDevice() {
    // More aggressive cache size reduction for low-end devices
    const lowEndCacheSize = 10 * 1024 * 1024; // Reduced to 10MB
    const lowEndCacheItems = 200; // Reduced to 200 items

    // Clear excess cache
    if (_estimateMemoryUsage() > lowEndCacheSize) {
      final excessItems = _memoryCache.length - lowEndCacheItems;
      if (excessItems > 0) {
        final sortedKeys = _cacheTimestamps.keys.toList()
          ..sort((a, b) => _cacheTimestamps[a]!.compareTo(_cacheTimestamps[b]!));

        for (int i = 0; i < excessItems; i++) {
          final key = sortedKeys[i];
          _memoryCache.remove(key);
          _cacheTimestamps.remove(key);
        }
      }
    }

    debugPrint('App optimized for low-end device - Cache reduced to ${_formatBytes(_estimateMemoryUsage())}');
  }

  // Optimize app for size reduction
  static void optimizeForSize() {
    // Clear all non-essential cache
    final essentialKeys = _memoryCache.keys.where((key) =>
      key.contains('critical') ||
      key.contains('essential') ||
      key.contains('user_data')
    ).toList();

    final keysToRemove = _memoryCache.keys.where((key) =>
      !essentialKeys.contains(key)
    ).toList();

    for (final key in keysToRemove) {
      _memoryCache.remove(key);
      _cacheTimestamps.remove(key);
    }

    debugPrint('App optimized for size - Removed ${keysToRemove.length} cache items');
  }

  // Get memory usage statistics
  static Map<String, dynamic> getMemoryStats() {
    final memoryUsage = _estimateMemoryUsage();
    final itemCount = _memoryCache.length;

    return {
      'memoryUsage': memoryUsage,
      'formattedMemoryUsage': _formatBytes(memoryUsage),
      'itemCount': itemCount,
      'averageItemSize': itemCount > 0 ? memoryUsage ~/ itemCount : 0,
      'cacheEfficiency': itemCount > 0 ? (memoryUsage / (itemCount * 1024)).toStringAsFixed(2) : '0',
    };
  }



  // Check if device is low-end
  static bool isLowEndDevice() {
    // This is a simple heuristic - in a real app you might want to check
    // actual device specs using device_info_plus
    return _estimateMemoryUsage() > _maxCacheSize * 0.8;
  }

  // Auto-optimize based on performance
  static void autoOptimize() {
    final avgFps = getAverageFrameRate();
    final memoryUsage = _estimateMemoryUsage();

    // If FPS is low or memory usage is high, optimize
    if (avgFps < 30 || memoryUsage > _maxCacheSize * 0.7) {
      optimizeMemory();

      if (isLowEndDevice()) {
        optimizeForLowEndDevice();
      }

      debugPrint('Auto-optimization triggered: FPS=$avgFps, Memory=${_formatBytes(memoryUsage)}');
    }
  }
}
