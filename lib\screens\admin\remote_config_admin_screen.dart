import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/remote_config_provider.dart';
import '../../providers/locale_provider.dart';
import 'config_source_management_screen.dart';

class RemoteConfigAdminScreen extends StatefulWidget {
  const RemoteConfigAdminScreen({super.key});

  @override
  State<RemoteConfigAdminScreen> createState() => _RemoteConfigAdminScreenState();
}

class _RemoteConfigAdminScreenState extends State<RemoteConfigAdminScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadRemoteConfig();
    });
  }

  Future<void> _loadRemoteConfig() async {
    final provider = Provider.of<RemoteConfigProvider>(context, listen: false);
    if (!provider.isInitialized) {
      await provider.initialize();
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isRTL ? 'إدارة التكوين البعيد' : 'Remote Config Management',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _openConfigSourceManagement,
            tooltip: isRTL ? 'إدارة مصدر التكوين' : 'Manage Config Source',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshConfig,
            tooltip: isRTL ? 'تحديث التكوين' : 'Refresh Config',
          ),
        ],
      ),
      body: Consumer<RemoteConfigProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (provider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    isRTL ? 'خطأ في تحميل التكوين البعيد' : 'Error loading remote config',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    provider.error!,
                    style: TextStyle(color: Colors.red[600]),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _refreshConfig,
                    child: Text(isRTL ? 'إعادة المحاولة' : 'Retry'),
                  ),
                ],
              ),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildStatusCard(provider, isRTL),
                const SizedBox(height: 16),
                _buildGoogleDriveConfigCard(provider, isRTL),
                const SizedBox(height: 16),
                _buildConfigKeysCard(provider, isRTL),
                const SizedBox(height: 16),
                _buildDebugInfoCard(provider, isRTL),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatusCard(RemoteConfigProvider provider, bool isRTL) {
    final status = provider.getConfigurationStatus();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue[600],
                ),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'حالة التكوين' : 'Configuration Status',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatusRow(
              isRTL ? 'تم تهيئة التكوين البعيد' : 'Remote Config Initialized',
              status['remote_config_initialized'] ?? false,
              isRTL,
            ),
            _buildStatusRow(
              isRTL ? 'Google Drive مفعل' : 'Google Drive Enabled',
              status['google_drive_enabled'] ?? false,
              isRTL,
            ),
            _buildStatusRow(
              isRTL ? 'Google Drive مكون' : 'Google Drive Configured',
              status['google_drive_configured'] ?? false,
              isRTL,
            ),
            _buildStatusRow(
              isRTL ? 'التكوين البعيد كمصدر أساسي' : 'Remote Config as Primary',
              status['use_remote_config_primary'] ?? false,
              isRTL,
            ),
            const SizedBox(height: 8),
            Text(
              '${isRTL ? 'مصدر التكوين النشط: ' : 'Active Config Source: '}${status['config_source'] ?? 'N/A'}',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
            Text(
              '${isRTL ? 'تفضيل المصدر: ' : 'Source Preference: '}${status['config_source_preference'] ?? 'N/A'}',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, bool value, bool isRTL) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Icon(
            value ? Icons.check_circle : Icons.cancel,
            color: value ? Colors.green : Colors.red,
            size: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildGoogleDriveConfigCard(RemoteConfigProvider provider, bool isRTL) {
    final config = provider.googleDriveConfig;
    final errors = provider.getValidationErrors();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.cloud,
                  color: Colors.green[600],
                ),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'تكوين Google Drive' : 'Google Drive Configuration',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (config != null) ...[
              _buildConfigRow(
                isRTL ? 'معرف العميل' : 'Client ID',
                config.clientId.isNotEmpty ? '***' : 'Not set',
                config.clientId.isNotEmpty,
                isRTL,
              ),
              _buildConfigRow(
                isRTL ? 'بريد الخدمة' : 'Service Email',
                config.clientEmail?.isNotEmpty == true ? '***' : 'Not set',
                config.clientEmail?.isNotEmpty == true,
                isRTL,
              ),
              _buildConfigRow(
                isRTL ? 'معرف المشروع' : 'Project ID',
                config.projectId?.isNotEmpty == true ? '***' : 'Not set',
                config.projectId?.isNotEmpty == true,
                isRTL,
              ),
              _buildConfigRow(
                isRTL ? 'المفتاح الخاص' : 'Private Key',
                config.privateKey?.isNotEmpty == true ? '***' : 'Not set',
                config.privateKey?.isNotEmpty == true,
                isRTL,
              ),
              if (config.parentFolderId?.isNotEmpty == true)
                _buildConfigRow(
                  isRTL ? 'مجلد الوالد' : 'Parent Folder',
                  '***',
                  true,
                  isRTL,
                ),
            ] else ...[
              Text(
                isRTL ? 'لم يتم العثور على تكوين Google Drive' : 'Google Drive configuration not found',
                style: TextStyle(color: Colors.orange[600]),
              ),
            ],
            if (errors.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                isRTL ? 'أخطاء التحقق:' : 'Validation Errors:',
                style: TextStyle(
                  color: Colors.red[600],
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...errors.map((error) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Text(
                  '• $error',
                  style: TextStyle(color: Colors.red[600]),
                ),
              )),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildConfigRow(String label, String value, bool isValid, bool isRTL) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Row(
            children: [
              Text(
                value,
                style: TextStyle(
                  color: isValid ? Colors.green[600] : Colors.red[600],
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                isValid ? Icons.check_circle : Icons.cancel,
                color: isValid ? Colors.green : Colors.red,
                size: 16,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildConfigKeysCard(RemoteConfigProvider provider, bool isRTL) {
    final keys = provider.getGoogleDriveConfigKeys();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.vpn_key,
                  color: Colors.orange[600],
                ),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'مفاتيح التكوين المطلوبة' : 'Required Configuration Keys',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              isRTL
                ? 'يجب تعيين هذه المفاتيح في Firebase Remote Config:'
                : 'These keys must be set in Firebase Remote Config:',
              style: TextStyle(color: Colors.grey[700]),
            ),
            const SizedBox(height: 12),
            ...keys.entries.map((entry) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    entry.key,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontFamily: 'monospace',
                    ),
                  ),
                  Text(
                    entry.value,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildDebugInfoCard(RemoteConfigProvider provider, bool isRTL) {
    final debugInfo = provider.debugInfo;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.bug_report,
                  color: Colors.purple[600],
                ),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'معلومات التشخيص' : 'Debug Information',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (debugInfo.isNotEmpty) ...[
              ...debugInfo.entries.map((entry) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Text(
                  '${entry.key}: ${entry.value}',
                  style: const TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 12,
                  ),
                ),
              )),
            ] else ...[
              Text(
                isRTL ? 'لا توجد معلومات تشخيص متاحة' : 'No debug information available',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _refreshConfig() async {
    final provider = Provider.of<RemoteConfigProvider>(context, listen: false);
    await provider.fetchAndActivate();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            Provider.of<LocaleProvider>(context, listen: false).isRTL
                ? 'تم تحديث التكوين البعيد'
                : 'Remote config refreshed',
          ),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _openConfigSourceManagement() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ConfigSourceManagementScreen(),
      ),
    );
  }
}
