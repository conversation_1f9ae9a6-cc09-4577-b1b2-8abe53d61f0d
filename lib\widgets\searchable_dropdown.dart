import 'package:flutter/material.dart';

class SearchableDropdown extends StatefulWidget {
  final String? value;
  final List<String> items;
  final String hintText;
  final String labelText;
  final IconData? prefixIcon;
  final Function(String?) onChanged;
  final Function(String)? onAddNew;
  final String? Function(String?)? validator;
  final bool enabled;
  final bool allowAddNew;
  final bool isRTL;

  const SearchableDropdown({
    super.key,
    this.value,
    required this.items,
    required this.hintText,
    required this.labelText,
    this.prefixIcon,
    required this.onChanged,
    this.onAddNew,
    this.validator,
    this.enabled = true,
    this.allowAddNew = true,
    this.isRTL = false,
  });

  @override
  State<SearchableDropdown> createState() => _SearchableDropdownState();
}

class _SearchableDropdownState extends State<SearchableDropdown> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  List<String> _filteredItems = [];
  bool _isDropdownOpen = false;
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();

  @override
  void initState() {
    super.initState();
    _filteredItems = List.from(widget.items);
    if (widget.value != null) {
      _controller.text = widget.value!;
    }
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void didUpdateWidget(SearchableDropdown oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update filtered items if the items list changed
    if (oldWidget.items != widget.items) {
      _filteredItems = List.from(widget.items);
      _filterItems(_controller.text);
    }

    // Update controller text if value changed
    if (oldWidget.value != widget.value) {
      if (widget.value != null) {
        _controller.text = widget.value!;
      } else {
        _controller.clear();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _removeOverlay();
    super.dispose();
  }

  void _onFocusChanged() {
    if (_focusNode.hasFocus) {
      _showDropdown();
    } else {
      _hideDropdown();
    }
  }

  void _filterItems(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredItems = List.from(widget.items);
      } else {
        _filteredItems = widget.items
            .where((item) => item.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
    });
    _updateOverlay();
  }

  void _showDropdown() {
    if (_isDropdownOpen) return;

    _isDropdownOpen = true;
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideDropdown() {
    if (!_isDropdownOpen) return;

    _isDropdownOpen = false;
    _removeOverlay();
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _updateOverlay() {
    if (_isDropdownOpen) {
      _removeOverlay();
      _overlayEntry = _createOverlayEntry();
      Overlay.of(context).insert(_overlayEntry!);
    }
  }

  OverlayEntry _createOverlayEntry() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    return OverlayEntry(
      builder: (context) => Positioned(
        width: size.width,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0.0, size.height + 5.0),
          child: Material(
            elevation: 4.0,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              constraints: const BoxConstraints(maxHeight: 200),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Theme.of(context).colorScheme.outline),
              ),
              child: _buildDropdownList(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDropdownList() {
    final hasItems = _filteredItems.isNotEmpty;
    final canAddNew = widget.allowAddNew &&
                     _controller.text.isNotEmpty &&
                     !widget.items.contains(_controller.text);

    return ListView(
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      children: [
        if (hasItems) ...[
          ..._filteredItems.map((item) => InkWell(
            onTap: () => _selectItem(item),
            child: ListTile(
              dense: true,
              title: Text(item),
              leading: Icon(
                widget.prefixIcon ?? Icons.check_circle_outline,
                size: 20,
                color: Colors.blue,
              ),
              trailing: widget.value == item
                ? const Icon(Icons.check, color: Colors.green, size: 20)
                : null,
            ),
          )),
        ],
        if (canAddNew) ...[
          if (hasItems) const Divider(height: 1),
          InkWell(
            onTap: () => _addNewItem(_controller.text),
            child: ListTile(
              dense: true,
              title: Text(widget.isRTL ? 'إضافة "${_controller.text}"' : 'Add "${_controller.text}"'),
              leading: const Icon(
                Icons.add_circle_outline,
                size: 20,
                color: Colors.green,
              ),
            ),
          ),
        ],
        if (!hasItems && !canAddNew)
          ListTile(
            dense: true,
            title: Text(widget.isRTL ? 'لا توجد نتائج' : 'No results found'),
            leading: const Icon(
              Icons.search_off,
              size: 20,
              color: Colors.grey,
            ),
          ),
      ],
    );
  }

  void _selectItem(String item) {
    setState(() {
      _controller.text = item;
    });
    _hideDropdown();
    _focusNode.unfocus();

    // Call onChanged after hiding dropdown to ensure proper state update
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.onChanged(item);
    });
  }

  void _addNewItem(String item) {
    if (widget.onAddNew != null) {
      widget.onAddNew!(item);
      // Wait for the item to be added to the list before selecting it
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _selectItem(item);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: TextFormField(
        controller: _controller,
        focusNode: _focusNode,
        enabled: widget.enabled,
        decoration: InputDecoration(
          labelText: widget.labelText,
          hintText: widget.hintText,
          prefixIcon: widget.prefixIcon != null ? Icon(widget.prefixIcon) : null,
          suffixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (_controller.text.isNotEmpty)
                IconButton(
                  icon: const Icon(Icons.clear, size: 20),
                  onPressed: () {
                    setState(() {
                      _controller.clear();
                    });
                    widget.onChanged(null);
                    _filterItems('');
                  },
                ),
              Icon(
                _isDropdownOpen ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                color: Colors.grey,
              ),
              const SizedBox(width: 8),
            ],
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          // Highlight selected value
          fillColor: widget.value != null && widget.items.contains(widget.value)
              ? Theme.of(context).colorScheme.primary.withAlpha(25)
              : null,
          filled: widget.value != null && widget.items.contains(widget.value),
        ),
        onChanged: (value) {
          _filterItems(value);
          // Only call onChanged if the value exactly matches an item
          if (widget.items.contains(value)) {
            widget.onChanged(value);
          }
        },
        onTap: () {
          if (!_isDropdownOpen) {
            _showDropdown();
          }
        },
        validator: widget.validator,
      ),
    );
  }
}
