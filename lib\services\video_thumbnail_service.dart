import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:path/path.dart' as path;

/// خدمة استخراج thumbnails من الفيديوهات
class VideoThumbnailService {
  static final VideoThumbnailService _instance = VideoThumbnailService._internal();
  factory VideoThumbnailService() => _instance;
  VideoThumbnailService._internal();

  static VideoThumbnailService get instance => _instance;

  /// استخراج thumbnail من فيديو كـ Uint8List
  Future<Uint8List?> generateVideoThumbnail(String videoPath) async {
    try {
      debugPrint('Generating thumbnail for video: $videoPath');
      
      final thumbnail = await VideoThumbnail.thumbnailData(
        video: videoPath,
        imageFormat: ImageFormat.JPEG,
        maxWidth: 300,
        maxHeight: 300,
        quality: 75,
      );

      if (thumbnail != null) {
        debugPrint('Thumbnail generated successfully, size: ${thumbnail.length} bytes');
      } else {
        debugPrint('Failed to generate thumbnail');
      }

      return thumbnail;
    } catch (e) {
      debugPrint('Error generating video thumbnail: $e');
      return null;
    }
  }

  /// استخراج thumbnail من فيديو وحفظه كملف
  Future<String?> generateVideoThumbnailFile(String videoPath) async {
    try {
      debugPrint('Generating thumbnail file for video: $videoPath');
      
      final thumbnailPath = await VideoThumbnail.thumbnailFile(
        video: videoPath,
        imageFormat: ImageFormat.JPEG,
        maxWidth: 300,
        maxHeight: 300,
        quality: 75,
      );

      if (thumbnailPath != null) {
        debugPrint('Thumbnail file generated: $thumbnailPath');
      } else {
        debugPrint('Failed to generate thumbnail file');
      }

      return thumbnailPath;
    } catch (e) {
      debugPrint('Error generating video thumbnail file: $e');
      return null;
    }
  }

  /// استخراج thumbnail من URL فيديو
  Future<Uint8List?> generateThumbnailFromUrl(String videoUrl) async {
    try {
      debugPrint('Generating thumbnail from URL: $videoUrl');
      
      final thumbnail = await VideoThumbnail.thumbnailData(
        video: videoUrl,
        imageFormat: ImageFormat.JPEG,
        maxWidth: 300,
        maxHeight: 300,
        quality: 75,
      );

      if (thumbnail != null) {
        debugPrint('Thumbnail from URL generated successfully, size: ${thumbnail.length} bytes');
      } else {
        debugPrint('Failed to generate thumbnail from URL');
      }

      return thumbnail;
    } catch (e) {
      debugPrint('Error generating thumbnail from URL: $e');
      return null;
    }
  }

  /// حفظ thumbnail في مجلد الكاش
  Future<File?> saveThumbnailToCache(Uint8List thumbnailData, String videoId) async {
    try {
      final directory = await getTemporaryDirectory();
      final thumbnailsDir = Directory(path.join(directory.path, 'video_thumbnails'));
      
      if (!await thumbnailsDir.exists()) {
        await thumbnailsDir.create(recursive: true);
      }

      final thumbnailFile = File(path.join(thumbnailsDir.path, '${videoId}_thumbnail.jpg'));
      await thumbnailFile.writeAsBytes(thumbnailData);
      
      debugPrint('Thumbnail saved to cache: ${thumbnailFile.path}');
      return thumbnailFile;
    } catch (e) {
      debugPrint('Error saving thumbnail to cache: $e');
      return null;
    }
  }

  /// الحصول على thumbnail من الكاش
  Future<File?> getThumbnailFromCache(String videoId) async {
    try {
      final directory = await getTemporaryDirectory();
      final thumbnailFile = File(path.join(directory.path, 'video_thumbnails', '${videoId}_thumbnail.jpg'));
      
      if (await thumbnailFile.exists()) {
        debugPrint('Thumbnail found in cache: ${thumbnailFile.path}');
        return thumbnailFile;
      }
      
      return null;
    } catch (e) {
      debugPrint('Error getting thumbnail from cache: $e');
      return null;
    }
  }

  /// تنظيف كاش thumbnails القديمة
  Future<void> cleanOldThumbnails({int maxAgeInDays = 7}) async {
    try {
      final directory = await getTemporaryDirectory();
      final thumbnailsDir = Directory(path.join(directory.path, 'video_thumbnails'));
      
      if (!await thumbnailsDir.exists()) {
        return;
      }

      final now = DateTime.now();
      final files = await thumbnailsDir.list().toList();
      
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          final age = now.difference(stat.modified).inDays;
          
          if (age > maxAgeInDays) {
            await file.delete();
            debugPrint('Deleted old thumbnail: ${file.path}');
          }
        }
      }
      
      debugPrint('Cleaned old thumbnails older than $maxAgeInDays days');
    } catch (e) {
      debugPrint('Error cleaning old thumbnails: $e');
    }
  }

  /// الحصول على حجم كاش thumbnails
  Future<int> getThumbnailCacheSize() async {
    try {
      final directory = await getTemporaryDirectory();
      final thumbnailsDir = Directory(path.join(directory.path, 'video_thumbnails'));
      
      if (!await thumbnailsDir.exists()) {
        return 0;
      }

      int totalSize = 0;
      final files = await thumbnailsDir.list().toList();
      
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          totalSize += stat.size;
        }
      }
      
      return totalSize;
    } catch (e) {
      debugPrint('Error getting thumbnail cache size: $e');
      return 0;
    }
  }

  /// مسح جميع thumbnails من الكاش
  Future<void> clearThumbnailCache() async {
    try {
      final directory = await getTemporaryDirectory();
      final thumbnailsDir = Directory(path.join(directory.path, 'video_thumbnails'));
      
      if (await thumbnailsDir.exists()) {
        await thumbnailsDir.delete(recursive: true);
        debugPrint('Thumbnail cache cleared');
      }
    } catch (e) {
      debugPrint('Error clearing thumbnail cache: $e');
    }
  }

  /// إنشاء معرف فريد للفيديو من URL
  String generateVideoId(String videoUrl) {
    return videoUrl.hashCode.abs().toString();
  }

  /// استخراج thumbnail مع كاش
  Future<Uint8List?> getThumbnailWithCache(String videoUrl) async {
    try {
      final videoId = generateVideoId(videoUrl);
      
      // محاولة الحصول على thumbnail من الكاش أولاً
      final cachedFile = await getThumbnailFromCache(videoId);
      if (cachedFile != null) {
        return await cachedFile.readAsBytes();
      }
      
      // إذا لم يوجد في الكاش، إنشاء thumbnail جديد
      final thumbnailData = await generateThumbnailFromUrl(videoUrl);
      if (thumbnailData != null) {
        // حفظ في الكاش للاستخدام المستقبلي
        await saveThumbnailToCache(thumbnailData, videoId);
      }
      
      return thumbnailData;
    } catch (e) {
      debugPrint('Error getting thumbnail with cache: $e');
      return null;
    }
  }
}
