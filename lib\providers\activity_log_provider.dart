import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';
import '../models/activity_log_model.dart';


class ActivityLogProvider extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  List<ActivityLog> _activities = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<ActivityLog> get activities => _activities;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Log an activity
  Future<void> logActivity({
    required ActivityType type,
    required String performedBy,
    required String performedByName,
    String? targetUserId,
    String? targetUserName,
    String? roleId,
    String? roleName,
    String? permissionType,
    Map<String, dynamic>? details,
    String? ipAddress,
  }) async {
    try {
      // Get device info
      final deviceInfo = await _getDeviceInfo();

      // Create activity log
      final activity = ActivityLog(
        id: '', // Will be set by Firestore
        type: type,
        performedBy: performedBy,
        performedByName: performedByName,
        targetUserId: targetUserId,
        targetUserName: targetUserName,
        roleId: roleId,
        roleName: roleName,
        permissionType: permissionType,
        details: details ?? {},
        description: ActivityDescriptions.getDescription(
          type,
          false, // English description
          targetUserName: targetUserName,
          roleName: roleName,
          permissionType: permissionType,
          details: details,
        ),
        descriptionAr: ActivityDescriptions.getDescription(
          type,
          true, // Arabic description
          targetUserName: targetUserName,
          roleName: roleName,
          permissionType: permissionType,
          details: details,
        ),
        timestamp: DateTime.now(),
        ipAddress: ipAddress,
        deviceInfo: deviceInfo,
      );

      // Save to Firestore
      await _firestore.collection('activity_logs').add(activity.toFirestore());

      debugPrint('Activity logged: ${activity.description}');
    } catch (e) {
      debugPrint('Error logging activity: $e');
    }
  }

  /// Load activities with pagination
  Future<void> loadActivities({
    int limit = 50,
    DocumentSnapshot? startAfter,
    String? userId,
    ActivityType? activityType,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      Query query = _firestore.collection('activity_logs')
          .orderBy('timestamp', descending: true);

      // Apply filters
      if (userId != null) {
        query = query.where('performedBy', isEqualTo: userId);
      }

      if (activityType != null) {
        query = query.where('type', isEqualTo: activityType.toString());
      }

      if (startDate != null) {
        query = query.where('timestamp', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        query = query.where('timestamp', isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      // Apply pagination
      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      query = query.limit(limit);

      final snapshot = await query.get();

      if (startAfter == null) {
        // First load - replace activities
        _activities = snapshot.docs.map((doc) => ActivityLog.fromFirestore(doc)).toList();
      } else {
        // Load more - append activities
        final newActivities = snapshot.docs.map((doc) => ActivityLog.fromFirestore(doc)).toList();
        _activities.addAll(newActivities);
      }

      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// Get activities for a specific user
  Future<List<ActivityLog>> getUserActivities(String userId, {int limit = 20}) async {
    try {
      final snapshot = await _firestore
          .collection('activity_logs')
          .where('performedBy', isEqualTo: userId)
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs.map((doc) => ActivityLog.fromFirestore(doc)).toList();
    } catch (e) {
      debugPrint('Error getting user activities: $e');
      return [];
    }
  }

  /// Get activities by type
  Future<List<ActivityLog>> getActivitiesByType(ActivityType type, {int limit = 20}) async {
    try {
      // First get all activities, then filter by type to avoid composite index requirement
      final snapshot = await _firestore
          .collection('activity_logs')
          .orderBy('timestamp', descending: true)
          .limit(limit * 3) // Get more to account for filtering
          .get();

      final activities = snapshot.docs
          .map((doc) => ActivityLog.fromFirestore(doc))
          .where((activity) => activity.type == type)
          .take(limit)
          .toList();

      return activities;
    } catch (e) {
      debugPrint('Error getting activities by type: $e');
      return [];
    }
  }

  /// Get activity statistics
  Future<Map<String, dynamic>> getActivityStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // Use simple query to avoid index requirements
      Query query = _firestore.collection('activity_logs')
          .orderBy('timestamp', descending: true)
          .limit(1000); // Limit to recent activities

      final snapshot = await query.get();
      var activities = snapshot.docs.map((doc) => ActivityLog.fromFirestore(doc)).toList();

      // Apply date filters in memory if needed
      if (startDate != null) {
        activities = activities.where((activity) => activity.timestamp.isAfter(startDate)).toList();
      }
      if (endDate != null) {
        activities = activities.where((activity) => activity.timestamp.isBefore(endDate)).toList();
      }

      // Calculate statistics
      final Map<ActivityType, int> typeCount = {};
      final Map<String, int> userCount = {};
      final Map<String, int> dailyCount = {};

      for (final activity in activities) {
        // Count by type
        typeCount[activity.type] = (typeCount[activity.type] ?? 0) + 1;

        // Count by user
        userCount[activity.performedByName] = (userCount[activity.performedByName] ?? 0) + 1;

        // Count by day
        final day = activity.timestamp.toIso8601String().split('T')[0];
        dailyCount[day] = (dailyCount[day] ?? 0) + 1;
      }

      return {
        'totalActivities': activities.length,
        'typeCount': typeCount.map((key, value) => MapEntry(key.toString(), value)),
        'userCount': userCount,
        'dailyCount': dailyCount,
        'dateRange': {
          'start': startDate?.toIso8601String(),
          'end': endDate?.toIso8601String(),
        },
      };
    } catch (e) {
      debugPrint('Error getting activity statistics: $e');
      return {
        'totalActivities': 0,
        'typeCount': <String, int>{},
        'userCount': <String, int>{},
        'dailyCount': <String, int>{},
        'dateRange': {
          'start': startDate?.toIso8601String(),
          'end': endDate?.toIso8601String(),
        },
      };
    }
  }

  /// Delete old activities (cleanup)
  Future<void> cleanupOldActivities({int daysToKeep = 90}) async {
    try {
      final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));

      final snapshot = await _firestore
          .collection('activity_logs')
          .where('timestamp', isLessThan: Timestamp.fromDate(cutoffDate))
          .get();

      final batch = _firestore.batch();
      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      debugPrint('Cleaned up ${snapshot.docs.length} old activity logs');
    } catch (e) {
      debugPrint('Error cleaning up old activities: $e');
    }
  }

  /// Get device information
  Future<String> _getDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return 'Android ${androidInfo.version.release} - ${androidInfo.model}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return 'iOS ${iosInfo.systemVersion} - ${iosInfo.model}';
      } else if (Platform.isWindows) {
        final windowsInfo = await deviceInfo.windowsInfo;
        return 'Windows - ${windowsInfo.computerName}';
      } else if (Platform.isMacOS) {
        final macInfo = await deviceInfo.macOsInfo;
        return 'macOS ${macInfo.osRelease} - ${macInfo.model}';
      } else if (Platform.isLinux) {
        final linuxInfo = await deviceInfo.linuxInfo;
        return 'Linux - ${linuxInfo.name}';
      }

      return 'Unknown Device';
    } catch (e) {
      return 'Device Info Error';
    }
  }

  /// Helper methods for specific activity types

  // Role management activities
  Future<void> logRoleCreated(String performedBy, String performedByName, String roleId, String roleName) async {
    await logActivity(
      type: ActivityType.roleCreated,
      performedBy: performedBy,
      performedByName: performedByName,
      roleId: roleId,
      roleName: roleName,
    );
  }

  Future<void> logRoleUpdated(String performedBy, String performedByName, String roleId, String roleName) async {
    await logActivity(
      type: ActivityType.roleUpdated,
      performedBy: performedBy,
      performedByName: performedByName,
      roleId: roleId,
      roleName: roleName,
    );
  }

  Future<void> logRoleDeleted(String performedBy, String performedByName, String roleId, String roleName) async {
    await logActivity(
      type: ActivityType.roleDeleted,
      performedBy: performedBy,
      performedByName: performedByName,
      roleId: roleId,
      roleName: roleName,
    );
  }

  // User role assignment activities
  Future<void> logUserRoleAssigned(String performedBy, String performedByName, String targetUserId, String targetUserName, String roleId, String roleName) async {
    await logActivity(
      type: ActivityType.userRoleAssigned,
      performedBy: performedBy,
      performedByName: performedByName,
      targetUserId: targetUserId,
      targetUserName: targetUserName,
      roleId: roleId,
      roleName: roleName,
    );
  }

  Future<void> logUserRoleChanged(String performedBy, String performedByName, String targetUserId, String targetUserName, String newRoleId, String newRoleName, {String? oldRoleName}) async {
    await logActivity(
      type: ActivityType.userRoleChanged,
      performedBy: performedBy,
      performedByName: performedByName,
      targetUserId: targetUserId,
      targetUserName: targetUserName,
      roleId: newRoleId,
      roleName: newRoleName,
      details: oldRoleName != null ? {'oldRoleName': oldRoleName} : null,
    );
  }

  // Permission management activities
  Future<void> logPermissionGranted(String performedBy, String performedByName, String targetUserId, String targetUserName, String permissionType) async {
    await logActivity(
      type: ActivityType.permissionGranted,
      performedBy: performedBy,
      performedByName: performedByName,
      targetUserId: targetUserId,
      targetUserName: targetUserName,
      permissionType: permissionType,
    );
  }

  Future<void> logPermissionRevoked(String performedBy, String performedByName, String targetUserId, String targetUserName, String permissionType) async {
    await logActivity(
      type: ActivityType.permissionRevoked,
      performedBy: performedBy,
      performedByName: performedByName,
      targetUserId: targetUserId,
      targetUserName: targetUserName,
      permissionType: permissionType,
    );
  }

  /// Helper methods
  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  /// Clear all data (for logout)
  void clear() {
    _activities.clear();
    _error = null;
    _isLoading = false;
    notifyListeners();
  }
}
