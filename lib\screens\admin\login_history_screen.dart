import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../models/login_history_model.dart';
import '../../providers/locale_provider.dart';


class LoginHistoryScreen extends StatefulWidget {
  const LoginHistoryScreen({super.key});

  @override
  State<LoginHistoryScreen> createState() => _LoginHistoryScreenState();
}

class _LoginHistoryScreenState extends State<LoginHistoryScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  List<LoginHistoryModel> _loginHistory = [];
  List<LoginHistoryModel> _filteredHistory = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadLoginHistory();
  }

  Future<void> _loadLoginHistory() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final querySnapshot = await _firestore
          .collection('login_history')
          .orderBy('loginTime', descending: true)
          .limit(100)
          .get();

      _loginHistory = querySnapshot.docs
          .map((doc) => LoginHistoryModel.fromMap(doc.data()))
          .toList();

      _filterHistory();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل سجل تسجيل الدخول: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _filterHistory() {
    setState(() {
      if (_searchQuery.isEmpty) {
        _filteredHistory = _loginHistory;
      } else {
        _filteredHistory = _loginHistory.where((history) {
          return history.userEmail.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              (history.userName?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) ||
              (history.deviceInfo?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) ||
              (history.ipAddress?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isRTL ? 'سجل تسجيل الدخول' : 'Login History',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadLoginHistory,
            tooltip: isRTL ? 'تحديث' : 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              decoration: InputDecoration(
                hintText: isRTL ? 'البحث في سجل تسجيل الدخول...' : 'Search login history...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surface,
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
                _filterHistory();
              },
            ),
          ),

          // Login history list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredHistory.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.history,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _searchQuery.isEmpty
                                  ? (isRTL ? 'لا يوجد سجل تسجيل دخول' : 'No login history found')
                                  : (isRTL ? 'لا توجد نتائج للبحث' : 'No search results'),
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _filteredHistory.length,
                        itemBuilder: (context, index) {
                          final history = _filteredHistory[index];
                          return _buildHistoryCard(history, isRTL);
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryCard(LoginHistoryModel history, bool isRTL) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.surface,
              Theme.of(context).colorScheme.surface.withAlpha(204),
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Status icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: history.isSuccessful ? Colors.green.withAlpha(25) : Colors.red.withAlpha(25),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      history.isSuccessful ? Icons.login : Icons.error,
                      color: history.isSuccessful ? Colors.green : Colors.red,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),

                  // User info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          history.userName ?? history.userEmail,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (history.userName != null) ...[
                          const SizedBox(height: 2),
                          Text(
                            history.userEmail,
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onSurface.withAlpha(178),
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  // Login time
                  Text(
                    history.formattedLoginTime,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),
              const Divider(),
              const SizedBox(height: 8),

              // Device and location info
              Row(
                children: [
                  if (history.deviceInfo != null) ...[
                    const Icon(Icons.devices, size: 16, color: Colors.blue),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        history.deviceInfo!,
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).colorScheme.onSurface.withAlpha(204),
                        ),
                      ),
                    ),
                  ],
                ],
              ),

              if (history.ipAddress != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(Icons.public, size: 16, color: Colors.orange),
                    const SizedBox(width: 4),
                    Text(
                      'IP: ${history.ipAddress}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.onSurface.withAlpha(204),
                      ),
                    ),
                  ],
                ),
              ],

              if (history.location != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(Icons.location_on, size: 16, color: Colors.green),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        history.location!,
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).colorScheme.onSurface.withAlpha(204),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
