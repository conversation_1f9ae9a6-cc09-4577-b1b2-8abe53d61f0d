import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import '../../services/file_manager_service.dart';
import '../../providers/locale_provider.dart';
import '../../providers/auth_provider.dart';

class FileManagerScreen extends StatefulWidget {
  const FileManagerScreen({super.key});

  @override
  State<FileManagerScreen> createState() => _FileManagerScreenState();
}

class _FileManagerScreenState extends State<FileManagerScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  Map<String, List<FileInfo>> _allFiles = {};
  StorageInfo? _storageInfo;
  bool _isLoading = true;
  final Set<String> _selectedFiles = {};
  bool _isSelectionMode = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadFiles();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadFiles() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final files = await FileManagerService.getAllAppFiles();
      final storage = await FileManagerService.getStorageInfo();

      setState(() {
        _allFiles = files;
        _storageInfo = storage;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الملفات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final isRTL = localeProvider.isRTL;

    if (!authProvider.isAdmin) {
      return Scaffold(
        appBar: AppBar(
          title: Text(isRTL ? 'غير مصرح' : 'Unauthorized'),
        ),
        body: Center(
          child: Text(
            isRTL ? 'ليس لديك صلاحية للوصول لهذه الصفحة' : 'You don\'t have permission to access this page',
            style: const TextStyle(fontSize: 18),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isRTL ? 'إدارة الملفات' : 'File Manager',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: true,
        actions: [
          if (_isSelectionMode)
            IconButton(
              icon: const Icon(Icons.delete_sweep),
              onPressed: _selectedFiles.isNotEmpty ? _deleteSelectedFiles : null,
              tooltip: isRTL ? 'حذف المحدد' : 'Delete Selected',
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadFiles,
            tooltip: isRTL ? 'تحديث' : 'Refresh',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'clear_cache',
                child: Row(
                  children: [
                    const Icon(Icons.cleaning_services),
                    const SizedBox(width: 8),
                    Text(isRTL ? 'مسح الكاش' : 'Clear Cache'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'delete_old',
                child: Row(
                  children: [
                    const Icon(Icons.auto_delete),
                    const SizedBox(width: 8),
                    Text(isRTL ? 'حذف الملفات القديمة' : 'Delete Old Files'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'storage_info',
                child: Row(
                  children: [
                    const Icon(Icons.storage),
                    const SizedBox(width: 8),
                    Text(isRTL ? 'معلومات التخزين' : 'Storage Info'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: [
            Tab(
              icon: const Icon(Icons.dashboard),
              text: isRTL ? 'نظرة عامة' : 'Overview',
            ),
            Tab(
              icon: const Icon(Icons.image),
              text: isRTL ? 'الصور' : 'Images',
            ),
            Tab(
              icon: const Icon(Icons.backup),
              text: isRTL ? 'النسخ الاحتياطية' : 'Backups',
            ),
            Tab(
              icon: const Icon(Icons.file_download),
              text: isRTL ? 'التصدير' : 'Exports',
            ),
            Tab(
              icon: const Icon(Icons.cached),
              text: isRTL ? 'الكاش' : 'Cache',
            ),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(isRTL),
                _buildFileListTab('images', isRTL),
                _buildFileListTab('backups', isRTL),
                _buildFileListTab('exports', isRTL),
                _buildFileListTab('cache', isRTL),
              ],
            ),
      floatingActionButton: _isSelectionMode
          ? FloatingActionButton(
              onPressed: _exitSelectionMode,
              backgroundColor: Colors.grey,
              child: const Icon(Icons.close),
            )
          : null,
    );
  }

  Widget _buildOverviewTab(bool isRTL) {
    if (_storageInfo == null) {
      return const Center(child: Text('لا توجد معلومات تخزين'));
    }

    final storage = _storageInfo!;
    final totalFiles = _allFiles.values.fold<int>(0, (sum, files) => sum + files.length);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات التخزين
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    isRTL ? 'معلومات التخزين' : 'Storage Information',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 16),
                  _buildStorageBar(
                    isRTL ? 'المساحة المستخدمة بواسطة التطبيق' : 'App Storage Usage',
                    storage.appUsedSpace,
                    storage.totalSpace,
                    Colors.blue,
                    storage,
                  ),
                  const SizedBox(height: 8),
                  _buildStorageBar(
                    isRTL ? 'إجمالي المساحة المستخدمة' : 'Total Storage Usage',
                    storage.usedSpace,
                    storage.totalSpace,
                    Colors.orange,
                    storage,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // إحصائيات الملفات
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    isRTL ? 'إحصائيات الملفات' : 'File Statistics',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 16),
                  _buildFileStatItem(
                    isRTL ? 'إجمالي الملفات' : 'Total Files',
                    totalFiles.toString(),
                    Icons.folder,
                  ),
                  _buildFileStatItem(
                    isRTL ? 'الصور' : 'Images',
                    (_allFiles['images']?.length ?? 0).toString(),
                    Icons.image,
                  ),
                  _buildFileStatItem(
                    isRTL ? 'النسخ الاحتياطية' : 'Backups',
                    (_allFiles['backups']?.length ?? 0).toString(),
                    Icons.backup,
                  ),
                  _buildFileStatItem(
                    isRTL ? 'ملفات التصدير' : 'Export Files',
                    (_allFiles['exports']?.length ?? 0).toString(),
                    Icons.file_download,
                  ),
                  _buildFileStatItem(
                    isRTL ? 'ملفات الكاش' : 'Cache Files',
                    (_allFiles['cache']?.length ?? 0).toString(),
                    Icons.cached,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // إجراءات سريعة
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    isRTL ? 'إجراءات سريعة' : 'Quick Actions',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 16),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      ElevatedButton.icon(
                        onPressed: () => _handleMenuAction('clear_cache'),
                        icon: const Icon(Icons.cleaning_services),
                        label: Text(isRTL ? 'مسح الكاش' : 'Clear Cache'),
                      ),
                      ElevatedButton.icon(
                        onPressed: () => _handleMenuAction('delete_old'),
                        icon: const Icon(Icons.auto_delete),
                        label: Text(isRTL ? 'حذف القديم' : 'Delete Old'),
                      ),
                      ElevatedButton.icon(
                        onPressed: _loadFiles,
                        icon: const Icon(Icons.refresh),
                        label: Text(isRTL ? 'تحديث' : 'Refresh'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStorageBar(String label, int used, int total, Color color, StorageInfo storage) {
    final percentage = total > 0 ? (used / total) * 100 : 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(label),
            Text('${storage.formatBytes(used)} / ${storage.formatBytes(total)}'),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: percentage / 100,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(color),
        ),
        const SizedBox(height: 4),
        Text(
          '${percentage.toStringAsFixed(1)}%',
          style: const TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ],
    );
  }

  Widget _buildFileStatItem(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Expanded(child: Text(label)),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildFileListTab(String category, bool isRTL) {
    final files = _allFiles[category] ?? [];

    if (files.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getCategoryIcon(category),
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              isRTL ? 'لا توجد ملفات' : 'No files found',
              style: const TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // شريط الإجراءات
        Container(
          padding: const EdgeInsets.all(8),
          child: Row(
            children: [
              Text(
                '${files.length} ${isRTL ? 'ملف' : 'files'}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              IconButton(
                icon: Icon(_isSelectionMode ? Icons.check_circle : Icons.check_circle_outline),
                onPressed: _toggleSelectionMode,
                tooltip: isRTL ? 'وضع التحديد' : 'Selection Mode',
              ),
              if (_isSelectionMode)
                TextButton(
                  onPressed: _selectAllFiles,
                  child: Text(isRTL ? 'تحديد الكل' : 'Select All'),
                ),
            ],
          ),
        ),

        // قائمة الملفات
        Expanded(
          child: ListView.builder(
            itemCount: files.length,
            itemBuilder: (context, index) {
              final file = files[index];
              final isSelected = _selectedFiles.contains(file.path);

              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                child: ListTile(
                  leading: _isSelectionMode
                      ? Checkbox(
                          value: isSelected,
                          onChanged: (value) => _toggleFileSelection(file.path),
                        )
                      : Icon(_getFileIcon(file.type)),
                  title: Text(
                    file.name,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(file.formattedSize),
                      Text(
                        '${file.lastModified.day}/${file.lastModified.month}/${file.lastModified.year}',
                        style: const TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                  trailing: _isSelectionMode
                      ? null
                      : PopupMenuButton<String>(
                          onSelected: (action) => _handleFileAction(action, file),
                          itemBuilder: (context) => [
                            PopupMenuItem(
                              value: 'delete',
                              child: Row(
                                children: [
                                  const Icon(Icons.delete, color: Colors.red),
                                  const SizedBox(width: 8),
                                  Text(isRTL ? 'حذف' : 'Delete'),
                                ],
                              ),
                            ),
                            PopupMenuItem(
                              value: 'share',
                              child: Row(
                                children: [
                                  const Icon(Icons.share),
                                  const SizedBox(width: 8),
                                  Text(isRTL ? 'مشاركة' : 'Share'),
                                ],
                              ),
                            ),
                            PopupMenuItem(
                              value: 'info',
                              child: Row(
                                children: [
                                  const Icon(Icons.info),
                                  const SizedBox(width: 8),
                                  Text(isRTL ? 'معلومات' : 'Info'),
                                ],
                              ),
                            ),
                          ],
                        ),
                  onTap: _isSelectionMode
                      ? () => _toggleFileSelection(file.path)
                      : () => _showFileDetails(file),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'images':
        return Icons.image;
      case 'backups':
        return Icons.backup;
      case 'exports':
        return Icons.file_download;
      case 'cache':
        return Icons.cached;
      default:
        return Icons.folder;
    }
  }

  IconData _getFileIcon(String type) {
    switch (type) {
      case 'image':
        return Icons.image;
      case 'backup':
        return Icons.backup;
      case 'excel':
        return Icons.table_chart;
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'text':
        return Icons.text_snippet;
      default:
        return Icons.insert_drive_file;
    }
  }

  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedFiles.clear();
      }
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedFiles.clear();
    });
  }

  void _toggleFileSelection(String filePath) {
    setState(() {
      if (_selectedFiles.contains(filePath)) {
        _selectedFiles.remove(filePath);
      } else {
        _selectedFiles.add(filePath);
      }
    });
  }

  void _selectAllFiles() {
    setState(() {
      _selectedFiles.clear();
      for (final fileList in _allFiles.values) {
        for (final file in fileList) {
          _selectedFiles.add(file.path);
        }
      }
    });
  }

  Future<void> _handleMenuAction(String action) async {

    switch (action) {
      case 'clear_cache':
        await _clearCache();
        break;
      case 'delete_old':
        await _deleteOldFiles();
        break;
      case 'storage_info':
        _showStorageInfo();
        break;
    }
  }

  Future<void> _clearCache() async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'مسح الكاش' : 'Clear Cache'),
        content: Text(
          isRTL
            ? 'هل أنت متأكد من مسح جميع ملفات الكاش؟'
            : 'Are you sure you want to clear all cache files?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(isRTL ? 'مسح' : 'Clear'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final deletedCount = await FileManagerService.clearCache();
        await _loadFiles();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                isRTL
                  ? 'تم مسح $deletedCount ملف من الكاش'
                  : 'Cleared $deletedCount cache files',
              ),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في مسح الكاش: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _deleteOldFiles() async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'حذف الملفات القديمة' : 'Delete Old Files'),
        content: Text(
          isRTL
            ? 'هل أنت متأكد من حذف الملفات الأقدم من 30 يوم؟'
            : 'Are you sure you want to delete files older than 30 days?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(isRTL ? 'حذف' : 'Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final deletedCount = await FileManagerService.deleteOldFiles(daysOld: 30);
        await _loadFiles();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                isRTL
                  ? 'تم حذف $deletedCount ملف قديم'
                  : 'Deleted $deletedCount old files',
              ),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف الملفات: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _deleteSelectedFiles() async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'حذف الملفات المحددة' : 'Delete Selected Files'),
        content: Text(
          isRTL
            ? 'هل أنت متأكد من حذف ${_selectedFiles.length} ملف؟'
            : 'Are you sure you want to delete ${_selectedFiles.length} files?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(isRTL ? 'حذف' : 'Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final deletedCount = await FileManagerService.deleteFiles(_selectedFiles.toList());
        await _loadFiles();
        _exitSelectionMode();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                isRTL
                  ? 'تم حذف $deletedCount ملف'
                  : 'Deleted $deletedCount files',
              ),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف الملفات: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _handleFileAction(String action, FileInfo file) {
    switch (action) {
      case 'delete':
        _deleteFile(file);
        break;
      case 'share':
        _shareFile(file);
        break;
      case 'info':
        _showFileDetails(file);
        break;
    }
  }

  Future<void> _deleteFile(FileInfo file) async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'حذف الملف' : 'Delete File'),
        content: Text(
          isRTL
            ? 'هل أنت متأكد من حذف "${file.name}"؟'
            : 'Are you sure you want to delete "${file.name}"?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(isRTL ? 'حذف' : 'Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final success = await FileManagerService.deleteFile(file.path);
        if (success) {
          await _loadFiles();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  isRTL ? 'تم حذف الملف بنجاح' : 'File deleted successfully',
                ),
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف الملف: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _shareFile(FileInfo file) async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    try {
      await Share.shareXFiles(
        [XFile(file.path)],
        text: isRTL
          ? 'مشاركة ملف: ${file.name}'
          : 'Sharing file: ${file.name}',
        subject: isRTL
          ? 'ملف من تطبيق أعطال الأجهزة المنزلية'
          : 'File from HM Device Errors App',
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL
                ? 'خطأ في مشاركة الملف: $e'
                : 'Error sharing file: $e',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showFileDetails(FileInfo file) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'تفاصيل الملف' : 'File Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow(isRTL ? 'الاسم' : 'Name', file.name),
            _buildDetailRow(isRTL ? 'الحجم' : 'Size', file.formattedSize),
            _buildDetailRow(isRTL ? 'النوع' : 'Type', file.type),
            _buildDetailRow(isRTL ? 'الامتداد' : 'Extension', file.extension),
            _buildDetailRow(
              isRTL ? 'تاريخ التعديل' : 'Last Modified',
              '${file.lastModified.day}/${file.lastModified.month}/${file.lastModified.year}',
            ),
            _buildDetailRow(isRTL ? 'المسار' : 'Path', file.path),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'إغلاق' : 'Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  void _showStorageInfo() {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    if (_storageInfo == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'معلومات التخزين' : 'Storage Information'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildDetailRow(
              isRTL ? 'إجمالي المساحة' : 'Total Space',
              _storageInfo!.formatBytes(_storageInfo!.totalSpace),
            ),
            _buildDetailRow(
              isRTL ? 'المساحة المستخدمة' : 'Used Space',
              _storageInfo!.formatBytes(_storageInfo!.usedSpace),
            ),
            _buildDetailRow(
              isRTL ? 'المساحة المتاحة' : 'Free Space',
              _storageInfo!.formatBytes(_storageInfo!.freeSpace),
            ),
            _buildDetailRow(
              isRTL ? 'مساحة التطبيق' : 'App Space',
              _storageInfo!.formatBytes(_storageInfo!.appUsedSpace),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'إغلاق' : 'Close'),
          ),
        ],
      ),
    );
  }
}
