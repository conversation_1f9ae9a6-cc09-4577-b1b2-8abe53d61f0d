import 'package:cloud_firestore/cloud_firestore.dart';

class StorageConfigModel {
  final String id;
  final String configName; // 'google_drive' or 'onedrive'
  final String clientId;
  final String clientSecret;
  final String? redirectUri;
  final String? parentFolderId; // For Google Drive
  // Service Account fields for Google Drive
  final String? projectId;
  final String? privateKeyId;
  final String? privateKey;
  final String? clientEmail;
  final String? authUri;
  final String? tokenUri;
  final String? authProviderCertUrl;
  final String? clientCertUrl;
  final String? universeDomain;
  final Map<String, dynamic> additionalSettings;
  final bool isActive;
  final bool isDefault;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy;
  final String updatedBy;

  StorageConfigModel({
    required this.id,
    required this.configName,
    required this.clientId,
    required this.clientSecret,
    this.redirectUri,
    this.parentFolderId,
    this.projectId,
    this.privateKeyId,
    this.privateKey,
    this.clientEmail,
    this.authUri,
    this.tokenUri,
    this.authProviderCertUrl,
    this.clientCertUrl,
    this.universeDomain,
    this.additionalSettings = const {},
    this.isActive = true,
    this.isDefault = false,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    required this.updatedBy,
  });

  // Copy with method
  StorageConfigModel copyWith({
    String? id,
    String? configName,
    String? clientId,
    String? clientSecret,
    String? redirectUri,
    String? parentFolderId,
    String? projectId,
    String? privateKeyId,
    String? privateKey,
    String? clientEmail,
    String? authUri,
    String? tokenUri,
    String? authProviderCertUrl,
    String? clientCertUrl,
    String? universeDomain,
    Map<String, dynamic>? additionalSettings,
    bool? isActive,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
  }) {
    return StorageConfigModel(
      id: id ?? this.id,
      configName: configName ?? this.configName,
      clientId: clientId ?? this.clientId,
      clientSecret: clientSecret ?? this.clientSecret,
      redirectUri: redirectUri ?? this.redirectUri,
      parentFolderId: parentFolderId ?? this.parentFolderId,
      projectId: projectId ?? this.projectId,
      privateKeyId: privateKeyId ?? this.privateKeyId,
      privateKey: privateKey ?? this.privateKey,
      clientEmail: clientEmail ?? this.clientEmail,
      authUri: authUri ?? this.authUri,
      tokenUri: tokenUri ?? this.tokenUri,
      authProviderCertUrl: authProviderCertUrl ?? this.authProviderCertUrl,
      clientCertUrl: clientCertUrl ?? this.clientCertUrl,
      universeDomain: universeDomain ?? this.universeDomain,
      additionalSettings: additionalSettings ?? this.additionalSettings,
      isActive: isActive ?? this.isActive,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
    );
  }

  // Convert to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'configName': configName,
      'clientId': clientId,
      'clientSecret': clientSecret,
      'redirectUri': redirectUri,
      'parentFolderId': parentFolderId,
      'projectId': projectId,
      'privateKeyId': privateKeyId,
      'privateKey': privateKey,
      'clientEmail': clientEmail,
      'authUri': authUri,
      'tokenUri': tokenUri,
      'authProviderCertUrl': authProviderCertUrl,
      'clientCertUrl': clientCertUrl,
      'universeDomain': universeDomain,
      'additionalSettings': additionalSettings,
      'isActive': isActive,
      'isDefault': isDefault,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'createdBy': createdBy,
      'updatedBy': updatedBy,
    };
  }

  // Create from Firestore document
  factory StorageConfigModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return StorageConfigModel(
      id: doc.id,
      configName: data['configName'] ?? '',
      clientId: data['clientId'] ?? '',
      clientSecret: data['clientSecret'] ?? '',
      redirectUri: data['redirectUri'],
      parentFolderId: data['parentFolderId'],
      projectId: data['projectId'],
      privateKeyId: data['privateKeyId'],
      privateKey: data['privateKey'],
      clientEmail: data['clientEmail'],
      authUri: data['authUri'],
      tokenUri: data['tokenUri'],
      authProviderCertUrl: data['authProviderCertUrl'],
      clientCertUrl: data['clientCertUrl'],
      universeDomain: data['universeDomain'],
      additionalSettings: Map<String, dynamic>.from(data['additionalSettings'] ?? {}),
      isActive: data['isActive'] ?? true,
      isDefault: data['isDefault'] ?? false,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      createdBy: data['createdBy'] ?? '',
      updatedBy: data['updatedBy'] ?? '',
    );
  }

  // Create from Map
  factory StorageConfigModel.fromMap(Map<String, dynamic> map) {
    return StorageConfigModel(
      id: map['id'] ?? '',
      configName: map['configName'] ?? '',
      clientId: map['clientId'] ?? '',
      clientSecret: map['clientSecret'] ?? '',
      redirectUri: map['redirectUri'],
      parentFolderId: map['parentFolderId'],
      projectId: map['projectId'],
      privateKeyId: map['privateKeyId'],
      privateKey: map['privateKey'],
      clientEmail: map['clientEmail'],
      authUri: map['authUri'],
      tokenUri: map['tokenUri'],
      authProviderCertUrl: map['authProviderCertUrl'],
      clientCertUrl: map['clientCertUrl'],
      universeDomain: map['universeDomain'],
      additionalSettings: Map<String, dynamic>.from(map['additionalSettings'] ?? {}),
      isActive: map['isActive'] ?? true,
      isDefault: map['isDefault'] ?? false,
      createdAt: map['createdAt'] is Timestamp
          ? (map['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      updatedAt: map['updatedAt'] is Timestamp
          ? (map['updatedAt'] as Timestamp).toDate()
          : DateTime.now(),
      createdBy: map['createdBy'] ?? '',
      updatedBy: map['updatedBy'] ?? '',
    );
  }

  // Validation methods
  bool get isValid {
    return configName.isNotEmpty &&
           clientId.isNotEmpty &&
           clientSecret.isNotEmpty;
  }

  bool get isGoogleDrive => configName == 'google_drive';
  bool get isOneDrive => configName == 'onedrive';

  // Generate Service Account JSON for Google Drive
  Map<String, dynamic>? get serviceAccountJson {
    if (!isGoogleDrive || projectId == null || privateKey == null || clientEmail == null) {
      return null;
    }

    return {
      'type': 'service_account',
      'project_id': projectId,
      'private_key_id': privateKeyId,
      'private_key': privateKey,
      'client_email': clientEmail,
      'client_id': clientId,
      'auth_uri': authUri ?? 'https://accounts.google.com/o/oauth2/auth',
      'token_uri': tokenUri ?? 'https://oauth2.googleapis.com/token',
      'auth_provider_x509_cert_url': authProviderCertUrl ?? 'https://www.googleapis.com/oauth2/v1/certs',
      'client_x509_cert_url': clientCertUrl,
      'universe_domain': universeDomain ?? 'googleapis.com',
    };
  }

  @override
  String toString() {
    return 'StorageConfigModel(id: $id, configName: $configName, isActive: $isActive, isDefault: $isDefault)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StorageConfigModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// App-wide storage configuration settings
class AppStorageSettings {
  final int maxImageSize; // in bytes
  final int maxImagesPerError;
  final List<String> supportedImageFormats;
  final int imageQuality; // 0-100
  final int maxImageWidth;
  final int maxImageHeight;
  final String deviceErrorsFolder;
  final String imagesSubfolder;
  final Duration imageCacheDuration;
  final int maxCacheSize; // in bytes

  const AppStorageSettings({
    this.maxImageSize = 3 * 1024 * 1024, // Reduced to 3MB
    this.maxImagesPerError = 5,
    this.supportedImageFormats = const ['jpg', 'jpeg', 'png', 'webp'], // Removed gif, added webp priority
    this.imageQuality = 75, // Reduced from 85 to 75
    this.maxImageWidth = 1600, // Reduced from 1920
    this.maxImageHeight = 900, // Reduced from 1080
    this.deviceErrorsFolder = 'HM_Device_Errors',
    this.imagesSubfolder = 'Images',
    this.imageCacheDuration = const Duration(days: 5), // Reduced from 7 to 5 days
    this.maxCacheSize = 50 * 1024 * 1024, // Reduced from 100MB to 50MB
  });

  Map<String, dynamic> toMap() {
    return {
      'maxImageSize': maxImageSize,
      'maxImagesPerError': maxImagesPerError,
      'supportedImageFormats': supportedImageFormats,
      'imageQuality': imageQuality,
      'maxImageWidth': maxImageWidth,
      'maxImageHeight': maxImageHeight,
      'deviceErrorsFolder': deviceErrorsFolder,
      'imagesSubfolder': imagesSubfolder,
      'imageCacheDurationDays': imageCacheDuration.inDays,
      'maxCacheSize': maxCacheSize,
    };
  }

  factory AppStorageSettings.fromMap(Map<String, dynamic> map) {
    return AppStorageSettings(
      maxImageSize: map['maxImageSize'] ?? 3 * 1024 * 1024, // Updated to 3MB
      maxImagesPerError: map['maxImagesPerError'] ?? 5,
      supportedImageFormats: List<String>.from(map['supportedImageFormats'] ??
          ['jpg', 'jpeg', 'png', 'webp']), // Updated formats
      imageQuality: map['imageQuality'] ?? 75, // Updated to 75
      maxImageWidth: map['maxImageWidth'] ?? 1600, // Updated to 1600
      maxImageHeight: map['maxImageHeight'] ?? 900, // Updated to 900
      deviceErrorsFolder: map['deviceErrorsFolder'] ?? 'HM_Device_Errors',
      imagesSubfolder: map['imagesSubfolder'] ?? 'Images',
      imageCacheDuration: Duration(days: map['imageCacheDurationDays'] ?? 5), // Updated to 5 days
      maxCacheSize: map['maxCacheSize'] ?? 50 * 1024 * 1024, // Updated to 50MB
    );
  }
}
