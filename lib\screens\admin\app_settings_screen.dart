import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/locale_provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/theme_provider.dart';
import '../../providers/remote_config_provider.dart';
import '../../widgets/app_logo.dart';
import '../../widgets/app_version_widget.dart';

class AppSettingsScreen extends StatefulWidget {
  const AppSettingsScreen({super.key});

  @override
  State<AppSettingsScreen> createState() => _AppSettingsScreenState();
}

class _AppSettingsScreenState extends State<AppSettingsScreen> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isRTL = localeProvider.isRTL;

    // Check admin access
    if (authProvider.user?.role.toString().split('.').last != 'admin') {
      return Scaffold(
        appBar: AppBar(
          title: Text(isRTL ? 'غير مصرح' : 'Unauthorized'),
        ),
        body: Center(
          child: Text(
            isRTL ? 'ليس لديك صلاحية للوصول لهذه الصفحة' : 'You don\'t have permission to access this page',
            style: const TextStyle(fontSize: 18),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isRTL ? 'إعدادات التطبيق' : 'App Settings',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: false,
        actions: [
          IconButton(
            onPressed: _isLoading ? null : _refreshSettings,
            icon: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                )
              : const Icon(Icons.refresh),
            tooltip: isRTL ? 'تحديث' : 'Refresh',
          ),
        ],
      ),
      body: _isLoading
        ? const Center(child: CircularProgressIndicator())
        : ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // App Information Card
              _buildAppInfoCard(context, isRTL),
              const SizedBox(height: 16),

              // Theme Settings Card
              _buildThemeSettingsCard(context, themeProvider, isRTL),
              const SizedBox(height: 16),

              // Language Settings Card
              _buildLanguageSettingsCard(context, localeProvider, isRTL),
              const SizedBox(height: 16),

              // Remote Config Status Card
              _buildRemoteConfigCard(context, isRTL),
              const SizedBox(height: 16),

              // System Information Card
              _buildSystemInfoCard(context, isRTL),
              const SizedBox(height: 16),

              // Actions Card
              _buildActionsCard(context, isRTL),
            ],
          ),
    );
  }

  Widget _buildAppInfoCard(BuildContext context, bool isRTL) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue[600]),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'معلومات التطبيق' : 'App Information',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Center(
              child: Column(
                children: [
                  const AppLogo(size: 64),
                  const SizedBox(height: 16),
                  Text(
                    isRTL ? 'أعطال الأجهزة المنزلية' : 'HM Device Errors',
                    style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  AppVersionInfo(isRTL: isRTL, showDetails: true),
                  const SizedBox(height: 8),
                  Text(
                    '© 2025 Mohamed Rady',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeSettingsCard(BuildContext context, ThemeProvider themeProvider, bool isRTL) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.palette, color: Colors.purple[600]),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'إعدادات المظهر' : 'Theme Settings',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: Text(isRTL ? 'الوضع المظلم' : 'Dark Mode'),
              subtitle: Text(isRTL ? 'تفعيل الوضع المظلم للتطبيق' : 'Enable dark theme for the app'),
              value: themeProvider.isDarkMode,
              onChanged: (value) {
                themeProvider.toggleTheme();
              },
              secondary: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withAlpha(25),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  themeProvider.isDarkMode ? Icons.dark_mode : Icons.light_mode,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageSettingsCard(BuildContext context, LocaleProvider localeProvider, bool isRTL) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.language, color: Colors.green[600]),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'إعدادات اللغة' : 'Language Settings',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: Text(isRTL ? 'اللغة الإنجليزية' : 'English Language'),
              subtitle: Text(
                localeProvider.locale.languageCode == 'ar'
                    ? (isRTL ? 'التبديل إلى الإنجليزية' : 'Switch to English')
                    : (isRTL ? 'التبديل إلى العربية' : 'Switch to Arabic')
              ),
              value: localeProvider.locale.languageCode == 'en',
              onChanged: (value) {
                localeProvider.toggleLocale();
              },
              secondary: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.secondary.withAlpha(25),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.language,
                  color: Theme.of(context).colorScheme.secondary,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRemoteConfigCard(BuildContext context, bool isRTL) {
    return Consumer<RemoteConfigProvider>(
      builder: (context, provider, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.cloud_sync, color: Colors.orange[600]),
                    const SizedBox(width: 8),
                    Text(
                      isRTL ? 'حالة التكوين البعيد' : 'Remote Config Status',
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                _buildStatusRow(
                  isRTL ? 'مهيأ' : 'Initialized',
                  provider.isInitialized,
                  isRTL,
                ),
                _buildStatusRow(
                  isRTL ? 'Google Drive مفعل' : 'Google Drive Enabled',
                  provider.isGoogleDriveEnabled(),
                  isRTL,
                ),
                _buildStatusRow(
                  isRTL ? 'التكوين البعيد كمصدر أساسي' : 'Remote Config Primary',
                  provider.useRemoteConfigAsPrimary(),
                  isRTL,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSystemInfoCard(BuildContext context, bool isRTL) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.computer, color: Colors.teal[600]),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'معلومات النظام' : 'System Information',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              isRTL ? 'المنصة' : 'Platform',
              Theme.of(context).platform.name,
              isRTL,
            ),
            _buildInfoRow(
              isRTL ? 'الوضع' : 'Mode',
              Theme.of(context).brightness == Brightness.dark
                ? (isRTL ? 'مظلم' : 'Dark')
                : (isRTL ? 'فاتح' : 'Light'),
              isRTL,
            ),
            _buildInfoRow(
              isRTL ? 'اتجاه النص' : 'Text Direction',
              isRTL ? 'من اليمين إلى اليسار' : 'Left to Right',
              isRTL,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionsCard(BuildContext context, bool isRTL) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.settings, color: Colors.red[600]),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'الإجراءات' : 'Actions',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _refreshSettings,
                icon: const Icon(Icons.refresh),
                label: Text(isRTL ? 'تحديث الإعدادات' : 'Refresh Settings'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => _showResetDialog(context, isRTL),
                icon: const Icon(Icons.restore),
                label: Text(isRTL ? 'إعادة تعيين الإعدادات' : 'Reset Settings'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, bool value, bool isRTL) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Icon(
            value ? Icons.check_circle : Icons.cancel,
            color: value ? Colors.green : Colors.red,
            size: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, bool isRTL) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: TextStyle(color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Future<void> _refreshSettings() async {
    setState(() => _isLoading = true);
    try {
      final remoteConfigProvider = Provider.of<RemoteConfigProvider>(context, listen: false);
      await remoteConfigProvider.fetchAndActivate();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              Provider.of<LocaleProvider>(context, listen: false).isRTL
                ? 'تم تحديث الإعدادات بنجاح'
                : 'Settings refreshed successfully',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showResetDialog(BuildContext context, bool isRTL) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'إعادة تعيين الإعدادات' : 'Reset Settings'),
        content: Text(
          isRTL
            ? 'هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟'
            : 'Are you sure you want to reset all settings to default values?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _resetSettings(isRTL);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: Text(isRTL ? 'إعادة تعيين' : 'Reset'),
          ),
        ],
      ),
    );
  }

  void _resetSettings(bool isRTL) {
    // Reset theme to light mode
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    if (themeProvider.isDarkMode) {
      themeProvider.toggleTheme();
    }

    // Reset language to Arabic
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    if (localeProvider.locale.languageCode == 'en') {
      localeProvider.toggleLocale();
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isRTL ? 'تم إعادة تعيين الإعدادات بنجاح' : 'Settings reset successfully',
        ),
        backgroundColor: Colors.green,
      ),
    );
  }
}
