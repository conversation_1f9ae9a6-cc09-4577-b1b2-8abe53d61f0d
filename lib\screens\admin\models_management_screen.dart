import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/manufacturer_provider.dart';
import '../../providers/locale_provider.dart';

class ModelsManagementScreen extends StatefulWidget {
  final String manufacturer;

  const ModelsManagementScreen({
    super.key,
    required this.manufacturer,
  });

  @override
  State<ModelsManagementScreen> createState() => _ModelsManagementScreenState();
}

class _ModelsManagementScreenState extends State<ModelsManagementScreen> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadModels();
  }

  Future<void> _loadModels() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final manufacturerProvider = Provider.of<ManufacturerProvider>(context, listen: false);
      await manufacturerProvider.fetchModelsForManufacturer(widget.manufacturer);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading models: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final manufacturerProvider = Provider.of<ManufacturerProvider>(context);
    final localeProvider = Provider.of<LocaleProvider>(context);
    final models = manufacturerProvider.getModelsForManufacturer(widget.manufacturer);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          localeProvider.isRTL
              ? 'موديلات ${widget.manufacturer}'
              : '${widget.manufacturer} Models',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: localeProvider.isRTL ? 'تحديث' : 'Refresh',
            onPressed: _loadModels,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : models.isEmpty
              ? Center(
                  child: Text(
                    localeProvider.isRTL
                        ? 'لا توجد موديلات لهذه الشركة المصنعة'
                        : 'No models found for this manufacturer',
                    style: const TextStyle(fontSize: 18),
                  ),
                )
              : ListView.builder(
                  itemCount: models.length,
                  itemBuilder: (context, index) {
                    final model = models[index];
                    return Card(
                      margin: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: ListTile(
                        title: Text(model),
                        subtitle: Text(
                          manufacturerProvider.getDeviceTypeForModel(widget.manufacturer, model),
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.secondary,
                          ),
                        ),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.edit),
                              tooltip: localeProvider.isRTL ? 'تعديل' : 'Edit',
                              onPressed: () => _showEditModelDialog(context, model),
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete),
                              tooltip: localeProvider.isRTL ? 'حذف' : 'Delete',
                              onPressed: () => _showDeleteModelDialog(context, model),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddModelDialog(context),
        tooltip: localeProvider.isRTL ? 'إضافة موديل' : 'Add Model',
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showAddModelDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final modelController = TextEditingController();
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final manufacturerProvider = Provider.of<ManufacturerProvider>(context, listen: false);
    final models = manufacturerProvider.getModelsForManufacturer(widget.manufacturer);

    String selectedDeviceType = 'Other';

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(
                localeProvider.isRTL ? 'إضافة موديل جديد' : 'Add New Model',
              ),
              content: Form(
                key: formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextFormField(
                      controller: modelController,
                      decoration: InputDecoration(
                        labelText: localeProvider.isRTL ? 'اسم الموديل' : 'Model Name',
                        border: const OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return localeProvider.isRTL
                              ? 'الرجاء إدخال اسم الموديل'
                              : 'Please enter a model name';
                        }
                        if (models.contains(value.trim())) {
                          return localeProvider.isRTL
                              ? 'هذا الموديل موجود بالفعل'
                              : 'This model already exists';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      decoration: InputDecoration(
                        labelText: localeProvider.isRTL ? 'نوع الجهاز' : 'Device Type',
                        border: const OutlineInputBorder(),
                      ),
                      value: selectedDeviceType,
                      items: manufacturerProvider.deviceTypes.map((type) {
                        return DropdownMenuItem<String>(
                          value: type,
                          child: Text(type),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            selectedDeviceType = value;
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(localeProvider.isRTL ? 'إلغاء' : 'Cancel'),
                ),
                TextButton(
                  onPressed: () async {
                    if (formKey.currentState!.validate()) {
                      final name = modelController.text.trim();
                      try {
                        await manufacturerProvider.addModel(
                          widget.manufacturer,
                          name,
                          deviceType: selectedDeviceType,
                        );
                        if (context.mounted) {
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                localeProvider.isRTL
                                    ? 'تمت إضافة الموديل بنجاح'
                                    : 'Model added successfully',
                              ),
                            ),
                          );
                        }
                      } catch (e) {
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Error: $e'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      }
                    }
                  },
                  child: Text(localeProvider.isRTL ? 'إضافة' : 'Add'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showEditModelDialog(BuildContext context, String model) {
    final formKey = GlobalKey<FormState>();
    final modelController = TextEditingController(text: model);
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final manufacturerProvider = Provider.of<ManufacturerProvider>(context, listen: false);
    final models = manufacturerProvider.getModelsForManufacturer(widget.manufacturer);

    // Get current device type
    String selectedDeviceType = manufacturerProvider.getDeviceTypeForModel(widget.manufacturer, model);

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(
                localeProvider.isRTL ? 'تعديل الموديل' : 'Edit Model',
              ),
              content: Form(
                key: formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextFormField(
                      controller: modelController,
                      decoration: InputDecoration(
                        labelText: localeProvider.isRTL ? 'اسم الموديل' : 'Model Name',
                        border: const OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return localeProvider.isRTL
                              ? 'الرجاء إدخال اسم الموديل'
                              : 'Please enter a model name';
                        }
                        if (value.trim() != model && models.contains(value.trim())) {
                          return localeProvider.isRTL
                              ? 'هذا الموديل موجود بالفعل'
                              : 'This model already exists';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      decoration: InputDecoration(
                        labelText: localeProvider.isRTL ? 'نوع الجهاز' : 'Device Type',
                        border: const OutlineInputBorder(),
                      ),
                      value: selectedDeviceType,
                      items: manufacturerProvider.deviceTypes.map((type) {
                        return DropdownMenuItem<String>(
                          value: type,
                          child: Text(type),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            selectedDeviceType = value;
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(localeProvider.isRTL ? 'إلغاء' : 'Cancel'),
                ),
                TextButton(
                  onPressed: () async {
                    if (formKey.currentState!.validate()) {
                      final newName = modelController.text.trim();
                      try {
                        await manufacturerProvider.updateModel(
                          widget.manufacturer,
                          model,
                          newName,
                          deviceType: selectedDeviceType,
                        );
                        if (context.mounted) {
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                localeProvider.isRTL
                                    ? 'تم تحديث الموديل بنجاح'
                                    : 'Model updated successfully',
                              ),
                            ),
                          );
                        }
                      } catch (e) {
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Error: $e'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      }
                    }
                  },
                  child: Text(localeProvider.isRTL ? 'حفظ' : 'Save'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showDeleteModelDialog(BuildContext context, String model) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final manufacturerProvider = Provider.of<ManufacturerProvider>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            localeProvider.isRTL ? 'حذف الموديل' : 'Delete Model',
          ),
          content: Text(
            localeProvider.isRTL
                ? 'هل أنت متأكد من حذف الموديل "$model"؟'
                : 'Are you sure you want to delete the model "$model"?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(localeProvider.isRTL ? 'إلغاء' : 'Cancel'),
            ),
            TextButton(
              onPressed: () async {
                try {
                  await manufacturerProvider.deleteModel(widget.manufacturer, model);
                  if (context.mounted) {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          localeProvider.isRTL
                              ? 'تم حذف الموديل بنجاح'
                              : 'Model deleted successfully',
                        ),
                      ),
                    );
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Error: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: Text(localeProvider.isRTL ? 'حذف' : 'Delete'),
            ),
          ],
        );
      },
    );
  }
}
