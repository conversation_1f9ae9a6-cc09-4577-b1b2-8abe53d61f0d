import 'dart:io';
import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import '../models/device_error_model.dart';
import '../models/category_model.dart';

class ExcelService {
  // Export errors to Excel
  Future<String?> exportErrorsToExcel(List<DeviceError> errors, {String? fileName}) async {
    try {
      final excel = Excel.createExcel();
      final sheet = excel['Errors'];

      // Remove default sheet if it exists
      if (excel.sheets.containsKey('Sheet1')) {
        excel.delete('Sheet1');
      }

      // Add headers
      final headers = [
        'ID',
        'Category ID',
        'Manufacturer',
        'Model',
        'Error Code',
        'Description',
        'Solution',
        'Image URLs',
        'Created At',
        'Created By',
        'Updated At',
        'Updated By',
        'Favorite Count'
      ];

      for (int i = 0; i < headers.length; i++) {
        final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
        cell.value = TextCellValue(headers[i]);
        cell.cellStyle = CellStyle(
          bold: true,
          backgroundColorHex: ExcelColor.blue,
          fontColorHex: ExcelColor.white,
        );
      }

      // Add data rows
      for (int i = 0; i < errors.length; i++) {
        final error = errors[i];
        final rowIndex = i + 1;

        final rowData = [
          error.id,
          error.categoryId,
          error.manufacturer,
          error.model,
          error.errorCode,
          error.description,
          error.solution,
          error.imageUrls.join('; '),
          error.createdAt.toIso8601String(),
          error.createdBy,
          error.updatedAt?.toIso8601String() ?? '',
          error.updatedBy ?? '',
          error.favoriteUsers.length.toString(),
        ];

        for (int j = 0; j < rowData.length; j++) {
          final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: rowIndex));
          cell.value = TextCellValue(rowData[j]);
        }
      }

      // Auto-fit columns
      for (int i = 0; i < headers.length; i++) {
        sheet.setColumnWidth(i, 20);
      }

      // Save file
      final directory = await getApplicationDocumentsDirectory();
      final filePath = '${directory.path}/${fileName ?? 'device_errors_${DateTime.now().millisecondsSinceEpoch}'}.xlsx';

      final fileBytes = excel.save();
      if (fileBytes != null) {
        final file = File(filePath);
        await file.writeAsBytes(fileBytes);
        return filePath;
      }

      return null;
    } catch (e) {
      throw Exception('Failed to export errors to Excel: $e');
    }
  }

  // Export categories to Excel
  Future<String?> exportCategoriesToExcel(List<Category> categories, {String? fileName}) async {
    try {
      final excel = Excel.createExcel();
      final sheet = excel['Categories'];

      // Remove default sheet if it exists
      if (excel.sheets.containsKey('Sheet1')) {
        excel.delete('Sheet1');
      }

      // Add headers
      final headers = ['ID', 'Name', 'Description', 'Icon', 'Created At'];

      for (int i = 0; i < headers.length; i++) {
        final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
        cell.value = TextCellValue(headers[i]);
        cell.cellStyle = CellStyle(
          bold: true,
          backgroundColorHex: ExcelColor.green,
          fontColorHex: ExcelColor.white,
        );
      }

      // Add data rows
      for (int i = 0; i < categories.length; i++) {
        final category = categories[i];
        final rowIndex = i + 1;

        final rowData = [
          category.id,
          category.nameAr,
          category.nameEn,
          category.description ?? '',
          category.iconName ?? '',
          category.createdAt.toIso8601String(),
        ];

        for (int j = 0; j < rowData.length; j++) {
          final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: rowIndex));
          cell.value = TextCellValue(rowData[j]);
        }
      }

      // Auto-fit columns
      for (int i = 0; i < headers.length; i++) {
        sheet.setColumnWidth(i, 20);
      }

      // Save file
      final directory = await getApplicationDocumentsDirectory();
      final filePath = '${directory.path}/${fileName ?? 'categories_${DateTime.now().millisecondsSinceEpoch}'}.xlsx';

      final fileBytes = excel.save();
      if (fileBytes != null) {
        final file = File(filePath);
        await file.writeAsBytes(fileBytes);
        return filePath;
      }

      return null;
    } catch (e) {
      throw Exception('Failed to export categories to Excel: $e');
    }
  }

  // Export manufacturers and models to Excel
  Future<String?> exportManufacturersToExcel(List<String> manufacturers, Map<String, List<String>> models, {String? fileName}) async {
    try {
      final excel = Excel.createExcel();
      final sheet = excel['Manufacturers'];

      // Remove default sheet if it exists
      if (excel.sheets.containsKey('Sheet1')) {
        excel.delete('Sheet1');
      }

      // Add headers
      final headers = ['Manufacturer', 'Model', 'Device Type'];

      for (int i = 0; i < headers.length; i++) {
        final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
        cell.value = TextCellValue(headers[i]);
        cell.cellStyle = CellStyle(
          bold: true,
          backgroundColorHex: ExcelColor.orange,
          fontColorHex: ExcelColor.white,
        );
      }

      // Add data rows
      int rowIndex = 1;
      for (final manufacturer in manufacturers) {
        final manufacturerModels = models[manufacturer] ?? [];

        if (manufacturerModels.isEmpty) {
          // Add manufacturer without models
          final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: rowIndex));
          cell.value = TextCellValue(manufacturer);
          rowIndex++;
        } else {
          // Add manufacturer with models
          for (final model in manufacturerModels) {
            final rowData = [manufacturer, model, ''];

            for (int j = 0; j < rowData.length; j++) {
              final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: rowIndex));
              cell.value = TextCellValue(rowData[j]);
            }
            rowIndex++;
          }
        }
      }

      // Auto-fit columns
      for (int i = 0; i < headers.length; i++) {
        sheet.setColumnWidth(i, 25);
      }

      // Save file
      final directory = await getApplicationDocumentsDirectory();
      final filePath = '${directory.path}/${fileName ?? 'manufacturers_${DateTime.now().millisecondsSinceEpoch}'}.xlsx';

      final fileBytes = excel.save();
      if (fileBytes != null) {
        final file = File(filePath);
        await file.writeAsBytes(fileBytes);
        return filePath;
      }

      return null;
    } catch (e) {
      throw Exception('Failed to export manufacturers to Excel: $e');
    }
  }

  // Import errors from Excel
  Future<List<DeviceError>> importErrorsFromExcel() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) {
        throw Exception('No file selected');
      }

      final file = result.files.first;
      final bytes = file.bytes;

      if (bytes == null) {
        throw Exception('Failed to read file');
      }

      final excel = Excel.decodeBytes(bytes);
      final sheet = excel.tables.values.first;

      if (sheet.rows.isEmpty) {
        throw Exception('No data found in Excel file');
      }

      final errors = <DeviceError>[];
      final rows = sheet.rows;

      // Skip header row
      for (int i = 1; i < rows.length; i++) {
        final row = rows[i];

        if (row.length < 7) continue; // Minimum required columns

        try {
          final error = DeviceError(
            id: row[0]?.value?.toString() ?? '',
            categoryId: row[1]?.value?.toString() ?? '',
            manufacturer: row[2]?.value?.toString() ?? '',
            model: row[3]?.value?.toString() ?? '',
            errorCode: row[4]?.value?.toString() ?? '',
            description: row[5]?.value?.toString() ?? '',
            solution: row[6]?.value?.toString() ?? '',
            imageUrls: row.length > 7 && row[7]?.value != null
                ? row[7]!.value.toString().split(';').map((e) => e.trim()).where((e) => e.isNotEmpty).toList()
                : [],
            createdAt: row.length > 8 && row[8]?.value != null
                ? DateTime.tryParse(row[8]!.value.toString()) ?? DateTime.now()
                : DateTime.now(),
            createdBy: row.length > 9 ? row[9]?.value?.toString() ?? '' : '',
            updatedAt: row.length > 10 && row[10]?.value != null
                ? DateTime.tryParse(row[10]!.value.toString())
                : null,
            updatedBy: row.length > 11 ? row[11]?.value?.toString() : null,
          );

          errors.add(error);
        } catch (e) {
          // Skip invalid rows
          continue;
        }
      }

      return errors;
    } catch (e) {
      throw Exception('Failed to import errors from Excel: $e');
    }
  }

  // Share Excel file
  Future<void> shareExcelFile(String filePath, String fileName) async {
    try {
      await Share.shareXFiles(
        [XFile(filePath)],
        text: 'Exported data: $fileName',
      );
    } catch (e) {
      throw Exception('Failed to share Excel file: $e');
    }
  }
}
