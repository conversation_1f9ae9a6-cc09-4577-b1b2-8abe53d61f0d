const functions = require('firebase-functions');
const admin = require('firebase-admin');

admin.initializeApp();

// Send FCM notification when a new notification document is created
exports.sendNotification = functions.firestore
  .document('notifications/{notificationId}')
  .onCreate(async (snap, context) => {
    const notificationData = snap.data();

    try {
      let message;
      let recipients = [];

      // Determine recipients based on target
      if (notificationData.target_audience === 'all_users') {
        // Send to all users via topic
        message = {
          topic: 'all_users',
          notification: {
            title: notificationData.title_en,
            body: notificationData.body_en,
          },
          data: {
            type: notificationData.type,
            error_id: notificationData.error_id || '',
            error_code: notificationData.error_code || '',
            manufacturer: notificationData.manufacturer || '',
            model: notificationData.model || '',
            title_ar: notificationData.title_ar,
            body_ar: notificationData.body_ar,
            priority: notificationData.priority || 'normal',
            auto_generated: String(notificationData.auto_generated || false),
            ...notificationData.data,
          },
          android: {
            notification: {
              icon: 'ic_notification',
              color: notificationData.priority === 'critical' ? '#F44336' :
                     notificationData.priority === 'high' ? '#FF9800' : '#2196F3',
              sound: 'default',
              priority: notificationData.priority === 'critical' ? 'max' : 'high',
            },
          },
          apns: {
            payload: {
              aps: {
                sound: 'default',
                badge: 1,
                'content-available': 1,
              },
            },
          },
        };
      } else if (notificationData.target_group) {
        // Send to specific group
        recipients = await getGroupRecipients(notificationData.target_group);

        // Send to each user in the group
        for (const userId of recipients) {
          try {
            const userTokenDoc = await admin.firestore()
              .collection('user_tokens')
              .doc(userId)
              .get();

            if (userTokenDoc.exists) {
              const userToken = userTokenDoc.data().fcm_token;

              const userMessage = {
                token: userToken,
                notification: {
                  title: notificationData.title_en,
                  body: notificationData.body_en,
                },
                data: {
                  type: notificationData.type,
                  title_ar: notificationData.title_ar,
                  body_ar: notificationData.body_ar,
                  priority: notificationData.priority || 'normal',
                  ...notificationData.data,
                },
                android: {
                  notification: {
                    icon: 'ic_notification',
                    color: notificationData.priority === 'critical' ? '#F44336' :
                           notificationData.priority === 'high' ? '#FF9800' : '#2196F3',
                    sound: 'default',
                  },
                },
                apns: {
                  payload: {
                    aps: {
                      sound: 'default',
                      badge: 1,
                    },
                  },
                },
              };

              await admin.messaging().send(userMessage);
            }
          } catch (error) {
            console.log(`Error sending to user ${userId}:`, error);
          }
        }
      } else if (notificationData.target_user_id) {
        // Send to specific user
        const userTokenDoc = await admin.firestore()
          .collection('user_tokens')
          .doc(notificationData.target_user_id)
          .get();

        if (!userTokenDoc.exists) {
          console.log('User token not found for user:', notificationData.target_user_id);
          return null;
        }

        const userToken = userTokenDoc.data().fcm_token;

        message = {
          token: userToken,
          notification: {
            title: notificationData.title_en,
            body: notificationData.body_en,
          },
          data: {
            type: notificationData.type,
            title_ar: notificationData.title_ar,
            body_ar: notificationData.body_ar,
            ...notificationData.data,
          },
          android: {
            notification: {
              icon: 'ic_notification',
              color: '#2196F3',
              sound: 'default',
            },
          },
          apns: {
            payload: {
              aps: {
                sound: 'default',
                badge: 1,
              },
            },
          },
        };
      }

      if (message) {
        const response = await admin.messaging().send(message);
        console.log('Successfully sent message:', response);

        // Update notification document with sent status
        await snap.ref.update({
          sent_at: admin.firestore.FieldValue.serverTimestamp(),
          message_id: response,
          status: 'sent',
        });
      }

    } catch (error) {
      console.error('Error sending notification:', error);

      // Update notification document with error status
      await snap.ref.update({
        error_at: admin.firestore.FieldValue.serverTimestamp(),
        error_message: error.message,
        status: 'failed',
      });
    }

    return null;
  });

// Clean up old notifications (run daily)
exports.cleanupOldNotifications = functions.pubsub
  .schedule('0 2 * * *') // Run at 2 AM daily
  .timeZone('UTC')
  .onRun(async (context) => {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const oldNotifications = await admin.firestore()
      .collection('notifications')
      .where('created_at', '<', thirtyDaysAgo)
      .get();

    const batch = admin.firestore().batch();
    oldNotifications.docs.forEach(doc => {
      batch.delete(doc.ref);
    });

    await batch.commit();
    console.log(`Deleted ${oldNotifications.size} old notifications`);

    return null;
  });

// Monitor user session and send expiration warnings
exports.checkUserSessions = functions.pubsub
  .schedule('*/30 * * * *') // Run every 30 minutes
  .timeZone('UTC')
  .onRun(async (context) => {
    const now = new Date();
    const warningTime = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24 hours from now

    // Get users whose accounts will expire within 24 hours
    const expiringUsers = await admin.firestore()
      .collection('users')
      .where('expiryDate', '<=', warningTime)
      .where('expiryDate', '>', now)
      .where('isActive', '==', true)
      .get();

    for (const userDoc of expiringUsers.docs) {
      const userData = userDoc.data();

      // Check if we already sent a warning today
      const today = new Date().toDateString();
      if (userData.lastExpiryWarning === today) {
        continue;
      }

      // Send expiration warning notification
      await admin.firestore().collection('notifications').add({
        type: 'account_expiry_warning',
        title_ar: 'تحذير انتهاء الحساب',
        title_en: 'Account Expiry Warning',
        body_ar: 'سينتهي حسابك قريباً. يرجى التواصل مع المدير لتجديد الحساب.',
        body_en: 'Your account will expire soon. Please contact the administrator to renew your account.',
        target_user_id: userDoc.id,
        created_at: admin.firestore.FieldValue.serverTimestamp(),
      });

      // Update user document to mark warning as sent
      await userDoc.ref.update({
        lastExpiryWarning: today,
      });
    }

    console.log(`Processed ${expiringUsers.size} expiring users`);
    return null;
  });

// Automatically deactivate expired users
exports.deactivateExpiredUsers = functions.pubsub
  .schedule('0 1 * * *') // Run at 1 AM daily
  .timeZone('UTC')
  .onRun(async (context) => {
    const now = new Date();

    // Get users whose accounts have expired
    const expiredUsers = await admin.firestore()
      .collection('users')
      .where('expiryDate', '<', now)
      .where('isActive', '==', true)
      .get();

    const batch = admin.firestore().batch();

    for (const userDoc of expiredUsers.docs) {
      // Deactivate the user
      batch.update(userDoc.ref, {
        isActive: false,
        deactivatedAt: admin.firestore.FieldValue.serverTimestamp(),
        deactivationReason: 'Account expired',
      });

      // Send deactivation notification
      await admin.firestore().collection('notifications').add({
        type: 'account_deactivated',
        title_ar: 'تم إيقاف الحساب',
        title_en: 'Account Deactivated',
        body_ar: 'تم إيقاف حسابك بسبب انتهاء الصلاحية. يرجى التواصل مع المدير.',
        body_en: 'Your account has been deactivated due to expiry. Please contact the administrator.',
        target_user_id: userDoc.id,
        created_at: admin.firestore.FieldValue.serverTimestamp(),
      });
    }

    await batch.commit();
    console.log(`Deactivated ${expiredUsers.size} expired users`);

    return null;
  });

// Log security incidents
exports.logSecurityIncident = functions.firestore
  .document('security_incidents/{incidentId}')
  .onCreate(async (snap, context) => {
    const incidentData = snap.data();

    console.log('Security incident logged:', {
      type: incidentData.type,
      timestamp: incidentData.timestamp,
      platform: incidentData.platform,
      device_id: incidentData.device_id,
    });

    // Send alert to admins for critical security incidents
    if (incidentData.type === 'signature_verification_failed') {
      const adminUsers = await admin.firestore()
        .collection('users')
        .where('role', '==', 'admin')
        .where('isActive', '==', true)
        .get();

      for (const adminDoc of adminUsers.docs) {
        await admin.firestore().collection('notifications').add({
          type: 'security_alert',
          title_ar: 'تحذير أمني',
          title_en: 'Security Alert',
          body_ar: 'تم اكتشاف محاولة تلاعب بالتطبيق',
          body_en: 'App tampering attempt detected',
          target_user_id: adminDoc.id,
          data: {
            incident_id: context.params.incidentId,
            incident_type: incidentData.type,
          },
          created_at: admin.firestore.FieldValue.serverTimestamp(),
        });
      }
    }

    return null;
  });

// Helper function to get group recipients
async function getGroupRecipients(groupType) {
  let query;

  switch (groupType) {
    case 'admins':
      query = admin.firestore()
        .collection('users')
        .where('role', '==', 'admin')
        .where('isActive', '==', true);
      break;
    case 'active_users':
      query = admin.firestore()
        .collection('users')
        .where('isActive', '==', true);
      break;
    case 'expired_users':
      const now = new Date();
      query = admin.firestore()
        .collection('users')
        .where('expiryDate', '<', now)
        .where('isActive', '==', true);
      break;
    case 'premium_users':
      query = admin.firestore()
        .collection('users')
        .where('isPremium', '==', true)
        .where('isActive', '==', true);
      break;
    default:
      return [];
  }

  const snapshot = await query.get();
  return snapshot.docs.map(doc => doc.id);
}

// Scheduled function to send daily summary notifications
exports.sendDailySummary = functions.pubsub
  .schedule('0 18 * * *') // Run at 6 PM daily
  .timeZone('UTC')
  .onRun(async (context) => {
    try {
      const today = new Date();
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);

      // Get today's error count
      const todayErrors = await admin.firestore()
        .collection('device_errors')
        .where('createdAt', '>=', yesterday)
        .get();

      const errorCount = todayErrors.size;

      if (errorCount > 0) {
        // Send daily summary notification
        await admin.firestore().collection('notifications').add({
          type: 'daily_summary',
          title_ar: 'ملخص يومي',
          title_en: 'Daily Summary',
          body_ar: `تم إضافة ${errorCount} أخطاء جديدة اليوم`,
          body_en: `${errorCount} new errors were added today`,
          error_count: errorCount,
          date: admin.firestore.FieldValue.serverTimestamp(),
          target_audience: 'all_users',
          priority: 'normal',
          auto_generated: true,
        });

        console.log(`Daily summary sent: ${errorCount} errors`);
      }
    } catch (error) {
      console.error('Error sending daily summary:', error);
    }

    return null;
  });

// Function to handle emergency broadcasts
exports.sendEmergencyBroadcast = functions.https.onCall(async (data, context) => {
  // Verify admin authentication
  if (!context.auth || !context.auth.token.admin) {
    throw new functions.https.HttpsError('permission-denied', 'Only admins can send emergency broadcasts');
  }

  try {
    await admin.firestore().collection('notifications').add({
      type: 'emergency_broadcast',
      title_ar: data.title_ar,
      title_en: data.title_en,
      body_ar: data.body_ar,
      body_en: data.body_en,
      target_audience: 'all_users',
      priority: 'critical',
      emergency: true,
      created_by: context.auth.uid,
      created_at: admin.firestore.FieldValue.serverTimestamp(),
      auto_generated: false,
    });

    return { success: true, message: 'Emergency broadcast sent' };
  } catch (error) {
    console.error('Error sending emergency broadcast:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send emergency broadcast');
  }
});