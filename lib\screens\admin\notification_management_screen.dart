import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/theme_provider.dart';
import '../../services/auto_notification_service.dart';

class NotificationManagementScreen extends StatefulWidget {
  const NotificationManagementScreen({super.key});

  @override
  State<NotificationManagementScreen> createState() => _NotificationManagementScreenState();
}

class _NotificationManagementScreenState extends State<NotificationManagementScreen> {
  bool _isLoading = false;
  AutoNotificationSettings _settings = const AutoNotificationSettings();
  Map<String, dynamic> _stats = {};

  final _titleArController = TextEditingController();
  final _titleEnController = TextEditingController();
  final _bodyArController = TextEditingController();
  final _bodyEnController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _titleArController.dispose();
    _titleEnController.dispose();
    _bodyArController.dispose();
    _bodyEnController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final autoNotificationService = AutoNotificationService.instance;
      await autoNotificationService.initialize();

      final settings = autoNotificationService.settings;
      final stats = await autoNotificationService.getNotificationStats();

      setState(() {
        _settings = settings;
        _stats = stats;
      });
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDark = themeProvider.isDarkMode;

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'إدارة الإشعارات التلقائية',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _loadData,
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: isDark
                ? [Colors.grey[900]!, Colors.grey[800]!]
                : [Colors.blue[50]!, Colors.white],
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : RefreshIndicator(
                onRefresh: _loadData,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSettingsCard(isDark),
                      const SizedBox(height: 16),
                      _buildStatsCard(isDark),
                      const SizedBox(height: 16),
                      _buildQuickActionsCard(isDark),
                      const SizedBox(height: 16),
                      _buildCustomNotificationCard(isDark),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildSettingsCard(bool isDark) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isDark
              ? Theme.of(context).colorScheme.outline.withAlpha(120)
              : const Color(0xFF2563EB).withAlpha(100), // حدود زرقاء فاتحة
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
              ? Colors.black.withAlpha(120)
              : const Color(0xFF2563EB).withAlpha(40), // ظل أزرق فاتح
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: isDark
              ? Colors.black.withAlpha(60)
              : const Color(0xFF2563EB).withAlpha(20), // ظل ثانوي
            blurRadius: 6,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Card(
        elevation: 0,
        margin: EdgeInsets.zero,
        color: isDark
            ? Theme.of(context).colorScheme.surface
            : const Color(0xFFF8FAFF), // خلفية زرقاء فاتحة جداً
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isDark ? [
                Theme.of(context).colorScheme.surface,
                Theme.of(context).colorScheme.surface.withAlpha(230),
              ] : [
                const Color(0xFFF8FAFF), // زرقاء فاتحة جداً
                const Color(0xFFF1F5FF), // زرقاء فاتحة أكثر قليلاً
                const Color(0xFFEBF2FF), // زرقاء فاتحة أكثر
              ],
              stops: isDark ? null : [0.0, 0.5, 1.0],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.settings,
                  color: isDark ? Colors.blue[300] : Colors.blue[600],
                ),
                const SizedBox(width: 8),
                Text(
                  'إعدادات الإشعارات التلقائية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('تفعيل الإشعارات التلقائية'),
              subtitle: const Text('تفعيل أو إيقاف جميع الإشعارات التلقائية'),
              value: _settings.isEnabled,
              onChanged: (value) => _updateSetting('isEnabled', value),
            ),
            const Divider(),
            SwitchListTile(
              title: const Text('إشعارات الأخطاء الجديدة'),
              subtitle: const Text('إرسال إشعار عند إضافة خطأ جديد'),
              value: _settings.notifyOnNewErrors,
              onChanged: _settings.isEnabled
                  ? (value) => _updateSetting('notifyOnNewErrors', value)
                  : null,
            ),
            SwitchListTile(
              title: const Text('إشعارات التحديثات'),
              subtitle: const Text('إرسال إشعار عند توفر تحديث جديد'),
              value: _settings.notifyOnUpdates,
              onChanged: _settings.isEnabled
                  ? (value) => _updateSetting('notifyOnUpdates', value)
                  : null,
            ),
            SwitchListTile(
              title: const Text('الإشعارات الدورية'),
              subtitle: const Text('إرسال ملخص دوري للمستخدمين'),
              value: _settings.enablePeriodicNotifications,
              onChanged: _settings.isEnabled
                  ? (value) => _updateSetting('enablePeriodicNotifications', value)
                  : null,
            ),
            if (_settings.enablePeriodicNotifications) ...[
              const SizedBox(height: 8),
              ListTile(
                title: const Text('فترة الإشعارات الدورية'),
                subtitle: Text('كل ${_settings.periodicIntervalHours} ساعة'),
                trailing: DropdownButton<int>(
                  value: _settings.periodicIntervalHours,
                  items: [6, 12, 24, 48, 72].map((hours) {
                    return DropdownMenuItem(
                      value: hours,
                      child: Text('$hours ساعة'),
                    );
                  }).toList(),
                  onChanged: (value) => _updateSetting('periodicIntervalHours', value),
                ),
              ),
            ],
            SwitchListTile(
              title: const Text('إشعارات الصيانة'),
              subtitle: const Text('إرسال إشعار عند جدولة صيانة'),
              value: _settings.notifyOnMaintenance,
              onChanged: _settings.isEnabled
                  ? (value) => _updateSetting('notifyOnMaintenance', value)
                  : null,
            ),
            SwitchListTile(
              title: const Text('التنبيهات الأمنية'),
              subtitle: const Text('إرسال إشعار عند اكتشاف مشاكل أمنية'),
              value: _settings.notifyOnSecurityAlerts,
              onChanged: _settings.isEnabled
                  ? (value) => _updateSetting('notifyOnSecurityAlerts', value)
                  : null,
            ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatsCard(bool isDark) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isDark
              ? Theme.of(context).colorScheme.outline.withAlpha(120)
              : const Color(0xFF2563EB).withAlpha(100), // حدود زرقاء فاتحة
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
              ? Colors.black.withAlpha(120)
              : const Color(0xFF2563EB).withAlpha(40), // ظل أزرق فاتح
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: isDark
              ? Colors.black.withAlpha(60)
              : const Color(0xFF2563EB).withAlpha(20), // ظل ثانوي
            blurRadius: 6,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Card(
        elevation: 0,
        margin: EdgeInsets.zero,
        color: isDark
            ? Theme.of(context).colorScheme.surface
            : const Color(0xFFF8FAFF), // خلفية زرقاء فاتحة جداً
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isDark ? [
                Theme.of(context).colorScheme.surface,
                Theme.of(context).colorScheme.surface.withAlpha(230),
              ] : [
                const Color(0xFFF8FAFF), // زرقاء فاتحة جداً
                const Color(0xFFF1F5FF), // زرقاء فاتحة أكثر قليلاً
                const Color(0xFFEBF2FF), // زرقاء فاتحة أكثر
              ],
              stops: isDark ? null : [0.0, 0.5, 1.0],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: isDark ? Colors.green[300] : Colors.green[600],
                ),
                const SizedBox(width: 8),
                Text(
                  'إحصائيات الإشعارات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatItem('آخر 24 ساعة', '${_stats['last_24_hours'] ?? 0}', isDark),
            _buildStatItem('آخر 7 أيام', '${_stats['last_7_days'] ?? 0}', isDark),
            _buildStatItem('تلقائية (7 أيام)', '${_stats['auto_generated_last_7_days'] ?? 0}', isDark),
            _buildStatItem('يدوية (7 أيام)', '${_stats['manual_notifications_last_7_days'] ?? 0}', isDark),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, bool isDark) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: isDark ? Colors.grey[300] : Colors.grey[700],
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsCard(bool isDark) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isDark
              ? Theme.of(context).colorScheme.outline.withAlpha(120)
              : const Color(0xFF2563EB).withAlpha(100), // حدود زرقاء فاتحة
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
              ? Colors.black.withAlpha(120)
              : const Color(0xFF2563EB).withAlpha(40), // ظل أزرق فاتح
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: isDark
              ? Colors.black.withAlpha(60)
              : const Color(0xFF2563EB).withAlpha(20), // ظل ثانوي
            blurRadius: 6,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Card(
        elevation: 0,
        margin: EdgeInsets.zero,
        color: isDark
            ? Theme.of(context).colorScheme.surface
            : const Color(0xFFF8FAFF), // خلفية زرقاء فاتحة جداً
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isDark ? [
                Theme.of(context).colorScheme.surface,
                Theme.of(context).colorScheme.surface.withAlpha(230),
              ] : [
                const Color(0xFFF8FAFF), // زرقاء فاتحة جداً
                const Color(0xFFF1F5FF), // زرقاء فاتحة أكثر قليلاً
                const Color(0xFFEBF2FF), // زرقاء فاتحة أكثر
              ],
              stops: isDark ? null : [0.0, 0.5, 1.0],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.flash_on,
                  color: isDark ? Colors.orange[300] : Colors.orange[600],
                ),
                const SizedBox(width: 8),
                Text(
                  'إجراءات سريعة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : () => _sendQuickNotification('daily_summary'),
                    icon: const Icon(Icons.today),
                    label: const Text('ملخص يومي'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isDark ? Colors.blue[700] : Colors.blue[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : () => _sendQuickNotification('test'),
                    icon: const Icon(Icons.bug_report),
                    label: const Text('إشعار تجريبي'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isDark ? Colors.green[700] : Colors.green[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : () => _sendGroupNotification('admins'),
                    icon: const Icon(Icons.admin_panel_settings),
                    label: const Text('للمديرين'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isDark ? Colors.purple[700] : Colors.purple[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : () => _sendGroupNotification('active_users'),
                    icon: const Icon(Icons.people),
                    label: const Text('للمستخدمين النشطين'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isDark ? Colors.teal[700] : Colors.teal[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCustomNotificationCard(bool isDark) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isDark
              ? Theme.of(context).colorScheme.outline.withAlpha(120)
              : const Color(0xFF2563EB).withAlpha(100), // حدود زرقاء فاتحة
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
              ? Colors.black.withAlpha(120)
              : const Color(0xFF2563EB).withAlpha(40), // ظل أزرق فاتح
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: isDark
              ? Colors.black.withAlpha(60)
              : const Color(0xFF2563EB).withAlpha(20), // ظل ثانوي
            blurRadius: 6,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Card(
        elevation: 0,
        margin: EdgeInsets.zero,
        color: isDark
            ? Theme.of(context).colorScheme.surface
            : const Color(0xFFF8FAFF), // خلفية زرقاء فاتحة جداً
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isDark ? [
                Theme.of(context).colorScheme.surface,
                Theme.of(context).colorScheme.surface.withAlpha(230),
              ] : [
                const Color(0xFFF8FAFF), // زرقاء فاتحة جداً
                const Color(0xFFF1F5FF), // زرقاء فاتحة أكثر قليلاً
                const Color(0xFFEBF2FF), // زرقاء فاتحة أكثر
              ],
              stops: isDark ? null : [0.0, 0.5, 1.0],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.create,
                  color: isDark ? Colors.red[300] : Colors.red[600],
                ),
                const SizedBox(width: 8),
                Text(
                  'إشعار مخصص',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _titleArController,
              decoration: const InputDecoration(
                labelText: 'العنوان (عربي)',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _titleEnController,
              decoration: const InputDecoration(
                labelText: 'العنوان (إنجليزي)',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _bodyArController,
              decoration: const InputDecoration(
                labelText: 'المحتوى (عربي)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _bodyEnController,
              decoration: const InputDecoration(
                labelText: 'المحتوى (إنجليزي)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _sendCustomNotification,
                icon: const Icon(Icons.send),
                label: const Text('إرسال لجميع المستخدمين'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: isDark ? Colors.red[700] : Colors.red[600],
                  foregroundColor: Colors.white,
                ),
              ),
            ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _updateSetting(String key, dynamic value) async {
    try {
      AutoNotificationSettings newSettings;

      switch (key) {
        case 'isEnabled':
          newSettings = _settings.copyWith(isEnabled: value);
          break;
        case 'notifyOnNewErrors':
          newSettings = _settings.copyWith(notifyOnNewErrors: value);
          break;
        case 'notifyOnUpdates':
          newSettings = _settings.copyWith(notifyOnUpdates: value);
          break;
        case 'enablePeriodicNotifications':
          newSettings = _settings.copyWith(enablePeriodicNotifications: value);
          break;
        case 'periodicIntervalHours':
          newSettings = _settings.copyWith(periodicIntervalHours: value);
          break;
        case 'notifyOnMaintenance':
          newSettings = _settings.copyWith(notifyOnMaintenance: value);
          break;
        case 'notifyOnSecurityAlerts':
          newSettings = _settings.copyWith(notifyOnSecurityAlerts: value);
          break;
        default:
          return;
      }

      await AutoNotificationService.instance.updateSettings(newSettings);

      setState(() {
        _settings = newSettings;
      });

      _showSuccessSnackBar('تم تحديث الإعدادات بنجاح');
    } catch (e) {
      _showErrorSnackBar('خطأ في تحديث الإعدادات: $e');
    }
  }

  Future<void> _sendQuickNotification(String type) async {
    try {
      final autoNotificationService = AutoNotificationService.instance;

      switch (type) {
        case 'daily_summary':
          await autoNotificationService.sendCustomNotificationToAll(
            titleAr: 'ملخص يومي',
            titleEn: 'Daily Summary',
            bodyAr: 'هذا ملخص يومي تجريبي للتطبيق',
            bodyEn: 'This is a test daily summary for the app',
            priority: 'normal',
          );
          break;
        case 'test':
          await autoNotificationService.sendCustomNotificationToAll(
            titleAr: 'إشعار تجريبي',
            titleEn: 'Test Notification',
            bodyAr: 'هذا إشعار تجريبي للتأكد من عمل النظام',
            bodyEn: 'This is a test notification to verify the system is working',
            priority: 'normal',
          );
          break;
      }

      _showSuccessSnackBar('تم إرسال الإشعار بنجاح');
    } catch (e) {
      _showErrorSnackBar('خطأ في إرسال الإشعار: $e');
    }
  }

  Future<void> _sendGroupNotification(String group) async {
    try {
      final autoNotificationService = AutoNotificationService.instance;

      String titleAr, titleEn, bodyAr, bodyEn;

      switch (group) {
        case 'admins':
          titleAr = 'رسالة للمديرين';
          titleEn = 'Message for Admins';
          bodyAr = 'هذه رسالة خاصة بالمديرين';
          bodyEn = 'This is a message for administrators';
          break;
        case 'active_users':
          titleAr = 'رسالة للمستخدمين النشطين';
          titleEn = 'Message for Active Users';
          bodyAr = 'شكراً لكم على استخدام التطبيق';
          bodyEn = 'Thank you for using the app';
          break;
        default:
          return;
      }

      await autoNotificationService.sendNotificationToGroup(
        groupType: group,
        titleAr: titleAr,
        titleEn: titleEn,
        bodyAr: bodyAr,
        bodyEn: bodyEn,
        priority: 'normal',
      );

      _showSuccessSnackBar('تم إرسال الإشعار للمجموعة بنجاح');
    } catch (e) {
      _showErrorSnackBar('خطأ في إرسال الإشعار للمجموعة: $e');
    }
  }

  Future<void> _sendCustomNotification() async {
    if (_titleArController.text.isEmpty ||
        _titleEnController.text.isEmpty ||
        _bodyArController.text.isEmpty ||
        _bodyEnController.text.isEmpty) {
      _showErrorSnackBar('يرجى ملء جميع الحقول');
      return;
    }

    try {
      await AutoNotificationService.instance.sendCustomNotificationToAll(
        titleAr: _titleArController.text,
        titleEn: _titleEnController.text,
        bodyAr: _bodyArController.text,
        bodyEn: _bodyEnController.text,
        priority: 'normal',
      );

      // Clear the form
      _titleArController.clear();
      _titleEnController.clear();
      _bodyArController.clear();
      _bodyEnController.clear();

      _showSuccessSnackBar('تم إرسال الإشعار المخصص بنجاح');
    } catch (e) {
      _showErrorSnackBar('خطأ في إرسال الإشعار المخصص: $e');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
