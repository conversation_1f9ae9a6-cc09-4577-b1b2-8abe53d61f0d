import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

class EncryptionService {
  static const String _keyString = 'HM_DEVICE_ERRORS_2024_SECURE_KEY_32';
  static late final encrypt.Key _key;
  static late final encrypt.IV _iv;
  static late final encrypt.Encrypter _encrypter;

  static void initialize() {
    try {
      // Create a proper 32-byte (256-bit) key
      final keyString = _keyString.padRight(32, '0').substring(0, 32);
      final keyBytes = utf8.encode(keyString);

      // Ensure exactly 32 bytes for AES-256
      final paddedKeyBytes = Uint8List(32);
      for (int i = 0; i < 32; i++) {
        paddedKeyBytes[i] = i < keyBytes.length ? keyBytes[i] : 0;
      }

      _key = encrypt.Key(paddedKeyBytes);

      // Use a fixed IV derived from the key for consistency
      final ivString = 'HM_DEVICE_IV_16'.padRight(16, '0').substring(0, 16);
      final ivBytes = utf8.encode(ivString);
      final paddedIvBytes = Uint8List(16);
      for (int i = 0; i < 16; i++) {
        paddedIvBytes[i] = i < ivBytes.length ? ivBytes[i] : 0;
      }
      _iv = encrypt.IV(paddedIvBytes);

      _encrypter = encrypt.Encrypter(encrypt.AES(_key));

      debugPrint('EncryptionService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing EncryptionService: $e');
      // Fallback initialization with fixed values
      _key = encrypt.Key.fromBase64('SGVsbG9Xb3JsZEhlbGxvV29ybGRIZWxsb1dvcmxkSGVsbG8=');
      _iv = encrypt.IV.fromBase64('SGVsbG9Xb3JsZEhlbGxvVw==');
      _encrypter = encrypt.Encrypter(encrypt.AES(_key));
    }
  }

  // تشفير النصوص
  static String encryptText(String plainText) {
    final encrypted = _encrypter.encrypt(plainText, iv: _iv);
    return encrypted.base64;
  }

  // فك تشفير النصوص
  static String decryptText(String encryptedText) {
    final encrypted = encrypt.Encrypted.fromBase64(encryptedText);
    return _encrypter.decrypt(encrypted, iv: _iv);
  }

  // تشفير البيانات الثنائية
  static Uint8List encryptBytes(Uint8List data) {
    final plainText = base64.encode(data);
    final encrypted = _encrypter.encrypt(plainText, iv: _iv);
    return encrypted.bytes;
  }

  // فك تشفير البيانات الثنائية
  static Uint8List decryptBytes(Uint8List encryptedData) {
    final encrypted = encrypt.Encrypted(encryptedData);
    final decryptedText = _encrypter.decrypt(encrypted, iv: _iv);
    return base64.decode(decryptedText);
  }

  // تحميل وفك تشفير ملف service account (deprecated - use database config instead)
  static Future<Map<String, dynamic>> loadServiceAccount() async {
    throw Exception('Service account loading from assets is deprecated. Use database configuration instead.');
  }

  // تشفير وحفظ ملف service account
  static Future<Uint8List> encryptServiceAccount(Map<String, dynamic> serviceAccount) async {
    final jsonString = jsonEncode(serviceAccount);
    final jsonBytes = utf8.encode(jsonString);
    return encryptBytes(jsonBytes);
  }

  // تشفير الصور والملفات
  static Future<Uint8List> encryptFile(Uint8List fileData) async {
    return encryptBytes(fileData);
  }

  // فك تشفير الصور والملفات
  static Future<Uint8List> decryptFile(Uint8List encryptedData) async {
    return decryptBytes(encryptedData);
  }

  // إنشاء hash للتحقق من سلامة البيانات
  static String createHash(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // التحقق من hash
  static bool verifyHash(String data, String hash) {
    return createHash(data) == hash;
  }

  // تشفير متقدم للملفات الحساسة
  static Future<Map<String, dynamic>> encryptSensitiveData({
    required Map<String, dynamic> serviceAccount,
    required List<String> apiKeys,
    required Map<String, String> secrets,
  }) async {
    final encryptedData = <String, dynamic>{};

    // تشفير service account
    encryptedData['service_account'] = encryptText(jsonEncode(serviceAccount));

    // تشفير API keys
    encryptedData['api_keys'] = apiKeys.map((key) => encryptText(key)).toList();

    // تشفير الأسرار
    final encryptedSecrets = <String, String>{};
    secrets.forEach((key, value) {
      encryptedSecrets[key] = encryptText(value);
    });
    encryptedData['secrets'] = encryptedSecrets;

    // إضافة timestamp وhash للتحقق
    encryptedData['timestamp'] = DateTime.now().millisecondsSinceEpoch;
    encryptedData['hash'] = createHash(jsonEncode(encryptedData));

    return encryptedData;
  }

  // فك تشفير البيانات الحساسة
  static Future<Map<String, dynamic>> decryptSensitiveData(Map<String, dynamic> encryptedData) async {
    try {
      final decryptedData = <String, dynamic>{};

      // فك تشفير service account
      if (encryptedData.containsKey('service_account')) {
        final decryptedServiceAccount = decryptText(encryptedData['service_account']);
        decryptedData['service_account'] = jsonDecode(decryptedServiceAccount);
      }

      // فك تشفير API keys
      if (encryptedData.containsKey('api_keys')) {
        final encryptedKeys = List<String>.from(encryptedData['api_keys']);
        decryptedData['api_keys'] = encryptedKeys.map((key) => decryptText(key)).toList();
      }

      // فك تشفير الأسرار
      if (encryptedData.containsKey('secrets')) {
        final encryptedSecrets = Map<String, String>.from(encryptedData['secrets']);
        final decryptedSecrets = <String, String>{};
        encryptedSecrets.forEach((key, value) {
          decryptedSecrets[key] = decryptText(value);
        });
        decryptedData['secrets'] = decryptedSecrets;
      }

      return decryptedData;
    } catch (e) {
      throw Exception('Failed to decrypt sensitive data: $e');
    }
  }
}

// خدمة حماية الأصول
class AssetProtectionService {
  static const List<String> _protectedAssets = [
    'assets/credentials/',
    'assets/keys/',
    'assets/config/',
  ];

  // التحقق من محاولة الوصول للأصول المحمية
  static bool isProtectedAsset(String assetPath) {
    return _protectedAssets.any((protected) => assetPath.startsWith(protected));
  }

  // تحميل أصل محمي
  static Future<ByteData> loadProtectedAsset(String assetPath) async {
    if (!isProtectedAsset(assetPath)) {
      return await rootBundle.load(assetPath);
    }

    // للأصول المحمية، نحتاج لفك التشفير
    try {
      final encryptedData = await rootBundle.load('$assetPath.encrypted');
      final decryptedBytes = EncryptionService.decryptBytes(
        encryptedData.buffer.asUint8List()
      );
      return ByteData.sublistView(Uint8List.fromList(decryptedBytes));
    } catch (e) {
      throw Exception('Failed to load protected asset: $assetPath');
    }
  }

  // إخفاء الملفات الحساسة في البناء
  static Future<void> obfuscateAssets() async {
    // هذه الدالة تستخدم أثناء البناء لتشفير الملفات
    // يتم استدعاؤها من build script
  }
}

// خدمة حماية الكود
class CodeProtectionService {


  // استرجاع المتغيرات المشوشة
  static String _deobfuscateString(String obfuscated) {
    final bytes = base64.decode(obfuscated);
    final deobfuscated = bytes.map((byte) => byte ^ 0xAA).toList();
    return utf8.decode(deobfuscated);
  }

  // الحصول على مفاتيح API بشكل آمن
  static String getApiKey(String keyName) {
    // المفاتيح مشفرة ومخزنة بشكل آمن
    const obfuscatedKeys = {
      'google_drive': 'encrypted_google_drive_key_here',
      'firebase': 'encrypted_firebase_key_here',
    };

    if (obfuscatedKeys.containsKey(keyName)) {
      return _deobfuscateString(obfuscatedKeys[keyName]!);
    }

    throw Exception('API key not found: $keyName');
  }

  // التحقق من سلامة التطبيق
  static Future<bool> verifyAppIntegrity() async {
    try {
      // التحقق من التوقيع
      // التحقق من عدم وجود تعديلات
      // التحقق من البيئة
      return true;
    } catch (e) {
      return false;
    }
  }
}
