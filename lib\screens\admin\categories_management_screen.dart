import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/device_category_model.dart';
import '../../providers/category_provider.dart';
import '../../providers/locale_provider.dart';
import '../../widgets/gradient_background.dart';

class CategoriesManagementScreen extends StatefulWidget {
  const CategoriesManagementScreen({super.key});

  @override
  State<CategoriesManagementScreen> createState() =>
      _CategoriesManagementScreenState();
}

class _CategoriesManagementScreenState
    extends State<CategoriesManagementScreen> {
  @override
  void initState() {
    super.initState();
    // Fetch categories when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CategoryProvider>(context, listen: false).fetchCategories();
    });
  }

  @override
  Widget build(BuildContext context) {
    final categoryProvider = Provider.of<CategoryProvider>(context);
    final localeProvider = Provider.of<LocaleProvider>(context);

    return GradientScaffold(
      appBar: AppBar(
        title: Text(
          localeProvider.isRTL ? 'إدارة الفئات' : 'Category Management',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => categoryProvider.fetchCategories(),
          ),
        ],
      ),
      body: categoryProvider.isLoading
          ? const Center(child: CircularProgressIndicator())
          : categoryProvider.categories.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.category,
                        size: 80,
                        color: Theme.of(context).colorScheme.primary.withAlpha(128),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        localeProvider.isRTL
                            ? 'لا توجد فئات'
                            : 'No categories found',
                        style: const TextStyle(fontSize: 18),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => _showAddCategoryDialog(context),
                        child: Text(
                          localeProvider.isRTL
                              ? 'إضافة فئة جديدة'
                              : 'Add New Category',
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(8),
                  itemCount: categoryProvider.categories.length,
                  itemBuilder: (context, index) {
                    final category = categoryProvider.categories[index];
                    final isDark = Theme.of(context).brightness == Brightness.dark;

                    return Container(
                      margin: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: isDark
                              ? Theme.of(context).colorScheme.outline.withAlpha(120)
                              : const Color(0xFF2563EB).withAlpha(100), // حدود زرقاء فاتحة
                          width: 1.5,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: isDark
                              ? Colors.black.withAlpha(120)
                              : const Color(0xFF2563EB).withAlpha(40), // ظل أزرق فاتح
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                            spreadRadius: 1,
                          ),
                          BoxShadow(
                            color: isDark
                              ? Colors.black.withAlpha(60)
                              : const Color(0xFF2563EB).withAlpha(20), // ظل ثانوي
                            blurRadius: 6,
                            offset: const Offset(0, 2),
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: Card(
                        elevation: 0,
                        margin: EdgeInsets.zero,
                        color: isDark
                            ? Theme.of(context).colorScheme.surface
                            : const Color(0xFFF8FAFF), // خلفية زرقاء فاتحة جداً
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: isDark ? [
                                Theme.of(context).colorScheme.surface,
                                Theme.of(context).colorScheme.surface.withAlpha(230),
                              ] : [
                                const Color(0xFFF8FAFF), // زرقاء فاتحة جداً
                                const Color(0xFFF1F5FF), // زرقاء فاتحة أكثر قليلاً
                                const Color(0xFFEBF2FF), // زرقاء فاتحة أكثر
                              ],
                              stops: isDark ? null : [0.0, 0.5, 1.0],
                            ),
                          ),
                          child: ListTile(
                            contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                        leading: Icon(
                          _getCategoryIcon(category.id),
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        title: Text(
                          category.name.isNotEmpty && !categoryProvider.isFirebaseId(category.name)
                              ? category.name
                              : 'فئة غير محددة',
                        ),
                        subtitle: Text(category.description ?? ''),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              localeProvider.isRTL
                                  ? '${category.deviceCount} جهاز'
                                  : '${category.deviceCount} devices',
                            ),
                            IconButton(
                              icon: const Icon(Icons.edit),
                              onPressed: () =>
                                  _showEditCategoryDialog(context, category),
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete),
                              onPressed: () =>
                                  _showDeleteCategoryDialog(context, category),
                            ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                  },
                ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddCategoryDialog(context),
        tooltip: localeProvider.isRTL ? 'إضافة فئة' : 'Add Category',
        child: const Icon(Icons.add),
      ),
    );
  }

  IconData _getCategoryIcon(String categoryId) {
    switch (categoryId.toLowerCase()) {
      case 'washing_machines':
        return Icons.local_laundry_service;
      case 'refrigerators':
        return Icons.kitchen;
      case 'air_conditioners':
        return Icons.ac_unit;
      case 'tvs':
        return Icons.tv;
      case 'microwaves':
        return Icons.microwave;
      case 'dishwashers':
        return Icons.wash;
      case 'ovens':
        return Icons.countertops;
      default:
        return Icons.devices_other;
    }
  }

  void _showAddCategoryDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    final idController = TextEditingController();
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            localeProvider.isRTL ? 'إضافة فئة جديدة' : 'Add New Category',
          ),
          content: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: nameController,
                    decoration: InputDecoration(
                      labelText: localeProvider.isRTL ? 'اسم الفئة' : 'Category Name',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return localeProvider.isRTL
                            ? 'الرجاء إدخال اسم الفئة'
                            : 'Please enter a category name';
                      }
                      return null;
                    },
                  ),
                  TextFormField(
                    controller: idController,
                    decoration: InputDecoration(
                      labelText: localeProvider.isRTL ? 'معرف الفئة' : 'Category ID',
                      hintText: localeProvider.isRTL
                          ? 'مثال: washing_machines'
                          : 'Example: washing_machines',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return localeProvider.isRTL
                            ? 'الرجاء إدخال معرف الفئة'
                            : 'Please enter a category ID';
                      }
                      if (value.contains(' ') || value.contains('/')) {
                        return localeProvider.isRTL
                            ? 'المعرف يجب ألا يحتوي على مسافات أو /'
                            : 'ID should not contain spaces or /';
                      }
                      return null;
                    },
                  ),
                  TextFormField(
                    controller: descriptionController,
                    decoration: InputDecoration(
                      labelText:
                          localeProvider.isRTL ? 'وصف الفئة' : 'Category Description',
                    ),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(localeProvider.isRTL ? 'إلغاء' : 'Cancel'),
            ),
            TextButton(
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  final categoryProvider =
                      Provider.of<CategoryProvider>(context, listen: false);
                  final newCategory = DeviceCategory(
                    id: idController.text.trim(),
                    name: nameController.text.trim(),
                    description: descriptionController.text.trim(),
                    deviceCount: 0,
                  );

                  final success = await categoryProvider.addCategory(newCategory);
                  if (success && context.mounted) {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          localeProvider.isRTL
                              ? 'تمت إضافة الفئة بنجاح'
                              : 'Category added successfully',
                        ),
                      ),
                    );
                  } else if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          localeProvider.isRTL
                              ? 'فشل إضافة الفئة'
                              : 'Failed to add category',
                        ),
                      ),
                    );
                  }
                }
              },
              child: Text(localeProvider.isRTL ? 'إضافة' : 'Add'),
            ),
          ],
        );
      },
    );
  }

  void _showEditCategoryDialog(BuildContext context, DeviceCategory category) {
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController(text: category.name);
    final descriptionController =
        TextEditingController(text: category.description);
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            localeProvider.isRTL ? 'تعديل الفئة' : 'Edit Category',
          ),
          content: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: nameController,
                    decoration: InputDecoration(
                      labelText: localeProvider.isRTL ? 'اسم الفئة' : 'Category Name',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return localeProvider.isRTL
                            ? 'الرجاء إدخال اسم الفئة'
                            : 'Please enter a category name';
                      }
                      return null;
                    },
                  ),
                  TextFormField(
                    controller: descriptionController,
                    decoration: InputDecoration(
                      labelText:
                          localeProvider.isRTL ? 'وصف الفئة' : 'Category Description',
                    ),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(localeProvider.isRTL ? 'إلغاء' : 'Cancel'),
            ),
            TextButton(
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  final categoryProvider =
                      Provider.of<CategoryProvider>(context, listen: false);
                  final updatedCategory = category.copyWith(
                    name: nameController.text.trim(),
                    description: descriptionController.text.trim(),
                  );

                  final success =
                      await categoryProvider.updateCategory(updatedCategory);
                  if (success && context.mounted) {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          localeProvider.isRTL
                              ? 'تم تحديث الفئة بنجاح'
                              : 'Category updated successfully',
                        ),
                      ),
                    );
                  } else if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          localeProvider.isRTL
                              ? 'فشل تحديث الفئة'
                              : 'Failed to update category',
                        ),
                      ),
                    );
                  }
                }
              },
              child: Text(localeProvider.isRTL ? 'حفظ' : 'Save'),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteCategoryDialog(BuildContext context, DeviceCategory category) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);

    // Get safe category name
    final safeCategoryName = category.name.isNotEmpty && !categoryProvider.isFirebaseId(category.name)
        ? category.name
        : (localeProvider.isRTL ? 'فئة غير محددة' : 'Undefined Category');

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            localeProvider.isRTL ? 'حذف الفئة' : 'Delete Category',
          ),
          content: Text(
            localeProvider.isRTL
                ? 'هل أنت متأكد من حذف فئة "$safeCategoryName"؟'
                : 'Are you sure you want to delete the category "$safeCategoryName"?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(localeProvider.isRTL ? 'إلغاء' : 'Cancel'),
            ),
            TextButton(
              onPressed: () async {
                final categoryProvider =
                    Provider.of<CategoryProvider>(context, listen: false);
                final success =
                    await categoryProvider.deleteCategory(category.id);
                if (success && context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        localeProvider.isRTL
                            ? 'تم حذف الفئة بنجاح'
                            : 'Category deleted successfully',
                      ),
                    ),
                  );
                } else if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        localeProvider.isRTL
                            ? 'فشل حذف الفئة'
                            : 'Failed to delete category',
                      ),
                    ),
                  );
                }
              },
              child: Text(
                localeProvider.isRTL ? 'حذف' : 'Delete',
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
            ),
          ],
        );
      },
    );
  }
}
