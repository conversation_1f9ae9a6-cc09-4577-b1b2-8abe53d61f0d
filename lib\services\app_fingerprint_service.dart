import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'encryption_service.dart';
import '../utils/platform_utils.dart';

class AppFingerprintService {
  static const String _fingerprintKey = 'app_fingerprint_hm_device_errors_2024';
  static String? _cachedFingerprint;
  static Map<String, dynamic>? _deviceInfo;

  /// Generate a unique fingerprint for the app
  static Future<String> generateFingerprint() async {
    if (_cachedFingerprint != null) {
      return _cachedFingerprint!;
    }

    try {
      final components = await _collectFingerprintComponents();
      final fingerprintData = _createFingerprintData(components);
      final fingerprint = _hashFingerprint(fingerprintData);

      _cachedFingerprint = fingerprint;
      return fingerprint;
    } catch (e) {
      debugPrint('Error generating fingerprint: $e');
      return _generateFallbackFingerprint();
    }
  }

  /// Collect all components needed for fingerprint
  static Future<Map<String, dynamic>> _collectFingerprintComponents() async {
    final components = <String, dynamic>{};

    // App information
    final packageInfo = await PackageInfo.fromPlatform();
    components['app_name'] = packageInfo.appName;
    components['package_name'] = packageInfo.packageName;
    components['version'] = packageInfo.version;
    components['build_number'] = packageInfo.buildNumber;

    // Device information
    final deviceInfo = await _getDeviceInfo();
    components.addAll(deviceInfo);

    // Platform information
    components['platform'] = PlatformUtils.operatingSystem;
    components['platform_version'] = PlatformUtils.operatingSystemVersion;

    // App-specific constants
    components['app_id'] = 'hm_device_errors_2024';
    components['developer'] = 'Mohamed Rady';
    components['fingerprint_version'] = '1.0';

    // Build configuration
    components['is_release'] = kReleaseMode;
    components['is_debug'] = kDebugMode;
    components['is_profile'] = kProfileMode;

    return components;
  }

  /// Get device-specific information
  static Future<Map<String, dynamic>> _getDeviceInfo() async {
    if (_deviceInfo != null) {
      return _deviceInfo!;
    }

    final deviceInfo = DeviceInfoPlugin();
    final info = <String, dynamic>{};

    try {
      if (PlatformUtils.isWeb) {
        final webInfo = await deviceInfo.webBrowserInfo;
        info['device_type'] = 'web';
        info['browser_name'] = webInfo.browserName.name;
        info['app_name'] = webInfo.appName;
        info['app_version'] = webInfo.appVersion;
        info['user_agent'] = webInfo.userAgent;
        info['platform'] = webInfo.platform;
        info['vendor'] = webInfo.vendor;
      } else if (PlatformUtils.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        info['device_type'] = 'android';
        info['device_model'] = androidInfo.model;
        info['device_brand'] = androidInfo.brand;
        info['device_manufacturer'] = androidInfo.manufacturer;
        info['android_version'] = androidInfo.version.release;
        info['sdk_int'] = androidInfo.version.sdkInt;
        info['device_id'] = androidInfo.id;
        info['hardware'] = androidInfo.hardware;
        info['board'] = androidInfo.board;
        info['bootloader'] = androidInfo.bootloader;
        info['fingerprint'] = androidInfo.fingerprint;
      } else if (PlatformUtils.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        info['device_type'] = 'ios';
        info['device_model'] = iosInfo.model;
        info['device_name'] = iosInfo.name;
        info['system_name'] = iosInfo.systemName;
        info['system_version'] = iosInfo.systemVersion;
        info['identifier_for_vendor'] = iosInfo.identifierForVendor;
        info['is_physical_device'] = iosInfo.isPhysicalDevice;
      } else if (PlatformUtils.isWindows) {
        final windowsInfo = await deviceInfo.windowsInfo;
        info['device_type'] = 'windows';
        info['computer_name'] = windowsInfo.computerName;
        info['user_name'] = windowsInfo.userName;
        info['major_version'] = windowsInfo.majorVersion;
        info['minor_version'] = windowsInfo.minorVersion;
        info['build_number'] = windowsInfo.buildNumber;
      } else if (PlatformUtils.isLinux) {
        final linuxInfo = await deviceInfo.linuxInfo;
        info['device_type'] = 'linux';
        info['name'] = linuxInfo.name;
        info['version'] = linuxInfo.version;
        info['id'] = linuxInfo.id;
        info['machine'] = linuxInfo.machineId ?? 'unknown';
      } else if (PlatformUtils.isMacOS) {
        final macInfo = await deviceInfo.macOsInfo;
        info['device_type'] = 'macos';
        info['computer_name'] = macInfo.computerName;
        info['host_name'] = macInfo.hostName;
        info['arch'] = macInfo.arch;
        info['model'] = macInfo.model;
        info['kernel_version'] = macInfo.kernelVersion;
        info['major_version'] = macInfo.majorVersion;
        info['minor_version'] = macInfo.minorVersion;
      }
    } catch (e) {
      debugPrint('Error getting device info: $e');
      info['device_type'] = 'unknown';
      info['error'] = e.toString();
    }

    _deviceInfo = info;
    return info;
  }

  /// Create fingerprint data from components
  static Map<String, dynamic> _createFingerprintData(Map<String, dynamic> components) {
    return {
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'components': components,
      'salt': _fingerprintKey,
      'version': '1.0.0',
    };
  }

  /// Hash the fingerprint data
  static String _hashFingerprint(Map<String, dynamic> data) {
    final jsonString = jsonEncode(data);
    final bytes = utf8.encode(jsonString);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Generate fallback fingerprint if main generation fails
  static String _generateFallbackFingerprint() {
    final fallbackData = {
      'app_id': 'hm_device_errors_2024',
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'platform': PlatformUtils.operatingSystem,
      'salt': _fingerprintKey,
      'fallback': true,
    };

    final jsonString = jsonEncode(fallbackData);
    final bytes = utf8.encode(jsonString);
    final digest = sha256.convert(bytes);
    return 'fallback_${digest.toString()}';
  }

  /// Verify app integrity using fingerprint
  static Future<bool> verifyAppIntegrity() async {
    try {
      final currentFingerprint = await generateFingerprint();
      final storedFingerprint = await _getStoredFingerprint();

      if (storedFingerprint == null) {
        // First run, store the fingerprint
        await _storeFingerprint(currentFingerprint);
        return true;
      }

      // Check if fingerprint matches
      final isValid = _validateFingerprint(currentFingerprint, storedFingerprint);

      if (!isValid) {
        debugPrint('App integrity check failed!');
        await _handleIntegrityFailure();
      }

      return isValid;
    } catch (e) {
      debugPrint('Error verifying app integrity: $e');
      return false;
    }
  }

  /// Get stored fingerprint
  static Future<String?> _getStoredFingerprint() async {
    try {
      // In a real implementation, this would be stored securely
      // For now, we'll use a simple approach
      return null; // Always return null for first implementation
    } catch (e) {
      debugPrint('Error getting stored fingerprint: $e');
      return null;
    }
  }

  /// Store fingerprint securely
  static Future<void> _storeFingerprint(String fingerprint) async {
    try {
      // In a real implementation, store this securely
      // Could use secure storage, keychain, or encrypted preferences
      debugPrint('Storing fingerprint: ${fingerprint.substring(0, 16)}...');
    } catch (e) {
      debugPrint('Error storing fingerprint: $e');
    }
  }

  /// Validate fingerprint
  static bool _validateFingerprint(String current, String stored) {
    // Simple comparison for now
    // In production, you might want more sophisticated validation
    return current == stored;
  }

  /// Handle integrity failure
  static Future<void> _handleIntegrityFailure() async {
    // Log the incident
    debugPrint('SECURITY ALERT: App integrity check failed!');

    // In production, you might want to:
    // - Send alert to server
    // - Disable certain features
    // - Show warning to user
    // - Exit the app
  }

  /// Get app signature (for additional security)
  static Future<String?> getAppSignature() async {
    try {
      if (PlatformUtils.isAndroid) {
        // Get Android app signature
        return await _getAndroidSignature();
      } else if (PlatformUtils.isIOS) {
        // Get iOS bundle signature info
        return await _getIOSSignature();
      }
      return null;
    } catch (e) {
      debugPrint('Error getting app signature: $e');
      return null;
    }
  }

  /// Get Android app signature
  static Future<String?> _getAndroidSignature() async {
    try {
      // This would require platform-specific code
      // For now, return a placeholder
      return 'android_signature_placeholder';
    } catch (e) {
      debugPrint('Error getting Android signature: $e');
      return null;
    }
  }

  /// Get iOS app signature
  static Future<String?> _getIOSSignature() async {
    try {
      // This would require platform-specific code
      // For now, return a placeholder
      return 'ios_signature_placeholder';
    } catch (e) {
      debugPrint('Error getting iOS signature: $e');
      return null;
    }
  }

  /// Generate secure app token
  static Future<String> generateSecureToken() async {
    try {
      final fingerprint = await generateFingerprint();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final signature = await getAppSignature();

      final tokenData = {
        'fingerprint': fingerprint,
        'timestamp': timestamp,
        'signature': signature,
        'app_id': 'hm_device_errors_2024',
      };

      final jsonString = jsonEncode(tokenData);
      final encryptedToken = EncryptionService.encryptText(jsonString);

      return encryptedToken;
    } catch (e) {
      debugPrint('Error generating secure token: $e');
      rethrow;
    }
  }

  /// Verify secure token
  static Future<bool> verifySecureToken(String token) async {
    try {
      final decryptedJson = EncryptionService.decryptText(token);
      final tokenData = jsonDecode(decryptedJson) as Map<String, dynamic>;

      final storedFingerprint = tokenData['fingerprint'] as String?;
      final currentFingerprint = await generateFingerprint();

      if (storedFingerprint != currentFingerprint) {
        return false;
      }

      final timestamp = tokenData['timestamp'] as int?;
      if (timestamp == null) {
        return false;
      }

      // Check if token is not too old (e.g., 24 hours)
      final tokenAge = DateTime.now().millisecondsSinceEpoch - timestamp;
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

      if (tokenAge > maxAge) {
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('Error verifying secure token: $e');
      return false;
    }
  }

  /// Get fingerprint info for debugging
  static Future<Map<String, dynamic>> getFingerprintInfo() async {
    try {
      final components = await _collectFingerprintComponents();
      final fingerprint = await generateFingerprint();

      return {
        'fingerprint': fingerprint,
        'components': components,
        'generated_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('Error getting fingerprint info: $e');
      return {'error': e.toString()};
    }
  }

  /// Clear cached data
  static void clearCache() {
    _cachedFingerprint = null;
    _deviceInfo = null;
  }
}
