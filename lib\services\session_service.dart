import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/user_model.dart';
import 'auth_service.dart';

class SessionService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static Timer? _sessionTimer;
  static StreamSubscription<DocumentSnapshot>? _userSubscription;
  static Function()? _onSessionExpired;
  static Function()? _onUserDeactivated;
  
  static bool _isMonitoring = false;
  static const Duration _checkInterval = Duration(minutes: 1); // Check every minute

  /// Start monitoring user session
  static void startSessionMonitoring({
    Function()? onSessionExpired,
    Function()? onUserDeactivated,
  }) {
    if (_isMonitoring) return;

    _onSessionExpired = onSessionExpired;
    _onUserDeactivated = onUserDeactivated;
    
    _isMonitoring = true;
    
    // Start periodic session checks
    _sessionTimer = Timer.periodic(_checkInterval, (timer) {
      _checkUserSession();
    });

    // Listen to user document changes in real-time
    _startUserDocumentListener();
    
    debugPrint('Session monitoring started');
  }

  /// Stop monitoring user session
  static void stopSessionMonitoring() {
    _sessionTimer?.cancel();
    _sessionTimer = null;
    
    _userSubscription?.cancel();
    _userSubscription = null;
    
    _isMonitoring = false;
    _onSessionExpired = null;
    _onUserDeactivated = null;
    
    debugPrint('Session monitoring stopped');
  }

  /// Check current user session validity
  static Future<void> _checkUserSession() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        debugPrint('No authenticated user found');
        return;
      }

      // Get user data from Firestore
      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) {
        debugPrint('User document not found, signing out');
        await _signOutUser();
        return;
      }

      final userData = UserModel.fromFirestore(userDoc);
      
      // Check if user is still active
      if (!userData.isActive) {
        debugPrint('User account deactivated, signing out');
        await _signOutUser();
        _onUserDeactivated?.call();
        return;
      }

      // Check if user account has expired
      if (userData.isExpired) {
        debugPrint('User account expired, signing out');
        await _signOutUser();
        _onSessionExpired?.call();
        return;
      }

      // Update last activity timestamp
      await _updateLastActivity(currentUser.uid);
      
    } catch (e) {
      debugPrint('Error checking user session: $e');
    }
  }

  /// Start listening to user document changes
  static void _startUserDocumentListener() {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    _userSubscription = _firestore
        .collection('users')
        .doc(currentUser.uid)
        .snapshots()
        .listen(
      (snapshot) {
        if (!snapshot.exists) {
          debugPrint('User document deleted, signing out');
          _signOutUser();
          return;
        }

        final userData = UserModel.fromFirestore(snapshot);
        
        // Check if user was deactivated
        if (!userData.isActive) {
          debugPrint('User deactivated in real-time, signing out');
          _signOutUser();
          _onUserDeactivated?.call();
          return;
        }

        // Check if user expired
        if (userData.isExpired) {
          debugPrint('User expired in real-time, signing out');
          _signOutUser();
          _onSessionExpired?.call();
          return;
        }
      },
      onError: (error) {
        debugPrint('Error listening to user document: $error');
      },
    );
  }

  /// Update user's last activity timestamp
  static Future<void> _updateLastActivity(String userId) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'lastActivity': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error updating last activity: $e');
    }
  }

  /// Sign out user
  static Future<void> _signOutUser() async {
    try {
      stopSessionMonitoring();
      await AuthService().signOut();
    } catch (e) {
      debugPrint('Error signing out user: $e');
    }
  }

  /// Check if user session is valid (one-time check)
  static Future<bool> isSessionValid() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return false;

      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) return false;

      final userData = UserModel.fromFirestore(userDoc);
      return userData.isActive && !userData.isExpired;
    } catch (e) {
      debugPrint('Error checking session validity: $e');
      return false;
    }
  }

  /// Force refresh user session
  static Future<void> refreshSession() async {
    await _checkUserSession();
  }

  /// Get session status
  static bool get isMonitoring => _isMonitoring;

  /// Manual session validation (call this on app resume)
  static Future<void> validateSessionOnResume() async {
    if (!_isMonitoring) return;
    
    debugPrint('Validating session on app resume');
    await _checkUserSession();
  }

  /// Set custom session expired callback
  static void setSessionExpiredCallback(Function() callback) {
    _onSessionExpired = callback;
  }

  /// Set custom user deactivated callback
  static void setUserDeactivatedCallback(Function() callback) {
    _onUserDeactivated = callback;
  }

  /// Check if user has been inactive for too long
  static Future<bool> isUserInactive({Duration maxInactivity = const Duration(hours: 24)}) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return true;

      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) return true;

      final userData = userDoc.data() as Map<String, dynamic>;
      final lastActivity = userData['lastActivity'] as Timestamp?;
      
      if (lastActivity == null) return false; // No activity recorded yet

      final lastActivityTime = lastActivity.toDate();
      final now = DateTime.now();
      final inactiveDuration = now.difference(lastActivityTime);

      return inactiveDuration > maxInactivity;
    } catch (e) {
      debugPrint('Error checking user inactivity: $e');
      return false;
    }
  }

  /// Update user online status
  static Future<void> updateOnlineStatus(bool isOnline) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      await _firestore.collection('users').doc(currentUser.uid).update({
        'isOnline': isOnline,
        'lastSeen': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error updating online status: $e');
    }
  }

  /// Set user as offline when app is closed
  static Future<void> setUserOffline() async {
    await updateOnlineStatus(false);
  }

  /// Set user as online when app is opened
  static Future<void> setUserOnline() async {
    await updateOnlineStatus(true);
  }
}
