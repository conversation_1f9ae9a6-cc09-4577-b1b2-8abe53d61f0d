import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:url_launcher/url_launcher.dart';

/// Widget for loading images from Google Drive with better error handling
class GoogleDriveImageWidget extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;

  const GoogleDriveImageWidget({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
  });

  @override
  State<GoogleDriveImageWidget> createState() => _GoogleDriveImageWidgetState();
}

class _GoogleDriveImageWidgetState extends State<GoogleDriveImageWidget> {
  Uint8List? _imageData;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  @override
  void didUpdateWidget(GoogleDriveImageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imageUrl != widget.imageUrl) {
      _loadImage();
    }
  }

  Future<void> _loadImage() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = null;
      _imageData = null;
    });

    try {
      // Extract file ID from Google Drive URL
      final fileId = _extractFileId(widget.imageUrl);
      if (fileId == null) {
        throw Exception('Invalid Google Drive URL');
      }

      // For web platform, use different approach due to CORS restrictions
      if (kIsWeb) {
        // On web, we can't load Google Drive images directly due to CORS
        // Show a placeholder with a link to open the image in a new tab
        if (mounted) {
          setState(() {
            _isLoading = false;
            _hasError = true;
            _errorMessage = 'Click to view image in new tab (CORS restriction)';
          });
        }
        return;
      }

      // Try multiple URL formats for Google Drive (non-web platforms)
      // استراتيجيات متعددة لحل مشكلة 403
      final urls = [
        // أولاً: جرب thumbnail URL (الأكثر نجاحاً)
        'https://drive.google.com/thumbnail?id=$fileId&sz=w1000',
        'https://drive.google.com/thumbnail?id=$fileId&sz=w800',
        'https://drive.google.com/thumbnail?id=$fileId&sz=w600',
        // ثانياً: جرب export URLs
        'https://drive.google.com/uc?export=view&id=$fileId',
        'https://drive.google.com/uc?export=download&id=$fileId',
        // ثالثاً: جرب direct file access
        'https://drive.google.com/file/d/$fileId/view?usp=sharing',
        // رابعاً: جرب proxy URLs
        'https://lh3.googleusercontent.com/d/$fileId',
      ];

      Uint8List? imageData;
      String? lastError;

      for (final url in urls) {
        try {
          debugPrint('Trying to load image from: $url');

          // استخدام headers محسنة لتجنب 403
          final headers = <String, String>{
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
          };

          // إضافة headers خاصة حسب نوع URL
          if (url.contains('thumbnail')) {
            headers['Referer'] = 'https://drive.google.com/';
          } else if (url.contains('googleusercontent')) {
            headers['Referer'] = 'https://photos.google.com/';
          }

          final response = await http.get(
            Uri.parse(url),
            headers: headers,
          );

          if (response.statusCode == 200) {
            // Check if response is actually an image
            final contentType = response.headers['content-type'] ?? '';
            if (contentType.startsWith('image/') ||
                contentType.startsWith('application/octet-stream')) {

              imageData = response.bodyBytes;
              debugPrint('Successfully loaded image from: $url');
              break;
            } else {
              lastError = 'Response is not an image (Content-Type: $contentType)';
              debugPrint('Invalid content type from $url: $contentType');
            }
          } else {
            lastError = 'HTTP ${response.statusCode}: ${response.reasonPhrase}';
            debugPrint('Failed to load from $url: ${response.statusCode}');
          }
        } catch (e) {
          lastError = e.toString();
          debugPrint('Error loading from $url: $e');
        }
      }

      if (mounted) {
        setState(() {
          _imageData = imageData;
          _isLoading = false;
          _hasError = imageData == null;
          _errorMessage = lastError;
        });
      }
    } catch (e) {
      debugPrint('Error loading Google Drive image: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = e.toString();
        });
      }
    }
  }

  String? _extractFileId(String url) {
    // Try different patterns to extract file ID
    final patterns = [
      RegExp(r'/file/d/([a-zA-Z0-9-_]+)'),
      RegExp(r'id=([a-zA-Z0-9-_]+)'),
      RegExp(r'/([a-zA-Z0-9-_]+)/view'),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(url);
      if (match != null && match.groupCount > 0) {
        return match.group(1);
      }
    }
    return null;
  }

  Future<void> _openImageInNewTab() async {
    try {
      final fileId = _extractFileId(widget.imageUrl);
      if (fileId != null) {
        final url = 'https://drive.google.com/file/d/$fileId/view';
        final uri = Uri.parse(url);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        }
      }
    } catch (e) {
      debugPrint('Error opening image in new tab: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return widget.placeholder ??
          Container(
            width: widget.width,
            height: widget.height,
            color: Colors.grey.shade200,
            child: const Center(
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          );
    }

    if (_hasError || _imageData == null) {
      debugPrint('Error displaying image: $_errorMessage');

      // For web platform, show clickable placeholder to open image in new tab
      if (kIsWeb) {
        return widget.errorWidget ??
            GestureDetector(
              onTap: () => _openImageInNewTab(),
              child: Container(
                width: widget.width,
                height: widget.height,
                color: Colors.grey.shade200,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.open_in_new, color: Colors.blue, size: 32),
                    const SizedBox(height: 8),
                    Text(
                      'انقر لعرض الصورة',
                      style: TextStyle(
                        color: Colors.blue.shade600,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    Text(
                      'في تبويب جديد',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 10,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            );
      }

      return widget.errorWidget ??
          Container(
            width: widget.width,
            height: widget.height,
            color: Colors.grey.shade200,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, color: Colors.red, size: 32),
                const SizedBox(height: 8),
                Text(
                  'فشل تحميل الصورة',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
                if (_errorMessage != null)
                  Padding(
                    padding: const EdgeInsets.all(4),
                    child: Text(
                      _errorMessage!,
                      style: TextStyle(
                        color: Colors.grey.shade500,
                        fontSize: 10,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
              ],
            ),
          );
    }

    return Image.memory(
      _imageData!,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      errorBuilder: (context, error, stackTrace) {
        debugPrint('Error displaying image from memory: $error');
        return widget.errorWidget ??
            Container(
              width: widget.width,
              height: widget.height,
              color: Colors.grey.shade200,
              child: const Icon(Icons.error, color: Colors.red),
            );
      },
    );
  }
}
