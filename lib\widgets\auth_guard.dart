import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../screens/auth/login_screen.dart';

class AuthGuard extends StatelessWidget {
  final Widget child;
  final bool requireAdmin;

  const AuthGuard({
    super.key,
    required this.child,
    this.requireAdmin = false,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        // Check if user is authenticated
        if (!authProvider.isAuthenticated) {
          // Return login screen directly instead of redirecting
          return const LoginScreen();
        }

        // Check if admin access is required
        if (requireAdmin && !authProvider.isAdmin) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('Access Denied / الوصول مرفوض'),
            ),
            body: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.lock,
                    size: 64,
                    color: Colors.red,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Admin access required\nمطلوب صلاحية المدير',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 8),
                  Text(
                    'You need administrator privileges to access this page.\nتحتاج إلى صلاحيات المدير للوصول إلى هذه الصفحة.',
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        }

        // User is authenticated and has required permissions
        return child;
      },
    );
  }
}
