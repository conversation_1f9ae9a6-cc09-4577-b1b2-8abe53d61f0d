import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/locale_provider.dart';
import '../../providers/storage_config_provider.dart';
import '../../widgets/responsive_layout.dart';

class CloudStorageAdminScreen extends StatefulWidget {
  const CloudStorageAdminScreen({super.key});

  @override
  State<CloudStorageAdminScreen> createState() => _CloudStorageAdminScreenState();
}

class _CloudStorageAdminScreenState extends State<CloudStorageAdminScreen> {
  final _formKey = GlobalKey<FormState>();

  // Service Account Controllers
  final _projectIdController = TextEditingController();
  final _privateKeyIdController = TextEditingController();
  final _privateKeyController = TextEditingController();
  final _clientEmailController = TextEditingController();
  final _clientIdController = TextEditingController();
  final _parentFolderController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<StorageConfigProvider>(context, listen: false);
      provider.refresh();
      _loadExistingConfig();
    });
  }

  void _loadExistingConfig() {
    final provider = Provider.of<StorageConfigProvider>(context, listen: false);
    final googleConfig = provider.googleDriveConfig;

    if (googleConfig != null) {
      _projectIdController.text = googleConfig.projectId ?? '';
      _privateKeyIdController.text = googleConfig.privateKeyId ?? '';
      _privateKeyController.text = '••••••••'; // Hide private key
      _clientEmailController.text = googleConfig.clientEmail ?? '';
      _clientIdController.text = googleConfig.clientId;
      _parentFolderController.text = googleConfig.parentFolderId ?? '';
    }
  }

  @override
  void dispose() {
    _projectIdController.dispose();
    _privateKeyIdController.dispose();
    _privateKeyController.dispose();
    _clientEmailController.dispose();
    _clientIdController.dispose();
    _parentFolderController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isRTL ? 'إعدادات التخزين السحابي' : 'Cloud Storage Settings',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: true,
      ),
      body: Consumer<StorageConfigProvider>(
        builder: (context, provider, child) {
          return ResponsivePadding(
            mobilePadding: const EdgeInsets.all(16.0),
            tabletPadding: const EdgeInsets.all(24.0),
            desktopPadding: const EdgeInsets.all(32.0),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Card(
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        children: [
                          Icon(
                            Icons.cloud,
                            size: 48,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            isRTL ? 'إعدادات التخزين السحابي' : 'Cloud Storage Configuration',
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            isRTL
                              ? 'يتم تشفير جميع البيانات المدخلة قبل الحفظ. تأكد من صحة البيانات المدخلة قبل الحفظ.'
                              : 'All entered data is encrypted before saving. Make sure the entered data is correct before saving.',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Google Drive Service Account Form
                  Card(
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.blue.withAlpha(25),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Icon(Icons.cloud, color: Colors.blue),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Google Drive Service Account',
                                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      isRTL
                                        ? 'إعدادات حساب الخدمة لـ Google Drive'
                                        : 'Service Account settings for Google Drive',
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              if (provider.isGoogleDriveConfigured)
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: Colors.green.withAlpha(25),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Text(
                                    isRTL ? 'مُكوّن' : 'Configured',
                                    style: const TextStyle(
                                      color: Colors.green,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                            ],
                          ),

                          const SizedBox(height: 24),

                          Form(
                            key: _formKey,
                            child: Column(
                              children: [
                                _buildTextField(
                                  controller: _projectIdController,
                                  labelText: isRTL ? 'معرف المشروع' : 'Project ID',
                                  hintText: isRTL ? 'أدخل معرف المشروع' : 'Enter Project ID',
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return isRTL ? 'معرف المشروع مطلوب' : 'Project ID is required';
                                    }
                                    return null;
                                  },
                                ),

                                const SizedBox(height: 16),

                                _buildTextField(
                                  controller: _privateKeyIdController,
                                  labelText: isRTL ? 'معرف المفتاح الخاص' : 'Private Key ID',
                                  hintText: isRTL ? 'أدخل معرف المفتاح الخاص' : 'Enter Private Key ID',
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return isRTL ? 'معرف المفتاح الخاص مطلوب' : 'Private Key ID is required';
                                    }
                                    return null;
                                  },
                                ),

                                const SizedBox(height: 16),

                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    _buildTextField(
                                      controller: _privateKeyController,
                                      labelText: isRTL ? 'المفتاح الخاص' : 'Private Key',
                                      hintText: isRTL
                                        ? '-----BEGIN PRIVATE KEY-----\nMIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC...\n-----END PRIVATE KEY-----'
                                        : '-----BEGIN PRIVATE KEY-----\nMIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC...\n-----END PRIVATE KEY-----',
                                      obscureText: false,
                                      maxLines: 8,
                                      validator: (value) {
                                        if (value == null || value.isEmpty || value == '••••••••') {
                                          return isRTL ? 'المفتاح الخاص مطلوب' : 'Private Key is required';
                                        }
                                        // Validate PEM format
                                        final trimmedValue = value.trim();
                                        if (!trimmedValue.contains('BEGIN PRIVATE KEY') &&
                                            !trimmedValue.contains('BEGIN RSA PRIVATE KEY')) {
                                          return isRTL
                                            ? 'يجب أن يحتوي المفتاح على تنسيق PEM الصحيح'
                                            : 'Private key must be in valid PEM format';
                                        }
                                        return null;
                                      },
                                    ),
                                    const SizedBox(height: 8),
                                    Container(
                                      padding: const EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: Colors.blue.shade50,
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(color: Colors.blue.shade200),
                                      ),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              Icon(Icons.info_outline,
                                                size: 16,
                                                color: Colors.blue.shade700),
                                              const SizedBox(width: 8),
                                              Text(
                                                isRTL ? 'تنسيق المفتاح الخاص:' : 'Private Key Format:',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.blue.shade700,
                                                  fontSize: 12,
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            isRTL
                                              ? '• يجب أن يبدأ بـ -----BEGIN PRIVATE KEY-----\n• وينتهي بـ -----END PRIVATE KEY-----\n• انسخ المفتاح كاملاً من ملف JSON'
                                              : '• Must start with -----BEGIN PRIVATE KEY-----\n• Must end with -----END PRIVATE KEY-----\n• Copy the complete key from JSON file',
                                            style: TextStyle(
                                              fontSize: 11,
                                              color: Colors.blue.shade600,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),

                                const SizedBox(height: 16),

                                _buildTextField(
                                  controller: _clientEmailController,
                                  labelText: isRTL ? 'بريد العميل' : 'Client Email',
                                  hintText: isRTL ? 'أدخل بريد العميل' : 'Enter Client Email',
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return isRTL ? 'بريد العميل مطلوب' : 'Client Email is required';
                                    }
                                    if (!value.contains('@')) {
                                      return isRTL ? 'بريد إلكتروني غير صحيح' : 'Invalid email format';
                                    }
                                    return null;
                                  },
                                ),

                                const SizedBox(height: 16),

                                _buildTextField(
                                  controller: _clientIdController,
                                  labelText: isRTL ? 'معرف العميل' : 'Client ID',
                                  hintText: isRTL ? 'أدخل معرف العميل' : 'Enter Client ID',
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return isRTL ? 'معرف العميل مطلوب' : 'Client ID is required';
                                    }
                                    return null;
                                  },
                                ),

                                const SizedBox(height: 16),

                                _buildTextField(
                                  controller: _parentFolderController,
                                  labelText: isRTL ? 'معرف المجلد الرئيسي' : 'Parent Folder ID',
                                  hintText: isRTL ? 'أدخل معرف المجلد الرئيسي (اختياري)' : 'Enter Parent Folder ID (optional)',
                                ),

                                const SizedBox(height: 32),

                                // Action Buttons
                                Row(
                                  children: [
                                    Expanded(
                                      child: ElevatedButton.icon(
                                        onPressed: provider.isLoading ? null : _saveConfiguration,
                                        icon: provider.isLoading
                                          ? const SizedBox(
                                              width: 16,
                                              height: 16,
                                              child: CircularProgressIndicator(strokeWidth: 2),
                                            )
                                          : const Icon(Icons.save),
                                        label: Text(isRTL ? 'حفظ الإعدادات' : 'Save Configuration'),
                                        style: ElevatedButton.styleFrom(
                                          padding: const EdgeInsets.symmetric(vertical: 16),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(12),
                                          ),
                                        ),
                                      ),
                                    ),

                                    if (provider.isGoogleDriveConfigured) ...[
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: OutlinedButton.icon(
                                          onPressed: provider.isLoading ? null : _deleteConfiguration,
                                          icon: const Icon(Icons.delete, color: Colors.red),
                                          label: Text(
                                            isRTL ? 'حذف الإعدادات' : 'Delete Configuration',
                                            style: const TextStyle(color: Colors.red),
                                          ),
                                          style: OutlinedButton.styleFrom(
                                            padding: const EdgeInsets.symmetric(vertical: 16),
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(12),
                                            ),
                                            side: const BorderSide(color: Colors.red),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String labelText,
    required String hintText,
    bool obscureText = false,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    // Ensure obscureText is false when maxLines > 1
    final effectiveObscureText = maxLines > 1 ? false : obscureText;

    return TextFormField(
      controller: controller,
      obscureText: effectiveObscureText,
      maxLines: maxLines,
      validator: validator,
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
      ),
    );
  }

  Future<void> _saveConfiguration() async {
    if (!_formKey.currentState!.validate()) return;

    final provider = Provider.of<StorageConfigProvider>(context, listen: false);
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Text(isRTL ? 'جاري الحفظ...' : 'Saving...'),
          ],
        ),
      ),
    );

    try {
      final success = await provider.createConfig(
        configName: 'google_drive',
        clientId: _clientIdController.text.trim(),
        clientSecret: '', // Not used for Service Account
        projectId: _projectIdController.text.trim(),
        privateKeyId: _privateKeyIdController.text.trim(),
        privateKey: _privateKeyController.text.trim(),
        clientEmail: _clientEmailController.text.trim(),
        authUri: 'https://accounts.google.com/o/oauth2/auth',
        tokenUri: 'https://oauth2.googleapis.com/token',
        authProviderCertUrl: 'https://www.googleapis.com/oauth2/v1/certs',
        clientCertUrl: 'https://www.googleapis.com/robot/v1/metadata/x509/${Uri.encodeComponent(_clientEmailController.text.trim())}',
        universeDomain: 'googleapis.com',
        parentFolderId: _parentFolderController.text.trim().isEmpty
            ? null
            : _parentFolderController.text.trim(),
        isDefault: !provider.hasAnyStorageConfigured,
      );

      // Close loading dialog
      if (mounted) Navigator.pop(context);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL ? 'تم حفظ إعدادات Google Drive بنجاح' : 'Google Drive settings saved successfully',
            ),
            backgroundColor: Colors.green,
          ),
        );
        _loadExistingConfig(); // Reload to show updated data
      } else if (mounted) {
        // Show detailed error message
        final errorMessage = provider.error ?? (isRTL ? 'فشل في حفظ الإعدادات' : 'Failed to save settings');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } catch (e) {
      // Close loading dialog
      if (mounted) Navigator.pop(context);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL ? 'خطأ: ${e.toString()}' : 'Error: ${e.toString()}',
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  Future<void> _deleteConfiguration() async {
    final provider = Provider.of<StorageConfigProvider>(context, listen: false);
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'تأكيد الحذف' : 'Confirm Deletion'),
        content: Text(
          isRTL
            ? 'هل أنت متأكد من حذف إعدادات Google Drive؟ لن يمكن التراجع عن هذا الإجراء.'
            : 'Are you sure you want to delete Google Drive settings? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(isRTL ? 'حذف' : 'Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await provider.deleteConfig('google_drive');

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL ? 'تم حذف إعدادات Google Drive بنجاح' : 'Google Drive settings deleted successfully',
            ),
            backgroundColor: Colors.green,
          ),
        );

        // Clear form
        _projectIdController.clear();
        _privateKeyIdController.clear();
        _privateKeyController.clear();
        _clientEmailController.clear();
        _clientIdController.clear();
        _parentFolderController.clear();
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL ? 'فشل في حذف الإعدادات' : 'Failed to delete settings',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}