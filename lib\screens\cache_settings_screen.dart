import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/attachment_provider.dart';
import '../providers/user_settings_provider.dart';
import '../providers/locale_provider.dart';

/// شاشة إعدادات الـ cache
class CacheSettingsScreen extends StatefulWidget {
  const CacheSettingsScreen({super.key});

  @override
  State<CacheSettingsScreen> createState() => _CacheSettingsScreenState();
}

class _CacheSettingsScreenState extends State<CacheSettingsScreen> {
  bool _isLoading = false;
  Map<String, dynamic>? _cacheStats;

  @override
  void initState() {
    super.initState();
    _loadCacheStats();
  }

  Future<void> _loadCacheStats() async {
    setState(() => _isLoading = true);

    try {
      final provider = Provider.of<AttachmentProvider>(context, listen: false);
      final stats = await provider.getCacheStatistics();

      setState(() {
        _cacheStats = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
        final isRTL = localeProvider.isRTL;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isRTL ? 'خطأ في تحميل إحصائيات الـ cache: $e' : 'Error loading cache statistics: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final settingsProvider = Provider.of<UserSettingsProvider>(context);
    final isRTL = localeProvider.isRTL;

    return Scaffold(
      appBar: AppBar(
        title: Text(isRTL ? 'إعدادات التخزين المؤقت' : 'Cache Settings'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildCacheInfoCard(isRTL),
                  const SizedBox(height: 16),
                  _buildCacheSettingsCard(settingsProvider, isRTL),
                  const SizedBox(height: 16),
                  _buildCacheActionsCard(isRTL),
                ],
              ),
            ),
    );
  }

  Widget _buildCacheInfoCard(bool isRTL) {
    if (_cacheStats == null) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Text(isRTL ? 'لا توجد بيانات متاحة' : 'No data available'),
        ),
      );
    }

    final usagePercentage = _cacheStats!['usagePercentage'] as double;
    final fileCount = _cacheStats!['fileCount'] as int;
    final formattedTotalSize = _cacheStats!['formattedTotalSize'] as String;
    final formattedMaxSize = _cacheStats!['formattedMaxSize'] as String;
    final formattedAvailableSpace = _cacheStats!['formattedAvailableSpace'] as String;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.storage,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'معلومات التخزين المؤقت' : 'Cache Information',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // شريط التقدم
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(isRTL ? 'المساحة المستخدمة: $formattedTotalSize' : 'Used Space: $formattedTotalSize'),
                    Text('${usagePercentage.toStringAsFixed(1)}%'),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: usagePercentage / 100,
                  backgroundColor: Colors.grey.shade300,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    usagePercentage > 80
                        ? Colors.red
                        : usagePercentage > 60
                            ? Colors.orange
                            : Colors.green,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // الإحصائيات
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    isRTL ? 'عدد الملفات' : 'File Count',
                    fileCount.toString(),
                    Icons.file_copy,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    isRTL ? 'الحد الأقصى' : 'Max Size',
                    formattedMaxSize,
                    Icons.storage,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    isRTL ? 'المساحة المتاحة' : 'Available Space',
                    formattedAvailableSpace,
                    Icons.free_breakfast,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Theme.of(context).colorScheme.primary),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildCacheActionsCard(bool isRTL) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.cleaning_services,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'إجراءات التخزين المؤقت' : 'Cache Actions',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // أزرار الإجراءات
            Column(
              children: [
                _buildActionButton(
                  isRTL ? 'تنظيف الملفات القديمة' : 'Clean Old Files',
                  isRTL ? 'إزالة الملفات التي لم يتم الوصول إليها لأكثر من 30 يوم' : 'Remove files not accessed for more than 30 days',
                  Icons.auto_delete,
                  Colors.orange,
                  _cleanupOldCache,
                ),
                const SizedBox(height: 12),
                _buildActionButton(
                  isRTL ? 'مسح جميع الملفات المؤقتة' : 'Clear All Cache',
                  isRTL ? 'حذف جميع الملفات المحفوظة مؤقتاً' : 'Delete all temporarily saved files',
                  Icons.delete_sweep,
                  Colors.red,
                  _clearAllCache,
                ),
                const SizedBox(height: 12),
                _buildActionButton(
                  isRTL ? 'تحديث الإحصائيات' : 'Refresh Statistics',
                  isRTL ? 'إعادة تحميل معلومات التخزين المؤقت' : 'Reload cache information',
                  Icons.refresh,
                  Colors.blue,
                  _loadCacheStats,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(color: color.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        leading: Icon(icon, color: color),
        title: Text(title),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        trailing: Icon(Icons.arrow_forward_ios, size: 16, color: color),
        onTap: onPressed,
      ),
    );
  }

  Widget _buildCacheSettingsCard(UserSettingsProvider settingsProvider, bool isRTL) {
    final settings = settingsProvider.settings;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.settings,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'إعدادات التخزين المؤقت' : 'Cache Settings',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Max cache size slider
            _buildSliderSetting(
              isRTL ? 'الحد الأقصى للتخزين' : 'Max Cache Size',
              '${settings?.maxCacheSizeMB ?? 500} MB',
              (settings?.maxCacheSizeMB ?? 500).toDouble(),
              100,
              2000,
              (value) async {
                await settingsProvider.updateMaxCacheSize(value.round());
              },
              isRTL,
            ),

            const Divider(),

            // Cache retention days slider
            _buildSliderSetting(
              isRTL ? 'مدة الاحتفاظ بالملفات' : 'File Retention Period',
              isRTL ? '${settings?.cacheRetentionDays ?? 30} يوم' : '${settings?.cacheRetentionDays ?? 30} days',
              (settings?.cacheRetentionDays ?? 30).toDouble(),
              7,
              90,
              (value) async {
                await settingsProvider.updateCacheRetentionDays(value.round());
              },
              isRTL,
            ),

            const Divider(),

            // File type settings
            Text(
              isRTL ? 'أنواع الملفات المحفوظة' : 'Cached File Types',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            SwitchListTile(
              title: Text(isRTL ? 'الصور' : 'Images'),
              value: settings?.cacheImages ?? true,
              onChanged: (value) async {
                await settingsProvider.updateCacheFileTypes(images: value);
              },
              dense: true,
            ),

            SwitchListTile(
              title: Text(isRTL ? 'الفيديو' : 'Videos'),
              value: settings?.cacheVideos ?? true,
              onChanged: (value) async {
                await settingsProvider.updateCacheFileTypes(videos: value);
              },
              dense: true,
            ),

            SwitchListTile(
              title: Text(isRTL ? 'المستندات' : 'Documents'),
              value: settings?.cacheDocuments ?? true,
              onChanged: (value) async {
                await settingsProvider.updateCacheFileTypes(documents: value);
              },
              dense: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSliderSetting(
    String title,
    String currentValue,
    double value,
    double min,
    double max,
    Function(double) onChanged,
    bool isRTL,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(
              currentValue,
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: ((max - min) / (max > 100 ? 50 : 1)).round(),
          onChanged: onChanged,
        ),
      ],
    );
  }

  Future<void> _cleanupOldCache() async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    final confirmed = await _showConfirmationDialog(
      isRTL ? 'تنظيف الملفات القديمة' : 'Clean Old Files',
      isRTL ? 'هل تريد حذف الملفات التي لم يتم الوصول إليها لأكثر من 30 يوم؟' : 'Do you want to delete files that haven\'t been accessed for more than 30 days?',
      isRTL,
    );

    if (confirmed && mounted) {
      final provider = Provider.of<AttachmentProvider>(context, listen: false);
      setState(() => _isLoading = true);

      try {
        await provider.cleanupOldCache();
        if (mounted) {
          await _loadCacheStats();
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(isRTL ? 'تم تنظيف الملفات القديمة بنجاح' : 'Old files cleaned successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        setState(() => _isLoading = false);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(isRTL ? 'خطأ في تنظيف الملفات: $e' : 'Error cleaning files: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _clearAllCache() async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    final confirmed = await _showConfirmationDialog(
      isRTL ? 'مسح جميع الملفات المؤقتة' : 'Clear All Cache',
      isRTL ? 'هل تريد حذف جميع الملفات المحفوظة مؤقتاً؟ سيتم إعادة تحميلها عند الحاجة.' : 'Do you want to delete all temporarily saved files? They will be reloaded when needed.',
      isRTL,
    );

    if (confirmed && mounted) {
      final provider = Provider.of<AttachmentProvider>(context, listen: false);
      setState(() => _isLoading = true);

      try {
        await provider.clearAllCache();
        if (mounted) {
          await _loadCacheStats();
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(isRTL ? 'تم مسح جميع الملفات المؤقتة بنجاح' : 'All cache files cleared successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        setState(() => _isLoading = false);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(isRTL ? 'خطأ في مسح الملفات: $e' : 'Error clearing files: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<bool> _showConfirmationDialog(String title, String content, bool isRTL) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(isRTL ? 'تأكيد' : 'Confirm'),
          ),
        ],
      ),
    );

    return result ?? false;
  }
}
