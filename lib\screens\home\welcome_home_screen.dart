import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/device_error_model.dart';
import '../../providers/auth_provider.dart';
import '../../providers/error_provider.dart';
import '../../providers/locale_provider.dart';

import '../../widgets/enhanced_error_card.dart';
import '../../widgets/gradient_background.dart';
import '../../widgets/modern_gradient_background.dart';
import '../../utils/theme.dart';


class WelcomeHomeScreen extends StatefulWidget {
  const WelcomeHomeScreen({super.key});

  @override
  State<WelcomeHomeScreen> createState() => _WelcomeHomeScreenState();
}

class _WelcomeHomeScreenState extends State<WelcomeHomeScreen> {
  List<DeviceError> _popularErrors = [];
  List<DeviceError> _recentErrors = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
      await errorProvider.loadErrors();

      // Get popular errors (most viewed/searched)
      final allErrors = errorProvider.errors;
      _popularErrors = allErrors.take(5).toList();

      // Get recent errors
      final sortedByDate = List<DeviceError>.from(allErrors);
      sortedByDate.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      _recentErrors = sortedByDate.take(3).toList();

    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;
    final user = authProvider.user;

    return GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: SafeArea(
          child: RefreshIndicator(
            onRefresh: _loadData,
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Welcome Header
                        _buildWelcomeHeader(user?.displayName ?? 'المستخدم', isRTL),

                        // Quick Stats
                        _buildQuickStats(isRTL),

                        // Popular Errors Section
                        _buildPopularErrorsSection(isRTL),

                        // Recent Errors Section
                        _buildRecentErrorsSection(isRTL),

                        // Random Errors Section
                        _buildRandomErrorsSection(isRTL),

                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeHeader(String userName, bool isRTL) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final authProvider = Provider.of<AuthProvider>(context);
    final user = authProvider.user;

    return ModernCard(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      backgroundColor: isDarkMode
          ? const Color(0xFF1E2A3A)
          : const Color(0xFFF8FAFF), // خلفية زرقاء فاتحة جداً
      borderRadius: BorderRadius.circular(20),
      boxShadow: [
        BoxShadow(
          color: isDarkMode
              ? Colors.black.withValues(alpha: 0.4)
              : const Color(0xFF2563EB).withValues(alpha: 0.08), // ظل أزرق فاتح
          blurRadius: isDarkMode ? 20 : 15,
          offset: const Offset(0, 5),
          spreadRadius: isDarkMode ? 2 : 1,
        ),
      ],
      border: Border.all(
        color: isDarkMode
            ? AppTheme.primaryBlue.withValues(alpha: 0.3)
            : const Color(0xFF2563EB).withValues(alpha: 0.15), // حدود زرقاء فاتحة
        width: 1,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // أيقونة النجوم على اليسار
              ModernIconContainer(
                icon: Icons.auto_awesome,
                iconColor: AppTheme.primaryBlue,
                backgroundColor: AppTheme.primaryBlue,
                size: 50,
                iconSize: 24,
                borderRadius: BorderRadius.circular(12),
              ),
              const SizedBox(width: 16),
              // النص في المنتصف
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isRTL ? 'مرحباً بك' : 'Welcome back',
                      style: TextStyle(
                        color: isDarkMode
                            ? Colors.white.withValues(alpha: 0.8)
                            : AppTheme.textSecondary,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      userName,
                      style: TextStyle(
                        color: isDarkMode
                            ? Colors.white
                            : AppTheme.textPrimary,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              // صورة المستخدم على اليمين
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppTheme.primaryBlue.withValues(alpha: 0.3),
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryBlue.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: user?.profileImageUrl != null
                    ? ClipOval(
                        child: Image.network(
                          user!.profileImageUrl!,
                          fit: BoxFit.cover,
                          width: 60,
                          height: 60,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    AppTheme.primaryBlue.withValues(alpha: 0.2),
                                    AppTheme.primaryBlue.withValues(alpha: 0.1),
                                  ],
                                ),
                              ),
                              child: Icon(
                                Icons.person,
                                color: AppTheme.primaryBlue,
                                size: 30,
                              ),
                            );
                          },
                        ),
                      )
                    : Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              AppTheme.primaryBlue.withValues(alpha: 0.2),
                              AppTheme.primaryBlue.withValues(alpha: 0.1),
                            ],
                          ),
                        ),
                        child: Icon(
                          Icons.person,
                          color: AppTheme.primaryBlue,
                          size: 30,
                        ),
                      ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          // قسم البحث
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? AppTheme.primaryBlue.withValues(alpha: 0.1)
                  : const Color(0xFFEBF2FF), // خلفية زرقاء فاتحة أكثر
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isDarkMode
                    ? AppTheme.primaryBlue.withValues(alpha: 0.2)
                    : const Color(0xFF2563EB).withValues(alpha: 0.2), // حدود زرقاء فاتحة
                width: 1,
              ),
            ),
            child: Row(
              children: [
                ModernIconContainer(
                  icon: Icons.location_on_outlined,
                  iconColor: AppTheme.primaryBlue,
                  backgroundColor: AppTheme.primaryBlue,
                  size: 40,
                  iconSize: 20,
                  borderRadius: BorderRadius.circular(10),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    isRTL
                        ? 'ابحث عن حلول للأعطال التقنية'
                        : 'Find solutions for technical issues',
                    style: TextStyle(
                      color: isDarkMode
                          ? Colors.white.withValues(alpha: 0.9)
                          : AppTheme.textPrimary,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats(bool isRTL) {
    return Consumer<ErrorProvider>(
      builder: (context, errorProvider, child) {
        return Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  icon: Icons.bug_report_outlined,
                  title: isRTL ? 'إجمالي الأعطال' : 'Total Errors',
                  value: '${errorProvider.errors.length}',
                  color: AppTheme.primaryBlue,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.favorite_outline,
                  title: isRTL ? 'المفضلة' : 'Favorites',
                  value: '${errorProvider.favoriteErrors.length}',
                  color: AppTheme.errorRose,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.trending_up_outlined,
                  title: isRTL ? 'الشائعة' : 'Popular',
                  value: '${_popularErrors.length}',
                  color: AppTheme.successGreen,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return ModernCard(
      margin: const EdgeInsets.all(4),
      padding: const EdgeInsets.all(16),
      backgroundColor: isDarkMode
          ? const Color(0xFF1E2A3A)
          : const Color(0xFFF0F8FF), // أزرق فاتح جداً
      borderRadius: BorderRadius.circular(20),
      boxShadow: [
        BoxShadow(
          color: isDarkMode
              ? Colors.black.withValues(alpha: 0.4)
              : const Color(0xFF2563EB).withValues(alpha: 0.08), // ظل أزرق فاتح
          blurRadius: isDarkMode ? 20 : 15,
          offset: const Offset(0, 6),
          spreadRadius: isDarkMode ? 2 : 1,
        ),
        if (isDarkMode)
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 3),
            spreadRadius: 1,
          ),
      ],
      border: Border.all(
        color: isDarkMode
            ? color.withValues(alpha: 0.3)
            : const Color(0xFF2563EB).withValues(alpha: 0.2), // حدود زرقاء فاتحة
        width: isDarkMode ? 1.5 : 1,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ModernIconContainer(
            icon: icon,
            iconColor: color,
            backgroundColor: color,
            size: 48, // توحيد حجم الأيقونات
            iconSize: 24, // توحيد حجم الأيقونات الداخلية
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
              letterSpacing: 0.5,
            ),
          ),
          const SizedBox(height: 8),
          Flexible(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: isDarkMode
                    ? Colors.white.withValues(alpha: 0.8)
                    : const Color(0xFF1E40AF), // لون أزرق داكن للنص
                letterSpacing: 0.3,
                height: 1.2,
              ),
              textAlign: TextAlign.center,
              maxLines: 2, // السماح بسطرين للنص
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPopularErrorsSection(bool isRTL) {
    if (_popularErrors.isEmpty) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                isRTL ? 'الأعطال الشائعة' : 'Popular Errors',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : AppTheme.textPrimary,
                ),
              ),
              TextButton(
                onPressed: () {
                  // Navigate to errors page
                  Navigator.pushNamed(context, '/errors');
                },
                child: Text(
                  isRTL ? 'عرض الكل' : 'View All',
                  style: TextStyle(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.white70
                        : AppTheme.textSecondary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          LayoutBuilder(
            builder: (context, constraints) {
              // Determine if we should use horizontal scroll or grid
              final isWideScreen = constraints.maxWidth > 600;

              if (isWideScreen) {
                // Use grid layout for wide screens
                return GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: constraints.maxWidth > 1200 ? 3 : 2,
                    childAspectRatio: 2.5,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                  ),
                  itemCount: _popularErrors.length,
                  itemBuilder: (context, index) {
                    final error = _popularErrors[index];
                    return EnhancedErrorCard(
                      error: error,
                      isCompact: true,
                    );
                  },
                );
              } else {
                // Use horizontal scroll for narrow screens
                return SizedBox(
                  height: 200,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _popularErrors.length,
                    itemBuilder: (context, index) {
                      final error = _popularErrors[index];
                      return Container(
                        width: constraints.maxWidth * 0.85,
                        margin: EdgeInsets.only(
                          right: isRTL ? 0 : 12,
                          left: isRTL ? 12 : 0,
                        ),
                        child: EnhancedErrorCard(
                          error: error,
                          isCompact: true,
                        ),
                      );
                    },
                  ),
                );
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildRecentErrorsSection(bool isRTL) {
    if (_recentErrors.isEmpty) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                isRTL ? 'الأعطال الحديثة' : 'Recent Errors',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : AppTheme.textPrimary,
                ),
              ),
              TextButton(
                onPressed: () {
                  // Navigate to errors page
                  Navigator.pushNamed(context, '/errors');
                },
                child: Text(
                  isRTL ? 'عرض الكل' : 'View All',
                  style: TextStyle(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.white70
                        : AppTheme.textSecondary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          LayoutBuilder(
            builder: (context, constraints) {
              // Determine if we should use horizontal scroll or grid
              final isWideScreen = constraints.maxWidth > 600;

              if (isWideScreen) {
                // Use grid layout for wide screens
                return GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: constraints.maxWidth > 1200 ? 3 : 2,
                    childAspectRatio: 2.5,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                  ),
                  itemCount: _recentErrors.length,
                  itemBuilder: (context, index) {
                    final error = _recentErrors[index];
                    return EnhancedErrorCard(
                      error: error,
                      isCompact: true,
                    );
                  },
                );
              } else {
                // Use horizontal scroll for narrow screens
                return SizedBox(
                  height: 200,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _recentErrors.length,
                    itemBuilder: (context, index) {
                      final error = _recentErrors[index];
                      return Container(
                        width: constraints.maxWidth * 0.85,
                        margin: EdgeInsets.only(
                          right: isRTL ? 0 : 12,
                          left: isRTL ? 12 : 0,
                        ),
                        child: EnhancedErrorCard(
                          error: error,
                          isCompact: true,
                        ),
                      );
                    },
                  ),
                );
              }
            },
          ),
        ],
      ),
    );
  }


  Widget _buildRandomErrorsSection(bool isRTL) {
    final errorProvider = Provider.of<ErrorProvider>(context);
    final allErrors = errorProvider.errors;

    if (allErrors.isEmpty) return const SizedBox.shrink();

    // Get random errors (shuffle and take first 5)
    final randomErrors = List.from(allErrors)..shuffle();
    final displayErrors = randomErrors.take(5).toList();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                isRTL ? 'أعطال متنوعة' : 'Random Errors',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : AppTheme.textPrimary,
                ),
              ),
              IconButton(
                onPressed: () {
                  setState(() {
                    // Refresh random errors
                  });
                },
                icon: Icon(
                  Icons.refresh,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : AppTheme.textPrimary,
                ),
                tooltip: isRTL ? 'تحديث' : 'Refresh',
              ),
            ],
          ),
          const SizedBox(height: 12),
          LayoutBuilder(
            builder: (context, constraints) {
              // Determine if we should use horizontal scroll or grid
              final isWideScreen = constraints.maxWidth > 600;

              if (isWideScreen) {
                // Use grid layout for wide screens
                return GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: constraints.maxWidth > 1200 ? 3 : 2,
                    childAspectRatio: 2.5,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                  ),
                  itemCount: displayErrors.length,
                  itemBuilder: (context, index) {
                    final error = displayErrors[index];
                    return EnhancedErrorCard(
                      error: error,
                      isCompact: true,
                    );
                  },
                );
              } else {
                // Use horizontal scroll for narrow screens
                return SizedBox(
                  height: 200,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: displayErrors.length,
                    itemBuilder: (context, index) {
                      final error = displayErrors[index];
                      return Container(
                        width: constraints.maxWidth * 0.85,
                        margin: EdgeInsets.only(
                          right: isRTL ? 0 : 12,
                          left: isRTL ? 12 : 0,
                        ),
                        child: EnhancedErrorCard(
                          error: error,
                          isCompact: true,
                        ),
                      );
                    },
                  ),
                );
              }
            },
          ),
        ],
      ),
    );
  }

}