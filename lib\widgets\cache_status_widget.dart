import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/attachment_provider.dart';
import '../screens/cache_settings_screen.dart';

/// Widget لعرض حالة الـ cache والتحميل
class CacheStatusWidget extends StatelessWidget {
  final bool showDetails;
  final bool showSettingsButton;

  const CacheStatusWidget({
    super.key,
    this.showDetails = true,
    this.showSettingsButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AttachmentProvider>(
      builder: (context, provider, child) {
        if (!provider.isInitialized) {
          return const SizedBox.shrink();
        }

        return Card(
          margin: const EdgeInsets.all(8),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(context, provider),
                if (showDetails) ...[
                  const SizedBox(height: 12),
                  _buildCacheInfo(provider),
                ],
                if (provider.downloadingCount > 0) ...[
                  const SizedBox(height: 12),
                  _buildDownloadingInfo(provider),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, AttachmentProvider provider) {
    return Row(
      children: [
        Icon(
          Icons.storage,
          color: Theme.of(context).colorScheme.primary,
          size: 20,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            'التخزين المؤقت',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        if (showSettingsButton)
          IconButton(
            icon: const Icon(Icons.settings, size: 20),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const CacheSettingsScreen(),
                ),
              );
            },
            tooltip: 'إعدادات التخزين المؤقت',
          ),
      ],
    );
  }

  Widget _buildCacheInfo(AttachmentProvider provider) {
    final cacheInfo = provider.cacheInfo;
    if (cacheInfo == null) {
      return const Text(
        'لا توجد معلومات متاحة',
        style: TextStyle(fontSize: 12),
      );
    }

    final usagePercentage = cacheInfo.usagePercentage;
    final color = usagePercentage > 80 
        ? Colors.red 
        : usagePercentage > 60 
            ? Colors.orange 
            : Colors.green;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'المستخدم: ${cacheInfo.formattedTotalSize}',
              style: const TextStyle(fontSize: 12),
            ),
            Text(
              '${usagePercentage.toStringAsFixed(1)}%',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: usagePercentage / 100,
          backgroundColor: Colors.grey.shade300,
          valueColor: AlwaysStoppedAnimation<Color>(color),
          minHeight: 4,
        ),
        const SizedBox(height: 4),
        Text(
          '${cacheInfo.fileCount} ملف • الحد الأقصى: ${cacheInfo.formattedMaxSize}',
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildDownloadingInfo(AttachmentProvider provider) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade600),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'جاري تحميل ${provider.downloadingCount} ملف...',
              style: TextStyle(
                fontSize: 12,
                color: Colors.blue.shade700,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Widget مبسط لعرض مؤشر الـ cache
class CacheIndicatorWidget extends StatelessWidget {
  final bool isCached;
  final bool isDownloading;
  final double? downloadProgress;

  const CacheIndicatorWidget({
    super.key,
    required this.isCached,
    this.isDownloading = false,
    this.downloadProgress,
  });

  @override
  Widget build(BuildContext context) {
    if (isDownloading) {
      return Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Colors.blue.withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 12,
              height: 12,
              child: CircularProgressIndicator(
                value: downloadProgress,
                strokeWidth: 2,
                valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            if (downloadProgress != null) ...[
              const SizedBox(width: 4),
              Text(
                '${(downloadProgress! * 100).toInt()}%',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 8,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ],
        ),
      );
    }

    if (isCached) {
      return Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Colors.green.withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(4),
        ),
        child: const Icon(
          Icons.offline_pin,
          size: 12,
          color: Colors.white,
        ),
      );
    }

    return const SizedBox.shrink();
  }
}

/// Widget لعرض قائمة الملفات قيد التحميل
class DownloadingFilesWidget extends StatelessWidget {
  const DownloadingFilesWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AttachmentProvider>(
      builder: (context, provider, child) {
        final downloadingAttachments = provider.downloadingAttachments;
        final downloadingFiles = downloadingAttachments.entries
            .where((entry) => entry.value)
            .map((entry) => entry.key)
            .toList();

        if (downloadingFiles.isEmpty) {
          return const SizedBox.shrink();
        }

        return Card(
          margin: const EdgeInsets.all(8),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.download,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'الملفات قيد التحميل (${downloadingFiles.length})',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                ...downloadingFiles.map((fileId) {
                  final attachment = provider.getAttachment(fileId);
                  final progress = provider.getDownloadProgress(fileId);
                  
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: _buildDownloadingFileItem(
                      context,
                      attachment?.fileName ?? 'ملف غير معروف',
                      progress,
                      () => provider.cancelDownload(fileId),
                    ),
                  );
                }),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDownloadingFileItem(
    BuildContext context,
    String fileName,
    double progress,
    VoidCallback onCancel,
  ) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              value: progress,
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  fileName,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  '${(progress * 100).toInt()}% مكتمل',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, size: 16),
            onPressed: onCancel,
            tooltip: 'إلغاء التحميل',
          ),
        ],
      ),
    );
  }
}

/// Widget لعرض إحصائيات سريعة للـ cache
class QuickCacheStatsWidget extends StatelessWidget {
  const QuickCacheStatsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AttachmentProvider>(
      builder: (context, provider, child) {
        if (!provider.isInitialized || provider.cacheInfo == null) {
          return const SizedBox.shrink();
        }

        final cacheInfo = provider.cacheInfo!;
        
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.storage,
                size: 14,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
              const SizedBox(width: 4),
              Text(
                '${cacheInfo.fileCount} ملف • ${cacheInfo.formattedTotalSize}',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
