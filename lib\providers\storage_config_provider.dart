import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/storage_config_model.dart';
import '../services/storage_config_service.dart';
import '../services/auth_service.dart';

class StorageConfigProvider extends ChangeNotifier {
  final StorageConfigService _configService = StorageConfigService();
  final AuthService _authService = AuthService();

  // State variables
  List<StorageConfigModel> _configs = [];
  AppStorageSettings _appSettings = const AppStorageSettings();
  bool _isLoading = false;
  String? _error;
  bool _isInitialized = false;

  // Getters
  List<StorageConfigModel> get configs => _configs;
  AppStorageSettings get appSettings => _appSettings;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isInitialized => _isInitialized;

  // Get specific configurations
  StorageConfigModel? get googleDriveConfig =>
      _configs.where((c) => c.configName == 'google_drive').firstOrNull;

  StorageConfigModel? get oneDriveConfig =>
      _configs.where((c) => c.configName == 'onedrive').firstOrNull;

  StorageConfigModel? get defaultConfig =>
      _configs.where((c) => c.isDefault).firstOrNull ?? _configs.firstOrNull;

  /// Initialize the provider
  Future<void> initialize() async {
    if (_isInitialized) return;

    _setLoading(true);
    _clearError();

    try {
      await _configService.initialize();
      await _loadConfigurations();
      _isInitialized = true;
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// Load all configurations
  Future<void> _loadConfigurations() async {
    try {
      _configs = _configService.allConfigs;
      _appSettings = _configService.appSettings;
      notifyListeners();
    } catch (e) {
      _setError('Failed to load configurations: $e');
    }
  }

  /// Create new storage configuration
  Future<bool> createConfig({
    required String configName,
    required String clientId,
    required String clientSecret,
    String? redirectUri,
    String? parentFolderId,
    String? projectId,
    String? privateKeyId,
    String? privateKey,
    String? clientEmail,
    String? authUri,
    String? tokenUri,
    String? authProviderCertUrl,
    String? clientCertUrl,
    String? universeDomain,
    Map<String, dynamic>? additionalSettings,
    bool isDefault = false,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      debugPrint('Creating config for user: ${currentUser.uid}');
      debugPrint('Config name: $configName');

      // Check if user is admin
      final isAdmin = await _checkUserIsAdmin(currentUser.uid);
      if (!isAdmin) {
        throw Exception('Access denied: Admin privileges required');
      }

      // Check if configuration already exists and update instead of creating new
      final existingConfig = _configs.where((c) => c.configName == configName).firstOrNull;
      if (existingConfig != null) {
        debugPrint('Updating existing config: ${existingConfig.id}');
        // Update existing configuration instead of creating new one
        return await updateConfig(existingConfig.id, {
          'clientId': clientId,
          'clientSecret': clientSecret,
          'redirectUri': redirectUri,
          'parentFolderId': parentFolderId,
          'projectId': projectId,
          'privateKeyId': privateKeyId,
          'privateKey': privateKey,
          'clientEmail': clientEmail,
          'authUri': authUri,
          'tokenUri': tokenUri,
          'authProviderCertUrl': authProviderCertUrl,
          'clientCertUrl': clientCertUrl,
          'universeDomain': universeDomain,
          'additionalSettings': additionalSettings,
          'isDefault': isDefault,
        });
      }

      debugPrint('Creating new config...');
      final config = await _configService.createConfig(
        configName: configName,
        clientId: clientId,
        clientSecret: clientSecret,
        redirectUri: redirectUri,
        parentFolderId: parentFolderId,
        projectId: projectId,
        privateKeyId: privateKeyId,
        privateKey: privateKey,
        clientEmail: clientEmail,
        authUri: authUri,
        tokenUri: tokenUri,
        authProviderCertUrl: authProviderCertUrl,
        clientCertUrl: clientCertUrl,
        universeDomain: universeDomain,
        additionalSettings: additionalSettings,
        isDefault: isDefault,
        userId: currentUser.uid,
      );

      _configs.add(config);
      notifyListeners();
      debugPrint('Config created successfully');
      return true;
    } catch (e) {
      debugPrint('Error in createConfig: $e');
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Update existing configuration
  Future<bool> updateConfig(
    String configId,
    Map<String, dynamic> updates,
  ) async {
    _setLoading(true);
    _clearError();

    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      await _configService.updateConfig(configId, updates, currentUser.uid);
      await _loadConfigurations();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Delete configuration
  Future<bool> deleteConfig(String configId) async {
    _setLoading(true);
    _clearError();

    try {
      await _configService.deleteConfig(configId);
      _configs.removeWhere((c) => c.id == configId);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Set configuration as default
  Future<bool> setAsDefault(String configName) async {
    _setLoading(true);
    _clearError();

    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      await _configService.setAsDefault(configName, currentUser.uid);
      await _loadConfigurations();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Update app storage settings
  Future<bool> updateAppSettings(AppStorageSettings settings) async {
    _setLoading(true);
    _clearError();

    try {
      await _configService.updateAppSettings(settings);
      _appSettings = settings;
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Test configuration
  Future<bool> testConfig(StorageConfigModel config) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _configService.testConfig(config);
      return result;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh configurations
  Future<void> refresh() async {
    _setLoading(true);
    _clearError();

    try {
      await _configService.refresh();
      await _loadConfigurations();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// Get configuration by name
  StorageConfigModel? getConfig(String configName) {
    return _configs.where((c) => c.configName == configName).firstOrNull;
  }

  /// Check if configuration exists
  bool hasConfig(String configName) {
    return _configs.any((c) => c.configName == configName);
  }

  /// Get active configurations
  List<StorageConfigModel> get activeConfigs =>
      _configs.where((c) => c.isActive).toList();

  /// Check if Google Drive is configured
  bool get isGoogleDriveConfigured => googleDriveConfig != null;

  /// Check if OneDrive is configured
  bool get isOneDriveConfigured => oneDriveConfig != null;

  /// Check if any storage is configured
  bool get hasAnyStorageConfigured => _configs.isNotEmpty;

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  /// Check if user is admin
  Future<bool> _checkUserIsAdmin(String userId) async {
    try {
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .get();

      if (!userDoc.exists) {
        debugPrint('User document not found: $userId');
        return false;
      }

      final userData = userDoc.data()!;
      final role = userData['role'] as String?;
      final isActive = userData['isActive'] as bool? ?? false;

      debugPrint('User role: $role, isActive: $isActive');

      return role == 'admin' && isActive;
    } catch (e) {
      debugPrint('Error checking user admin status: $e');
      return false;
    }
  }
}
