import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/locale_provider.dart';

/// Mixin to handle unsaved changes detection and prevention of navigation
/// when there are unsaved changes or ongoing operations
mixin UnsavedChangesMixin<T extends StatefulWidget> on State<T> {
  bool _hasUnsavedChanges = false;
  bool _isOperationInProgress = false;

  /// Whether there are unsaved changes
  bool get hasUnsavedChanges => _hasUnsavedChanges;

  /// Whether an operation (like file upload) is in progress
  bool get isOperationInProgress => _isOperationInProgress;

  /// Mark that changes have been made
  void markAsChanged() {
    if (!_hasUnsavedChanges) {
      setState(() {
        _hasUnsavedChanges = true;
      });
    }
  }

  /// Mark that changes have been saved
  void markAsSaved() {
    if (_hasUnsavedChanges) {
      setState(() {
        _hasUnsavedChanges = false;
      });
    }
  }

  /// Set operation in progress state (e.g., file upload)
  void setOperationInProgress(bool inProgress) {
    if (_isOperationInProgress != inProgress) {
      setState(() {
        _isOperationInProgress = inProgress;
      });
    }
  }

  /// Handle back navigation with unsaved changes check
  Future<bool> onWillPop() async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    // If operation is in progress, prevent exit
    if (_isOperationInProgress) {
      _showOperationInProgressDialog(isRTL);
      return false;
    }

    // If no unsaved changes, allow exit
    if (!_hasUnsavedChanges) {
      return true;
    }

    // Show confirmation dialog for unsaved changes
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.orange[600]),
            const SizedBox(width: 8),
            Text(
              isRTL ? 'تغييرات غير محفوظة' : 'Unsaved Changes',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        content: Text(
          isRTL
              ? 'لديك تغييرات غير محفوظة. هل تريد المغادرة بدون حفظ؟'
              : 'You have unsaved changes. Do you want to leave without saving?',
          style: const TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              isRTL ? 'البقاء' : 'Stay',
              style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(
              isRTL ? 'المغادرة' : 'Leave',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// Show dialog when operation is in progress
  void _showOperationInProgressDialog(bool isRTL) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        title: Row(
          children: [
            SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              isRTL ? 'عملية جارية' : 'Operation in Progress',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        content: Text(
          isRTL
              ? 'يرجى الانتظار حتى انتهاء العملية الجارية قبل المغادرة.'
              : 'Please wait for the current operation to complete before leaving.',
          style: const TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              isRTL ? 'فهمت' : 'Got it',
              style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build a PopScope widget with unsaved changes handling
  Widget buildWithUnsavedChangesProtection({required Widget child}) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;
        final shouldPop = await onWillPop();
        if (shouldPop && mounted) {
          Navigator.of(context).pop();
        }
      },
      child: child,
    );
  }

  /// Build app bar actions with unsaved changes indicator
  List<Widget> buildAppBarActions({
    required bool isRTL,
    required VoidCallback? onSave,
    bool isLoading = false,
    String? saveLabel,
    List<Widget>? additionalActions,
  }) {
    return [
      // Unsaved changes indicator
      if (_hasUnsavedChanges)
        Container(
          margin: const EdgeInsets.only(right: 8),
          child: Icon(
            Icons.circle,
            color: Colors.orange[600],
            size: 12,
          ),
        ),
      
      // Additional actions
      if (additionalActions != null) ...additionalActions,
      
      // Save button
      IconButton(
        icon: isLoading
            ? SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : const Icon(Icons.save),
        tooltip: saveLabel ?? (isRTL ? 'حفظ' : 'Save'),
        onPressed: (isLoading || _isOperationInProgress) ? null : onSave,
      ),
    ];
  }

  /// Add listeners to text controllers to detect changes
  void addChangeListeners(List<TextEditingController> controllers) {
    for (final controller in controllers) {
      controller.addListener(markAsChanged);
    }
  }

  /// Remove listeners from text controllers
  void removeChangeListeners(List<TextEditingController> controllers) {
    for (final controller in controllers) {
      controller.removeListener(markAsChanged);
    }
  }
}
