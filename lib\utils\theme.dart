import 'package:flutter/material.dart';

class AppTheme {
  // Modern vibrant color palette for light theme
  static const Color primaryBlue = Color(0xFF2563EB);        // Modern blue
  static const Color primaryBlueLight = Color(0xFF3B82F6);   // Lighter blue
  static const Color secondaryPurple = Color(0xFF8B5CF6);    // Purple accent
  static const Color accentTeal = Color(0xFF06B6D4);         // Teal accent
  static const Color successGreen = Color(0xFF10B981);       // Success green
  static const Color warningAmber = Color(0xFFF59E0B);       // Warning amber
  static const Color errorRose = Color(0xFFEF4444);          // Error rose
  static const Color surfaceLight = Color(0xFFF8FAFC);       // Very light gray
  static const Color surfaceDark = Color(0xFF0F172A);        // Dark slate
  static const Color cardLight = Color(0xFFF1F3F6);          // Light gray background like in image
  static const Color cardDark = Color(0xFF1E293B);           // Dark card

  // Additional modern colors
  static const Color backgroundGradientStart = Color(0xFFF1F5F9);  // Light slate
  static const Color backgroundGradientEnd = Color(0xFFE2E8F0);    // Slate 200
  static const Color textPrimary = Color(0xFF0F172A);              // Slate 900
  static const Color textSecondary = Color(0xFF475569);            // Slate 600
  static const Color textMuted = Color(0xFF94A3B8);               // Slate 400
  static const Color borderColor = Color(0xFFE2E8F0);             // Slate 200
  static const Color dividerColor = Color(0xFFF1F5F9);            // Slate 100

  static ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    fontFamily: 'AwanZaman',
    colorScheme: ColorScheme.fromSeed(
      seedColor: primaryBlue,
      brightness: Brightness.light,
      primary: primaryBlue,
      secondary: secondaryPurple,
      tertiary: accentTeal,
      error: errorRose,
      surface: surfaceLight,
      onSurface: textPrimary,
      surfaceContainerHighest: backgroundGradientEnd,
    ),
    scaffoldBackgroundColor: Colors.white,
    appBarTheme: const AppBarTheme(
      centerTitle: true,
      elevation: 0,
      scrolledUnderElevation: 1,
      backgroundColor: Colors.white,
      foregroundColor: textPrimary,
      titleTextStyle: TextStyle(
        color: textPrimary,
        fontSize: 24,
        fontWeight: FontWeight.w600,
        fontFamily: 'AwanZaman',
      ),
      iconTheme: IconThemeData(color: textPrimary),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        elevation: 2,
        shadowColor: primaryBlue.withValues(alpha: 0.3),
        backgroundColor: primaryBlue,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        textStyle: const TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          fontFamily: 'AwanZaman',
        ),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        side: const BorderSide(color: primaryBlue, width: 2),
        foregroundColor: primaryBlue,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        textStyle: const TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          fontFamily: 'AwanZaman',
        ),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: primaryBlue,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        textStyle: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w500,
          fontFamily: 'AwanZaman',
        ),
      ),
    ),
    cardTheme: CardThemeData(
      elevation: 3,
      shadowColor: Colors.black.withValues(alpha: 0.08),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      color: cardLight,
      surfaceTintColor: primaryBlue.withValues(alpha: 0.05),
    ),
    inputDecorationTheme: InputDecorationTheme(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: const BorderSide(color: borderColor),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: const BorderSide(color: borderColor),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: const BorderSide(color: primaryBlue, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: const BorderSide(color: errorRose, width: 2),
      ),
      filled: true,
      fillColor: Colors.white,
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
      hintStyle: const TextStyle(
        color: textMuted,
        fontSize: 20,
        fontFamily: 'AwanZaman',
      ),
      labelStyle: const TextStyle(
        color: textSecondary,
        fontSize: 20,
        fontFamily: 'AwanZaman',
      ),
    ),
    dialogTheme: DialogThemeData(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24),
      ),
      elevation: 8,
      backgroundColor: Colors.white,
      titleTextStyle: const TextStyle(
        color: textPrimary,
        fontSize: 24,
        fontWeight: FontWeight.w600,
        fontFamily: 'AwanZaman',
      ),
      contentTextStyle: const TextStyle(
        color: textSecondary,
        fontSize: 18,
        fontFamily: 'AwanZaman',
      ),
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: Colors.white,
      selectedItemColor: primaryBlue,
      unselectedItemColor: textMuted,
      type: BottomNavigationBarType.fixed,
      elevation: 8,
      selectedLabelStyle: TextStyle(fontWeight: FontWeight.w600),
    ),
    drawerTheme: const DrawerThemeData(
      backgroundColor: Colors.white,
      width: 300,
    ),
    listTileTheme: const ListTileThemeData(
      textColor: textPrimary,
      iconColor: textSecondary,
      selectedTileColor: Color(0xFFEFF6FF),
      selectedColor: primaryBlue,
      contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 4),
    ),
    chipTheme: ChipThemeData(
      backgroundColor: const Color(0xFFE8F4FD),
      selectedColor: primaryBlue,
      labelStyle: const TextStyle(color: primaryBlue),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    ),
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      backgroundColor: primaryBlue,
      foregroundColor: Colors.white,
      elevation: 6,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(16)),
      ),
    ),
    textTheme: const TextTheme(
      displayLarge: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 57,
        fontWeight: FontWeight.w400,
        color: textPrimary,
      ),
      displayMedium: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 45,
        fontWeight: FontWeight.w400,
        color: textPrimary,
      ),
      displaySmall: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 36,
        fontWeight: FontWeight.w400,
        color: textPrimary,
      ),
      headlineLarge: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 32,
        fontWeight: FontWeight.w600,
        color: textPrimary,
      ),
      headlineMedium: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 28,
        fontWeight: FontWeight.w600,
        color: textPrimary,
      ),
      headlineSmall: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: textPrimary,
      ),
      titleLarge: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 26,
        fontWeight: FontWeight.w500,
        color: textPrimary,
      ),
      titleMedium: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 22,
        fontWeight: FontWeight.w500,
        color: textPrimary,
      ),
      titleSmall: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 20,
        fontWeight: FontWeight.w500,
        color: textSecondary,
      ),
      bodyLarge: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 22,
        fontWeight: FontWeight.w400,
        color: textPrimary,
      ),
      bodyMedium: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 20,
        fontWeight: FontWeight.w400,
        color: textPrimary,
      ),
      bodySmall: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 18,
        fontWeight: FontWeight.w400,
        color: textSecondary,
      ),
      labelLarge: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 20,
        fontWeight: FontWeight.w500,
        color: textPrimary,
      ),
      labelMedium: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 18,
        fontWeight: FontWeight.w500,
        color: textSecondary,
      ),
      labelSmall: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: textSecondary,
      ),
    ),
  );

  static ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    fontFamily: 'AwanZaman',
    colorScheme: ColorScheme.fromSeed(
      seedColor: primaryBlue,
      brightness: Brightness.dark,
      primary: const Color(0xFF64B5F6),
      secondary: const Color(0xFF4DD0E1),
      tertiary: const Color(0xFF81C784),
      error: const Color(0xFFEF5350),
      surface: surfaceDark,
      onSurface: const Color(0xFFE1E1E1),
      surfaceContainerHighest: const Color(0xFF1E1E1E),
      outline: const Color(0xFF404040),
    ),
    scaffoldBackgroundColor: surfaceDark,
    appBarTheme: const AppBarTheme(
      centerTitle: true,
      elevation: 0,
      scrolledUnderElevation: 1,
      backgroundColor: Color(0xFF121212),
      foregroundColor: Color(0xFFE1E1E1),
      titleTextStyle: TextStyle(
        color: Color(0xFFFFFFFF),
        fontSize: 24,
        fontWeight: FontWeight.w600,
        fontFamily: 'AwanZaman',
      ),
      iconTheme: IconThemeData(color: Color(0xFFE1E1E1)),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        elevation: 2,
        shadowColor: const Color(0xFF64B5F6).withValues(alpha: 0.3),
        backgroundColor: const Color(0xFF64B5F6),
        foregroundColor: const Color(0xFF121212),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        textStyle: const TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          fontFamily: 'AwanZaman',
        ),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        side: const BorderSide(color: Color(0xFF64B5F6), width: 2),
        foregroundColor: const Color(0xFF64B5F6),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        textStyle: const TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          fontFamily: 'AwanZaman',
        ),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: const Color(0xFF64B5F6),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        textStyle: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w500,
          fontFamily: 'AwanZaman',
        ),
      ),
    ),
    cardTheme: CardThemeData(
      elevation: 3,
      color: cardDark,
      shadowColor: Colors.black.withValues(alpha: 0.4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      surfaceTintColor: primaryBlueLight.withValues(alpha: 0.05),
    ),
    inputDecorationTheme: InputDecorationTheme(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: const BorderSide(color: Color(0xFF404040)),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: const BorderSide(color: Color(0xFF404040)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: const BorderSide(color: Color(0xFF64B5F6), width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: const BorderSide(color: Color(0xFFEF5350), width: 2),
      ),
      filled: true,
      fillColor: const Color(0xFF2A2A2A),
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
      hintStyle: const TextStyle(
        color: Color(0xFF888888),
        fontSize: 20,
        fontFamily: 'AwanZaman',
      ),
      labelStyle: const TextStyle(
        color: Color(0xFFB3B3B3),
        fontSize: 20,
        fontFamily: 'AwanZaman',
      ),
    ),
    dialogTheme: DialogThemeData(
      backgroundColor: const Color(0xFF1E1E1E),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24),
      ),
      elevation: 8,
      titleTextStyle: const TextStyle(
        color: Color(0xFFFFFFFF),
        fontSize: 24,
        fontWeight: FontWeight.w600,
        fontFamily: 'AwanZaman',
      ),
      contentTextStyle: const TextStyle(
        color: Color(0xFFE0E0E0),
        fontSize: 18,
        fontFamily: 'AwanZaman',
      ),
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: Color(0xFF1A1A1A),
      selectedItemColor: Color(0xFF64B5F6),
      unselectedItemColor: Color(0xFF888888),
      type: BottomNavigationBarType.fixed,
      elevation: 8,
      selectedLabelStyle: TextStyle(fontWeight: FontWeight.w600),
    ),
    drawerTheme: const DrawerThemeData(
      backgroundColor: Color(0xFF1A1A1A),
      width: 300,
    ),
    listTileTheme: ListTileThemeData(
      textColor: const Color(0xFFE1E1E1),
      iconColor: const Color(0xFFB3B3B3),
      selectedTileColor: const Color(0xFF1565C0).withValues(alpha: 0.2),
      selectedColor: const Color(0xFF64B5F6),
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
    ),
    chipTheme: ChipThemeData(
      backgroundColor: const Color(0xFF2A2A2A),
      selectedColor: const Color(0xFF64B5F6),
      labelStyle: const TextStyle(color: Color(0xFF64B5F6)),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    ),
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      backgroundColor: Color(0xFF64B5F6),
      foregroundColor: Color(0xFF121212),
      elevation: 6,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(16)),
      ),
    ),
    textTheme: const TextTheme(
      displayLarge: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 57,
        fontWeight: FontWeight.w400,
        color: Color(0xFFFFFFFF),
      ),
      displayMedium: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 45,
        fontWeight: FontWeight.w400,
        color: Color(0xFFFFFFFF),
      ),
      displaySmall: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 36,
        fontWeight: FontWeight.w400,
        color: Color(0xFFFFFFFF),
      ),
      headlineLarge: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 32,
        fontWeight: FontWeight.w600,
        color: Color(0xFFFFFFFF),
      ),
      headlineMedium: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 28,
        fontWeight: FontWeight.w600,
        color: Color(0xFFFFFFFF),
      ),
      headlineSmall: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: Color(0xFFFFFFFF),
      ),
      titleLarge: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 26,
        fontWeight: FontWeight.w500,
        color: Color(0xFFFFFFFF),
      ),
      titleMedium: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 22,
        fontWeight: FontWeight.w500,
        color: Color(0xFFFFFFFF),
      ),
      titleSmall: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 20,
        fontWeight: FontWeight.w500,
        color: Color(0xFFE0E0E0),
      ),
      bodyLarge: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 22,
        fontWeight: FontWeight.w400,
        color: Color(0xFFFFFFFF),
      ),
      bodyMedium: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 20,
        fontWeight: FontWeight.w400,
        color: Color(0xFFFFFFFF),
      ),
      bodySmall: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 18,
        fontWeight: FontWeight.w400,
        color: Color(0xFFE0E0E0),
      ),
      labelLarge: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 20,
        fontWeight: FontWeight.w500,
        color: Color(0xFFFFFFFF),
      ),
      labelMedium: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 18,
        fontWeight: FontWeight.w500,
        color: Color(0xFFE0E0E0),
      ),
      labelSmall: TextStyle(
        fontFamily: 'AwanZaman',
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: Color(0xFFE0E0E0),
      ),
    ),
  );

  // Gradient backgrounds
  static LinearGradient lightGradient = const LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF4285F4), // Google Blue
      Color(0xFF1976D2), // Darker Blue
      Color(0xFF0D47A1), // Deep Blue
    ],
    stops: [0.0, 0.5, 1.0],
  );

  static LinearGradient darkGradient = const LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF0F172A), // Very Dark Slate
      Color(0xFF1E293B), // Dark Slate
      Color(0xFF334155), // Slate
    ],
    stops: [0.0, 0.5, 1.0],
  );

  // Background container with gradient
  static Widget gradientBackground({
    required Widget child,
    required bool isDarkMode,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: isDarkMode ? darkGradient : lightGradient,
      ),
      child: child,
    );
  }

  // Blur effect for dialogs
  static Widget blurredBackground({required Widget child}) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
      ),
      child: child,
    );
  }

  // Custom dialog with blur effect
  static Future<T?> showBlurredDialog<T>({
    required BuildContext context,
    required Widget child,
    bool barrierDismissible = true,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: Colors.black.withValues(alpha: 0.5),
      builder: (context) => child,
    );
  }

  // Enhanced dialog with modern styling
  static Future<T?> showModernDialog<T>({
    required BuildContext context,
    required String title,
    required Widget content,
    List<Widget>? actions,
    bool barrierDismissible = true,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: Colors.black.withValues(alpha: 0.5),
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(24),
        ),
        child: Container(
          constraints: const BoxConstraints(maxWidth: 400),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 16),
              content,
              if (actions != null) ...[
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: actions,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
