rules_version = '2';

// HM Device Errors - Unified Firebase Security Rules v1.1.0
// Enhanced security rules with Google Drive integration and advanced attachment management
//
// SECURITY FEATURES:
// - User authentication and authorization
// - Role-based access control (Admin/User)
// - Data validation and sanitization
// - File upload restrictions and validation
// - Audit trail for sensitive operations
// - Protection against unauthorized access
// - Google Drive backup configuration support
// - Remote config integration
// - Enhanced storage management
// - Advanced attachment cache management
// - Attachment access logging and analytics
// - Improved file size limits for better UX
// - Enhanced MIME type validation for Google Drive
// - Optimized for performance and app size
//
// NEW IN v1.1.0:
// - Enhanced attachment validation with optional fields
// - Attachment cache metadata tracking
// - Download statistics and analytics
// - User-specific cache settings
// - Global cache monitoring
// - Attachment access logging
// - Increased file size limits (Images: 15MB, Videos: 100MB, Documents: 50MB)
// - Enhanced MIME type support for Google Drive files
// - Cache and thumbnail storage rules

service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function getUserData() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data;
    }

    function isUserActive() {
      return request.auth != null &&
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             getUserData().isActive == true;
    }

    function isUserNotExpired() {
      return request.auth != null &&
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             (getUserData().expiryDate == null || getUserData().expiryDate > request.time);
    }

    function isValidUser() {
      return isAuthenticated() && isUserActive() && isUserNotExpired();
    }

    function isAdmin() {
      return isValidUser() &&
             getUserData().role == 'admin';
    }

    function isSuperAdmin() {
      return isValidUser() &&
             (getUserData().role == 'admin' || getUserData().role == 'super_admin') &&
             (getUserData().isMainAdmin == true || getUserData().email == '<EMAIL>');
    }

    function isMainAdmin() {
      return isValidUser() &&
             (getUserData().isMainAdmin == true ||
              getUserData().email == '<EMAIL>');
    }

    function isMainAdminDocument(docData) {
      return docData.isMainAdmin == true || docData.email == '<EMAIL>';
    }

    function isOwner(userId) {
      return isValidUser() && request.auth.uid == userId;
    }

    // Production-ready setup function - more secure
    function isInitialSetup() {
      // Allow initial setup temporarily for development/testing
      // Set to false for production after initial setup is complete
      return true; // Set to true for initial setup, false for production
    }

    // Validate storage configuration data
    function isValidStorageConfig(data) {
      return data.keys().hasAll(['configName', 'clientId', 'isActive']) &&
             data.configName is string &&
             data.configName in ['google_drive', 'onedrive', 'dropbox', 'aws_s3'] &&
             data.clientId is string &&
             data.clientId.size() > 0 &&
             data.isActive is bool &&
             // Ensure sensitive data is encrypted (clientSecret should be encrypted)
             (!data.keys().hasAny(['clientSecret']) || data.clientSecret is string) &&
             // Additional validation for Google Drive specific fields
             (data.configName != 'google_drive' ||
              (data.keys().hasAny(['clientEmail', 'projectId']) &&
               data.clientEmail is string &&
               data.projectId is string));
    }

    // Validate attachment data (enhanced for new features)
    function isValidAttachment(data) {
      return data.keys().hasAll(['id', 'fileName', 'originalFileName', 'type', 'url', 'fileSize', 'mimeType', 'uploadedAt', 'uploadedBy']) &&
             data.id is string &&
             data.id.size() > 0 &&
             data.fileName is string &&
             data.fileName.size() > 0 &&
             data.originalFileName is string &&
             data.originalFileName.size() > 0 &&
             data.type is string &&
             data.type in ['image', 'video', 'document'] &&
             data.url is string &&
             data.url.size() > 0 &&
             data.fileSize is number &&
             data.fileSize > 0 &&
             data.mimeType is string &&
             data.mimeType.size() > 0 &&
             data.uploadedAt is timestamp &&
             data.uploadedBy is string &&
             data.uploadedBy.size() > 0 &&
             isValidFileSize(data.fileSize, data.type) &&
             isValidMimeType(data.mimeType, data.type) &&
             // Optional fields validation
             (!data.keys().hasAny(['thumbnailUrl']) || (data.thumbnailUrl is string)) &&
             (!data.keys().hasAny(['metadata']) || (data.metadata is map)) &&
             (!data.keys().hasAny(['cacheInfo']) || (data.cacheInfo is map)) &&
             (!data.keys().hasAny(['downloadCount']) || (data.downloadCount is number && data.downloadCount >= 0)) &&
             (!data.keys().hasAny(['lastAccessed']) || (data.lastAccessed is timestamp));
    }

    // Validate MIME types for security (enhanced for Google Drive support)
    function isValidMimeType(mimeType, type) {
      return (type == 'image' && mimeType.matches('image/(jpeg|jpg|png|gif|webp|bmp|tiff)')) ||
             (type == 'video' && mimeType.matches('video/(mp4|avi|mov|wmv|flv|webm|3gpp|quicktime|x-msvideo|x-matroska)')) ||
             (type == 'document' && mimeType.matches('application/(pdf|msword|vnd\\.openxmlformats-officedocument\\.wordprocessingml\\.document|vnd\\.ms-excel|vnd\\.openxmlformats-officedocument\\.spreadsheetml\\.sheet|vnd\\.ms-powerpoint|vnd\\.openxmlformats-officedocument\\.presentationml\\.presentation|rtf|vnd\\.oasis\\.opendocument\\.text)|text/plain')) ||
             // Allow Google Drive MIME types
             (mimeType == 'application/octet-stream'); // Google Drive sometimes returns this
    }

    // Validate device error with attachments (simplified)
    function isValidDeviceError(data) {
      return data.keys().hasAll(['categoryId', 'manufacturer', 'model', 'errorCode', 'description', 'solution', 'createdAt', 'createdBy']) &&
             data.categoryId is string &&
             data.manufacturer is string &&
             data.model is string &&
             data.errorCode is string &&
             data.description is string &&
             data.solution is string &&
             data.createdAt is timestamp &&
             data.createdBy is string &&
             // Basic validation for attachments if present
             (!data.keys().hasAny(['attachments']) ||
              (data.attachments is list &&
               data.attachments.size() <= 10)); // Max 10 attachments
    }

    // Simplified attachment validation for individual attachments
    function isValidAttachmentType(type) {
      return type in ['image', 'video', 'document'];
    }

    // Validate file size based on type (enhanced limits for better user experience)
    function isValidFileSize(fileSize, type) {
      return fileSize > 0 &&
             ((type == 'image' && fileSize <= 15728640) ||    // 15MB for images (increased for high-res photos)
              (type == 'video' && fileSize <= 104857600) ||   // 100MB for videos (increased for better quality)
              (type == 'document' && fileSize <= 52428800) || // 50MB for documents (increased for complex docs)
              fileSize <= 10485760);                          // 10MB default (increased)
    }

    // Validate update operations for storage config
    function isValidStorageConfigUpdate(newData, oldData) {
      return newData.configName == oldData.configName && // Cannot change config type
             newData.keys().hasAll(['updatedAt', 'updatedBy']) &&
             newData.updatedAt is timestamp &&
             newData.updatedBy == request.auth.uid;
    }

    // Users collection
    match /users/{userId} {
      // Valid users can read user profiles, or allow during initial setup
      allow read: if isValidUser() || isInitialSetup();

      // Allow creation during initial setup or by admins
      allow create: if isAdmin() || isInitialSetup() ||
                     // Allow creation of the main admin user during setup
                     (request.resource.data.email == '<EMAIL>' &&
                      request.resource.data.role == 'admin');

      // Users can update their own profiles (except status fields), admins can update any profile
      allow update: if (isOwner(userId) &&
                       // Users cannot change their own active status or expiry date
                       (!request.resource.data.diff(resource.data).affectedKeys().hasAny(['isActive', 'expiryDate']))) ||
                     (isAdmin() && !isMainAdminDocument(resource.data)) ||
                     (isMainAdmin() && isMainAdminDocument(resource.data));

      // Only main admin can delete main admin, admins can delete regular users
      allow delete: if (isMainAdmin() && isMainAdminDocument(resource.data)) ||
                     (isAdmin() && !isMainAdminDocument(resource.data));
    }

    // Categories
    match /categories/{categoryId} {
      // Valid users can read categories, or allow during initial setup
      allow read: if isValidUser() || isInitialSetup();

      // Only admins can create, update, or delete categories, or allow during initial setup
      allow write: if isAdmin() || isInitialSetup();
    }

    // Device Categories
    match /deviceCategories/{categoryId} {
      // Valid users can read device categories, or allow during initial setup
      allow read: if isValidUser() || isInitialSetup();

      // Only admins can create, update, or delete device categories, or allow during initial setup
      allow write: if isAdmin() || isInitialSetup();
    }

    // Manufacturers
    match /manufacturers/{manufacturerId} {
      // Valid users can read manufacturers, or allow during initial setup
      allow read: if isValidUser() || isInitialSetup();

      // Only admins can create, update, or delete manufacturers, or allow during initial setup
      allow write: if isAdmin() || isInitialSetup();
    }

    // Device Models
    match /deviceModels/{modelId} {
      // Valid users can read device models, or allow during initial setup
      allow read: if isValidUser() || isInitialSetup();

      // Only admins can create, update, or delete device models, or allow during initial setup
      allow write: if isAdmin() || isInitialSetup();
    }

    // Faults/Errors
    match /faults/{faultId} {
      // Valid users can read faults, or allow during initial setup
      allow read: if isValidUser() || isInitialSetup();

      // Only admins can create new faults, or allow during initial setup
      allow create: if (isAdmin() && isValidDeviceError(request.resource.data)) || isInitialSetup();

      // Only admins can update or delete faults, or allow during initial setup
      allow update: if (isAdmin() && isValidDeviceError(request.resource.data)) || isInitialSetup();
      allow delete: if isAdmin() || isInitialSetup();
    }

    // Auth records
    match /auth/{userId} {
      // Users can read their own auth records, admins can read any, or allow during initial setup
      allow read: if isOwner(userId) || isAdmin() || isInitialSetup();

      // Only the user or an admin can write to auth records, or allow during initial setup
      allow write: if isOwner(userId) || isAdmin() || isInitialSetup();
    }

    // Login History
    match /loginHistory/{entryId} {
      // Users can read their own login history, admins can read any, or allow during initial setup
      allow read: if (isValidUser() && resource.data.userId == request.auth.uid) || isAdmin() || isInitialSetup();

      // Only valid users can create login history entries for themselves, or allow during initial setup
      allow create: if (isValidUser() && request.resource.data.userId == request.auth.uid) || isInitialSetup();

      // No one can update login history, except during initial setup
      allow update: if isInitialSetup();

      // Only admins can delete login history, or allow during initial setup
      allow delete: if isAdmin() || isInitialSetup();
    }

    // User Settings
    match /userSettings/{userId} {
      // Users can read their own settings, admins can read any, or allow during initial setup
      allow read: if isOwner(userId) || isAdmin() || isInitialSetup();

      // Users can update their own settings, admins can update any, or allow during initial setup
      allow write: if isOwner(userId) || isAdmin() || isInitialSetup();
    }

    // Favorites
    match /favorites/{favoriteId} {
      // Users can read their own favorites, admins can read any, or allow during initial setup
      allow read: if (isValidUser() && resource.data.userId == request.auth.uid) || isAdmin() || isInitialSetup();

      // Users can create favorites for themselves, or allow during initial setup
      allow create: if (isValidUser() && request.resource.data.userId == request.auth.uid) || isInitialSetup();

      // Users can update or delete their own favorites, or allow during initial setup
      allow update, delete: if (isValidUser() && resource.data.userId == request.auth.uid) || isAdmin() || isInitialSetup();
    }

    // App Configuration
    match /appConfiguration/{configDocId} {
      // Valid users can read app configuration, or allow during initial setup
      allow read: if isValidUser() || isInitialSetup();

      // Only admins can update app configuration, or allow during initial setup
      allow write: if isAdmin() || isInitialSetup();
    }

    // Storage Configuration
    match /storageConfig/{configId} {
      // Valid users can read storage configuration, or allow during initial setup
      allow read: if isValidUser() || isInitialSetup();

      // Only admins can create storage configuration, or allow during initial setup
      allow create: if (isAdmin() &&
                       isValidStorageConfig(request.resource.data) &&
                       request.resource.data.keys().hasAll(['createdAt', 'createdBy']) &&
                       request.resource.data.createdBy == request.auth.uid) ||
                      isInitialSetup();

      // Only admins can update storage configuration, or allow during initial setup
      allow update: if (isAdmin() &&
                       isValidStorageConfigUpdate(request.resource.data, resource.data)) ||
                      isInitialSetup();

      // Only admins can delete storage configuration, or allow during initial setup
      allow delete: if isAdmin() || isInitialSetup();
    }

    // App Storage Settings (special document)
    match /storageConfig/app_storage_settings {
      // Valid users can read app storage settings, or allow during initial setup
      allow read: if isValidUser() || isInitialSetup();

      // Only admins can update app storage settings, or allow during initial setup
      allow write: if isAdmin() || isInitialSetup();
    }

    // Login Logs
    match /login_logs/{logId} {
      // Admins can read any login logs, valid users can read their own logs
      allow read: if isAdmin() || (isValidUser() && resource.data.userId == request.auth.uid) || isInitialSetup();

      // Anyone can create login logs (for failed login attempts and initial authentication)
      allow create: if true;

      // No one can update login logs
      allow update: if false;

      // Only admins can delete login logs
      allow delete: if isAdmin() || isInitialSetup();
    }

    // Device Errors
    match /device_errors/{errorId} {
      // Valid users can read device errors, or allow during initial setup
      allow read: if isValidUser() || isInitialSetup();

      // Valid users can create device errors with validation, or allow during initial setup
      allow create: if (isValidUser() && isValidDeviceError(request.resource.data)) || isInitialSetup();

      // Users can update errors they created, admins can update any, with validation, or allow during initial setup
      allow update: if ((isValidUser() && resource.data.createdBy == request.auth.uid && isValidDeviceError(request.resource.data)) ||
                       (isAdmin() && isValidDeviceError(request.resource.data))) || isInitialSetup();

      // Only admins can delete device errors, or allow during initial setup
      allow delete: if isAdmin() || isInitialSetup();

      // Nested attachments subcollection (simplified)
      match /attachments/{attachmentId} {
        // Valid users can read attachments, or allow during initial setup
        allow read: if isValidUser() || isInitialSetup();

        // Users can create attachments for errors they own, admins can create any, or allow during initial setup
        allow create: if (isValidUser() && isValidAttachment(request.resource.data)) ||
                        (isAdmin() && isValidAttachment(request.resource.data)) ||
                        isInitialSetup();

        // Users can update attachments for errors they own, admins can update any, or allow during initial setup
        allow update: if (isValidUser() && isValidAttachment(request.resource.data)) ||
                        (isAdmin() && isValidAttachment(request.resource.data)) ||
                        isInitialSetup();

        // Users can delete attachments for errors they own, admins can delete any, or allow during initial setup
        allow delete: if isValidUser() || isAdmin() || isInitialSetup();
      }
    }

    // Companies
    match /companies/{companyId} {
      // Valid users can read companies, or allow during initial setup
      allow read: if isValidUser() || isInitialSetup();

      // Only admins can create, update, or delete companies, or allow during initial setup
      allow write: if isAdmin() || isInitialSetup();
    }

    // Models
    match /models/{modelId} {
      // Valid users can read models, or allow during initial setup
      allow read: if isValidUser() || isInitialSetup();

      // Only admins can create, update, or delete models, or allow during initial setup
      allow write: if isAdmin() || isInitialSetup();
    }

    // Login History (new collection)
    match /login_history/{historyId} {
      // Admins can read any login history, valid users can read their own
      allow read: if isAdmin() || (isValidUser() && resource.data.userId == request.auth.uid) || isInitialSetup();

      // Anyone can create login history (for authentication tracking)
      allow create: if true;

      // No one can update login history
      allow update: if false;

      // Only admins can delete login history
      allow delete: if isAdmin() || isInitialSetup();
    }

    // Update Events Collection
    match /update_events/{eventId} {
      // Admins can read all update events, valid users can read their own
      allow read: if isAdmin() || (isValidUser() && resource.data.userId == request.auth.uid);

      // Anyone can create update events (for tracking)
      allow create: if true;

      // No one can update events
      allow update: if false;

      // Only admins can delete update events
      allow delete: if isAdmin();
    }

    // User Settings (updated path)
    match /user_settings/{userId} {
      // Users can read their own settings, admins can read any, or allow during initial setup
      allow read: if isOwner(userId) || isAdmin() || isInitialSetup();

      // Users can update their own settings, admins can update any, or allow during initial setup
      allow write: if isOwner(userId) || isAdmin() || isInitialSetup();
    }

    // Security Audit Logs (for tracking sensitive operations)
    match /securityLogs/{logId} {
      // Only admins can read security logs
      allow read: if isAdmin() || isInitialSetup();

      // System can create security logs (for audit trail)
      allow create: if true;

      // No one can update or delete security logs
      allow update, delete: if false;
    }

    // System Configuration (for app-wide settings)
    match /systemConfig/{configId} {
      // Valid users can read system configuration
      allow read: if isValidUser() || isInitialSetup();

      // Only super admins can modify system configuration
      allow write: if isSuperAdmin() || isInitialSetup();
    }

    // Google Drive Backup Logs
    match /backup_logs/{logId} {
      // Admins can read backup logs, valid users can read their own
      allow read: if isAdmin() || (isValidUser() && resource.data.userId == request.auth.uid) || isInitialSetup();

      // System and admins can create backup logs
      allow create: if isAdmin() || isInitialSetup();

      // No one can update backup logs (immutable for audit)
      allow update: if false;

      // Only admins can delete backup logs
      allow delete: if isAdmin() || isInitialSetup();
    }

    // Remote Config Cache (for caching remote config values)
    match /remote_config_cache/{configKey} {
      // Valid users can read cached remote config
      allow read: if isValidUser() || isInitialSetup();

      // Only admins can update remote config cache
      allow write: if isAdmin() || isInitialSetup();
    }

    // App Update Events (for tracking app updates)
    match /app_update_events/{eventId} {
      // Admins can read all update events, valid users can read their own
      allow read: if isAdmin() || (isValidUser() && resource.data.userId == request.auth.uid) || isInitialSetup();

      // Anyone can create update events (for tracking)
      allow create: if true;

      // No one can update events (immutable for audit)
      allow update: if false;

      // Only admins can delete update events
      allow delete: if isAdmin() || isInitialSetup();
    }

    // Storage Sync Status (for tracking sync operations)
    match /storage_sync_status/{syncId} {
      // Admins can read all sync status, valid users can read their own
      allow read: if isAdmin() || (isValidUser() && resource.data.userId == request.auth.uid) || isInitialSetup();

      // System and admins can create/update sync status
      allow create, update: if isAdmin() || isInitialSetup();

      // Only admins can delete sync status
      allow delete: if isAdmin() || isInitialSetup();
    }

    // Firebase Remote Config Logs (for tracking remote config changes)
    match /remote_config_logs/{logId} {
      // Only admins can read remote config logs
      allow read: if isAdmin() || isInitialSetup();

      // System can create remote config logs
      allow create: if true;

      // No one can update remote config logs (immutable for audit)
      allow update: if false;

      // Only super admins can delete remote config logs
      allow delete: if isSuperAdmin() || isInitialSetup();
    }

    // Google Drive File Metadata (for tracking uploaded files)
    match /google_drive_files/{fileId} {
      // Admins can read all file metadata, valid users can read their own
      allow read: if isAdmin() || (isValidUser() && resource.data.uploadedBy == request.auth.uid) || isInitialSetup();

      // Valid users can create file metadata for their uploads
      allow create: if (isValidUser() && request.resource.data.uploadedBy == request.auth.uid) || isAdmin() || isInitialSetup();

      // Users can update metadata for their files, admins can update any
      allow update: if (isValidUser() && resource.data.uploadedBy == request.auth.uid) || isAdmin() || isInitialSetup();

      // Users can delete metadata for their files, admins can delete any
      allow delete: if (isValidUser() && resource.data.uploadedBy == request.auth.uid) || isAdmin() || isInitialSetup();
    }

    // App Performance Metrics (for monitoring)
    match /performance_metrics/{metricId} {
      // Only admins can read performance metrics
      allow read: if isAdmin() || isInitialSetup();

      // System can create performance metrics
      allow create: if true;

      // No one can update performance metrics
      allow update: if false;

      // Only admins can delete performance metrics
      allow delete: if isAdmin() || isInitialSetup();
    }

    // Error Reports (for crash reporting and debugging)
    match /error_reports/{reportId} {
      // Admins can read all error reports, users can read their own
      allow read: if isAdmin() || (isValidUser() && resource.data.userId == request.auth.uid) || isInitialSetup();

      // Anyone can create error reports (for crash reporting)
      allow create: if true;

      // No one can update error reports (immutable for debugging)
      allow update: if false;

      // Only admins can delete error reports
      allow delete: if isAdmin() || isInitialSetup();
    }

    // Attachment Cache Metadata (for tracking cached files)
    match /attachment_cache/{cacheId} {
      // Valid users can read cache metadata for their files, admins can read all
      allow read: if isValidUser() || isInitialSetup();

      // Valid users can create cache metadata for their downloads
      allow create: if (isValidUser() &&
                       request.resource.data.keys().hasAll(['attachmentId', 'userId', 'cachedAt', 'fileSize', 'lastAccessed']) &&
                       request.resource.data.userId == request.auth.uid) ||
                      isAdmin() || isInitialSetup();

      // Users can update cache metadata for their files (like lastAccessed)
      allow update: if (isValidUser() && resource.data.userId == request.auth.uid) ||
                      isAdmin() || isInitialSetup();

      // Users can delete cache metadata for their files, admins can delete any
      allow delete: if (isValidUser() && resource.data.userId == request.auth.uid) ||
                      isAdmin() || isInitialSetup();
    }

    // Attachment Download Statistics (for analytics)
    match /attachment_stats/{attachmentId} {
      // Valid users can read download statistics, admins can read all
      allow read: if isValidUser() || isInitialSetup();

      // System can create/update download statistics
      allow create, update: if isValidUser() || isAdmin() || isInitialSetup();

      // Only admins can delete statistics
      allow delete: if isAdmin() || isInitialSetup();
    }

    // User Cache Settings (for managing user-specific cache preferences)
    match /user_cache_settings/{userId} {
      // Users can read their own cache settings, admins can read any
      allow read: if isOwner(userId) || isAdmin() || isInitialSetup();

      // Users can manage their own cache settings, admins can manage any
      allow write: if isOwner(userId) || isAdmin() || isInitialSetup();
    }

    // Global Cache Statistics (for monitoring overall cache usage)
    match /global_cache_stats/{statId} {
      // Only admins can read global cache statistics
      allow read: if isAdmin() || isInitialSetup();

      // System can create/update global cache statistics
      allow create, update: if isAdmin() || isInitialSetup();

      // Only admins can delete global cache statistics
      allow delete: if isAdmin() || isInitialSetup();
    }

    // Attachment Access Logs (for tracking file access patterns)
    match /attachment_access_logs/{logId} {
      // Admins can read all access logs, users can read their own
      allow read: if isAdmin() || (isValidUser() && resource.data.userId == request.auth.uid) || isInitialSetup();

      // Valid users can create access logs for their file access
      allow create: if (isValidUser() &&
                       request.resource.data.keys().hasAll(['attachmentId', 'userId', 'accessedAt', 'accessType']) &&
                       request.resource.data.userId == request.auth.uid) ||
                      isAdmin() || isInitialSetup();

      // No one can update access logs (immutable for audit)
      allow update: if false;

      // Only admins can delete access logs
      allow delete: if isAdmin() || isInitialSetup();
    }

    // Default rule - deny access to any path not explicitly allowed above
    match /{document=**} {
      // Allow access during initial setup, otherwise deny by default
      allow read, write: if isInitialSetup() || false;
    }
  }
}

// Firebase Storage Security Rules
service firebase.storage {
  match /b/{bucket}/o {
    // Helper functions for storage
    function isAuthenticated() {
      return request.auth != null;
    }

    function isAdmin() {
      return isAuthenticated() &&
             firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    function isValidImageFile() {
      return resource.contentType.matches('image/.*') &&
             resource.size < 15 * 1024 * 1024; // 15MB limit (increased)
    }

    function isValidVideoFile() {
      return resource.contentType.matches('video/.*') &&
             resource.size < 100 * 1024 * 1024; // 100MB limit (increased)
    }

    function isValidDocumentFile() {
      return resource.contentType in ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/rtf', 'application/vnd.oasis.opendocument.text', 'text/plain'] &&
             resource.size < 50 * 1024 * 1024; // 50MB limit (increased)
    }

    function isValidAttachment() {
      return isValidImageFile() || isValidVideoFile() || isValidDocumentFile();
    }

    function isValidBackupFile() {
      return resource.contentType in ['application/json', 'application/zip', 'application/x-sqlite3'] &&
             resource.size < 100 * 1024 * 1024; // 100MB limit for backups
    }

    function isValidExportFile() {
      return resource.contentType in ['application/json', 'text/csv', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'] &&
             resource.size < 50 * 1024 * 1024; // 50MB limit for exports
    }

    // Device error attachments - Admin can manage, authenticated users can read
    match /device_errors/{errorId}/{attachmentId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin() && isValidAttachment();
      allow delete: if isAdmin();
    }

    // User profile images - Users can manage their own
    match /user_profiles/{userId}/{imageId} {
      allow read: if isAuthenticated();
      allow write: if (isAuthenticated() && request.auth.uid == userId && isValidImageFile()) || isAdmin();
      allow delete: if (isAuthenticated() && request.auth.uid == userId) || isAdmin();
    }

    // App assets - Admin only
    match /app_assets/{assetId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }

    // Backup files - Admin only
    match /backups/{backupId} {
      allow read: if isAdmin();
      allow write: if isAdmin() && isValidBackupFile();
      allow delete: if isAdmin();
    }

    // Google Drive backup files - Admin only
    match /google_drive_backups/{backupId} {
      allow read: if isAdmin();
      allow write: if isAdmin() && isValidBackupFile();
      allow delete: if isAdmin();
    }

    // Temporary upload files - Users can upload, admins can manage
    match /temp_uploads/{userId}/{fileId} {
      allow read: if isAuthenticated();
      allow write: if (isAuthenticated() && request.auth.uid == userId && isValidAttachment()) || isAdmin();
      allow delete: if (isAuthenticated() && request.auth.uid == userId) || isAdmin();
    }

    // Export files - Admin only
    match /exports/{exportId} {
      allow read: if isAdmin();
      allow write: if isAdmin() && isValidExportFile();
      allow delete: if isAdmin();
    }

    // Sync files - Admin only (for Google Drive sync)
    match /sync_files/{syncId} {
      allow read, write: if isAdmin();
    }

    // Cache files - Users can manage their own cache, admins can manage all
    match /cache/{userId}/{cacheId} {
      allow read: if isAuthenticated();
      allow write: if (isAuthenticated() && request.auth.uid == userId && isValidAttachment()) || isAdmin();
      allow delete: if (isAuthenticated() && request.auth.uid == userId) || isAdmin();
    }

    // Thumbnail cache - Users can manage their own thumbnails, admins can manage all
    match /thumbnails/{userId}/{thumbnailId} {
      allow read: if isAuthenticated();
      allow write: if (isAuthenticated() && request.auth.uid == userId && isValidImageFile()) || isAdmin();
      allow delete: if (isAuthenticated() && request.auth.uid == userId) || isAdmin();
    }

    // Attachment previews - Users can access previews, admins can manage
    match /previews/{attachmentId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin() && isValidImageFile();
      allow delete: if isAdmin();
    }

    // Google Drive cached files - Users can access their cached files, admins can manage all
    match /google_drive_cache/{userId}/{fileId} {
      allow read: if isAuthenticated();
      allow write: if (isAuthenticated() && request.auth.uid == userId && isValidAttachment()) || isAdmin();
      allow delete: if (isAuthenticated() && request.auth.uid == userId) || isAdmin();
    }

    // Default deny rule
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
