# Google Drive Remote Config Setup

This document explains how to configure Google Drive integration using Firebase Remote Config in your Flutter app.

## Overview

The app now supports configuring Google Drive settings through Firebase Remote Config as a fallback when local database configuration is not available. This allows you to:

- Centrally manage Google Drive configuration
- Update settings without app updates
- Provide fallback configuration for new installations
- Enable/disable Google Drive integration remotely

## Configuration Priority

The app now supports configurable priority for Google Drive configuration:

### Remote Config as Primary (Default)
1. **Remote Config** (highest priority) - Set in Firebase Remote Config
2. **Database Configuration** (fallback) - Set through admin panel
3. **None** - Google Drive integration disabled

### Database as Primary (Alternative)
1. **Database Configuration** (highest priority) - Set through admin panel
2. **Remote Config** (fallback) - Set in Firebase Remote Config
3. **None** - Google Drive integration disabled

The priority can be managed through the Configuration Source Management screen in the admin panel.

## Required Remote Config Keys

Set these keys in your Firebase Remote Config:

### Configuration Management
- `google_drive_use_remote_config` (boolean) - Use remote config as primary source (default: true)
- `google_drive_config_source` (string) - Configuration source preference ('remote' or 'database')

### Core Configuration
- `google_drive_enabled` (boolean) - Enable/disable Google Drive integration
- `google_drive_client_id` (string) - Google Drive Client ID
- `google_drive_client_email` (string) - Service Account Email
- `google_drive_project_id` (string) - Google Cloud Project ID
- `google_drive_private_key_id` (string) - Private Key ID
- `google_drive_private_key` (string) - Private Key in PEM format

### Optional Configuration
- `google_drive_parent_folder_id` (string) - Default parent folder ID
- `google_drive_auth_uri` (string) - Auth URI (default: https://accounts.google.com/o/oauth2/auth)
- `google_drive_token_uri` (string) - Token URI (default: https://oauth2.googleapis.com/token)
- `google_drive_auth_provider_cert_url` (string) - Auth Provider X509 Cert URL
- `google_drive_client_cert_url` (string) - Client X509 Cert URL (optional, auto-generated if empty)
- `google_drive_universe_domain` (string) - Universe domain (default: googleapis.com)

## Setup Instructions

### 1. Create Google Service Account

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create or select a project
3. Enable Google Drive API
4. Create a Service Account:
   - Go to IAM & Admin > Service Accounts
   - Click "Create Service Account"
   - Fill in details and create
   - Generate a JSON key file

### 2. Configure Firebase Remote Config

1. Go to Firebase Console > Remote Config
2. Add the following parameters:

```json
{
  "google_drive_enabled": true,
  "google_drive_use_remote_config": true,
  "google_drive_config_source": "remote",
  "google_drive_client_id": "your-client-id",
  "google_drive_client_email": "<EMAIL>",
  "google_drive_project_id": "your-project-id",
  "google_drive_private_key_id": "your-private-key-id",
  "google_drive_private_key": "-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----",
  "google_drive_parent_folder_id": "optional-folder-id",
  "google_drive_auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "google_drive_token_uri": "https://oauth2.googleapis.com/token",
  "google_drive_auth_provider_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
  "google_drive_client_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/your-service-account%40project.iam.gserviceaccount.com",
  "google_drive_universe_domain": "googleapis.com"
}
```

### 3. Set Permissions

Share your Google Drive folder with the service account email:
1. Open Google Drive
2. Right-click on the folder you want to use
3. Click "Share"
4. Add the service account email with "Editor" permissions

## Usage in Code

### Automatic Fallback

The Google Drive service automatically checks for remote config when database config is not available:

```dart
// This will automatically use remote config if database config is not found
final googleDriveService = GoogleDriveService();
await googleDriveService.initialize();
```

### Manual Remote Config Access

```dart
// Access remote config directly
final remoteConfigProvider = Provider.of<RemoteConfigProvider>(context);

// Check if Google Drive is enabled
bool isEnabled = remoteConfigProvider.isGoogleDriveEnabled();

// Get configuration
StorageConfigModel? config = remoteConfigProvider.googleDriveConfig;

// Refresh remote config
await remoteConfigProvider.fetchAndActivate();
```

### Admin Interface

Access the Remote Config admin screen to monitor configuration status:

```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const RemoteConfigAdminScreen(),
  ),
);
```

### Configuration Source Management

Use the Configuration Source Management screen to choose between remote config and database as primary source:

```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const ConfigSourceManagementScreen(),
  ),
);
```

This interface allows you to:
- View current configuration status
- Switch between remote config and database as primary source
- Compare configurations from both sources
- Apply configuration changes

## Validation

The system validates that all required fields are present:

- Client ID
- Client Email
- Project ID
- Private Key

Missing fields will be reported in the admin interface and debug logs.

## Security Considerations

1. **Private Key Security**: Store the private key securely in Remote Config
2. **Access Control**: Limit who can modify Remote Config settings
3. **Monitoring**: Monitor Remote Config changes and access logs
4. **Fallback**: Always have database configuration as primary option

## Troubleshooting

### Common Issues

1. **"Google Drive configuration not found"**
   - Check that `google_drive_enabled` is set to `true`
   - Verify all required keys are present in Remote Config

2. **"Invalid Google Drive configuration"**
   - Check private key format (must include PEM headers)
   - Verify service account permissions

3. **"Permission denied"**
   - Ensure service account has access to the target folder
   - Check that Google Drive API is enabled

### Debug Information

Use the Remote Config admin screen to view:
- Configuration status
- Validation errors
- Debug information
- Configuration source (database vs remote config)

### Logs

Check debug logs for detailed error messages:
```
flutter logs | grep "Google Drive\|Remote Config"
```

## Best Practices

1. **Use Database Config for Production**: Set up proper database configuration for production environments
2. **Remote Config for Fallback**: Use remote config as a fallback or for testing
3. **Regular Updates**: Regularly update and rotate service account keys
4. **Monitor Usage**: Monitor Google Drive API usage and quotas
5. **Test Configuration**: Always test configuration changes in a development environment first

## API Reference

### RemoteConfigService

- `initialize()` - Initialize remote config
- `isGoogleDriveEnabled()` - Check if Google Drive is enabled
- `getGoogleDriveConfig()` - Get Google Drive configuration
- `fetchAndActivate()` - Fetch latest remote config
- `getGoogleDriveDebugInfo()` - Get debug information

### RemoteConfigProvider

- `initialize()` - Initialize provider
- `fetchAndActivate()` - Refresh configuration
- `isGoogleDriveEnabled()` - Check if enabled
- `validateGoogleDriveConfig()` - Validate configuration
- `getValidationErrors()` - Get validation errors
- `getConfigurationStatus()` - Get status information
