import 'dart:io';
import 'dart:convert';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import '../models/device_error_model.dart';
import '../models/category_model.dart' as cat;
import '../models/user_model.dart';
import '../services/firestore_service.dart';

class BackupService {
  final FirestoreService _firestoreService = FirestoreService();

  // Create full database backup
  Future<String?> createFullBackup({String? fileName}) async {
    try {
      final backupData = <String, dynamic>{};

      // Add metadata
      backupData['backup_info'] = {
        'created_at': DateTime.now().toIso8601String(),
        'version': '1.0.0',
        'type': 'full_backup',
      };

      // Backup categories
      try {
        final categories = await _firestoreService.getCategories();
        backupData['categories'] = categories.map((c) => c.toMap()).toList();
      } catch (e) {
        backupData['categories'] = [];
      }

      // Backup all errors
      try {
        final errors = await _firestoreService.getDeviceErrors();
        backupData['errors'] = errors.map((e) => e.toMap()).toList();
      } catch (e) {
        backupData['errors'] = [];
      }

      // Backup manufacturers and models
      try {
        final manufacturers = await _firestoreService.getManufacturers();
        backupData['manufacturers'] = manufacturers;

        final models = <String, dynamic>{};
        for (final manufacturer in manufacturers) {
          try {
            final manufacturerModels = await _firestoreService.getModelsForManufacturer(manufacturer);
            models[manufacturer] = manufacturerModels;
          } catch (e) {
            models[manufacturer] = [];
          }
        }
        backupData['models'] = models;
      } catch (e) {
        backupData['manufacturers'] = [];
        backupData['models'] = {};
      }

      // Backup users (admin only)
      try {
        final users = await _firestoreService.getUsers();
        backupData['users'] = users.map((u) => u.toMap()).toList();
      } catch (e) {
        backupData['users'] = [];
      }

      // Convert to JSON
      final jsonString = const JsonEncoder.withIndent('  ').convert(backupData);

      // Save to file
      final directory = await getApplicationDocumentsDirectory();
      final filePath = '${directory.path}/${fileName ?? 'hm_device_errors_backup_${DateTime.now().millisecondsSinceEpoch}'}.json';

      final file = File(filePath);
      await file.writeAsString(jsonString);

      return filePath;
    } catch (e) {
      throw Exception('Failed to create backup: $e');
    }
  }

  // Create errors-only backup
  Future<String?> createErrorsBackup(List<DeviceError> errors, {String? fileName}) async {
    try {
      final backupData = <String, dynamic>{};

      // Add metadata
      backupData['backup_info'] = {
        'created_at': DateTime.now().toIso8601String(),
        'version': '1.0.0',
        'type': 'errors_backup',
        'count': errors.length,
      };

      // Add errors
      backupData['errors'] = errors.map((e) => e.toMap()).toList();

      // Convert to JSON
      final jsonString = const JsonEncoder.withIndent('  ').convert(backupData);

      // Save to file
      final directory = await getApplicationDocumentsDirectory();
      final filePath = '${directory.path}/${fileName ?? 'errors_backup_${DateTime.now().millisecondsSinceEpoch}'}.json';

      final file = File(filePath);
      await file.writeAsString(jsonString);

      return filePath;
    } catch (e) {
      throw Exception('Failed to create errors backup: $e');
    }
  }

  // Restore from backup
  Future<Map<String, dynamic>> restoreFromBackup() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) {
        throw Exception('No file selected');
      }

      final file = result.files.first;
      String jsonString;

      if (file.bytes != null) {
        jsonString = utf8.decode(file.bytes!);
      } else if (file.path != null) {
        final fileObj = File(file.path!);
        jsonString = await fileObj.readAsString();
      } else {
        throw Exception('Failed to read file');
      }

      final backupData = jsonDecode(jsonString) as Map<String, dynamic>;

      // Validate backup format
      if (!backupData.containsKey('backup_info')) {
        throw Exception('Invalid backup file format');
      }

      final backupInfo = backupData['backup_info'] as Map<String, dynamic>;
      final backupType = backupInfo['type'] as String?;

      if (backupType == null) {
        throw Exception('Unknown backup type');
      }

      return backupData;
    } catch (e) {
      throw Exception('Failed to restore from backup: $e');
    }
  }

  // Restore categories from backup
  Future<int> restoreCategories(Map<String, dynamic> backupData) async {
    try {
      final categoriesData = backupData['categories'] as List<dynamic>?;
      if (categoriesData == null) return 0;

      int restoredCount = 0;
      for (final categoryData in categoriesData) {
        try {
          final category = cat.Category.fromMap(categoryData as Map<String, dynamic>, categoryData['id'] ?? '');
          await _firestoreService.addCategory(category);
          restoredCount++;
        } catch (e) {
          // Skip invalid categories
          continue;
        }
      }

      return restoredCount;
    } catch (e) {
      throw Exception('Failed to restore categories: $e');
    }
  }

  // Restore errors from backup
  Future<int> restoreErrors(Map<String, dynamic> backupData) async {
    try {
      final errorsData = backupData['errors'] as List<dynamic>?;
      if (errorsData == null) return 0;

      int restoredCount = 0;
      for (final errorData in errorsData) {
        try {
          final error = DeviceError.fromMap(errorData as Map<String, dynamic>, errorData['id'] ?? '');
          await _firestoreService.addDeviceError(error);
          restoredCount++;
        } catch (e) {
          // Skip invalid errors
          continue;
        }
      }

      return restoredCount;
    } catch (e) {
      throw Exception('Failed to restore errors: $e');
    }
  }

  // Restore manufacturers and models from backup
  Future<int> restoreManufacturersAndModels(Map<String, dynamic> backupData) async {
    try {
      final manufacturersData = backupData['manufacturers'] as List<dynamic>?;
      final modelsData = backupData['models'] as Map<String, dynamic>?;

      if (manufacturersData == null) return 0;

      int restoredCount = 0;

      for (final manufacturerName in manufacturersData) {
        try {
          await _firestoreService.addManufacturer(manufacturerName as String);
          restoredCount++;

          // Add models for this manufacturer
          if (modelsData != null && modelsData.containsKey(manufacturerName)) {
            final models = modelsData[manufacturerName] as List<dynamic>?;
            if (models != null) {
              for (final modelName in models) {
                try {
                  await _firestoreService.addModel(manufacturerName, modelName as String);
                } catch (e) {
                  // Skip invalid models
                  continue;
                }
              }
            }
          }
        } catch (e) {
          // Skip invalid manufacturers
          continue;
        }
      }

      return restoredCount;
    } catch (e) {
      throw Exception('Failed to restore manufacturers and models: $e');
    }
  }

  // Restore users from backup (admin only)
  Future<int> restoreUsers(Map<String, dynamic> backupData) async {
    try {
      final usersData = backupData['users'] as List<dynamic>?;
      if (usersData == null) return 0;

      int restoredCount = 0;
      for (final userData in usersData) {
        try {
          final user = UserModel.fromMap(userData as Map<String, dynamic>);
          await _firestoreService.addUser(user);
          restoredCount++;
        } catch (e) {
          // Skip invalid users
          continue;
        }
      }

      return restoredCount;
    } catch (e) {
      throw Exception('Failed to restore users: $e');
    }
  }

  // Share backup file
  Future<void> shareBackupFile(String filePath, String fileName) async {
    try {
      await Share.shareXFiles(
        [XFile(filePath)],
        text: 'Database backup: $fileName',
      );
    } catch (e) {
      throw Exception('Failed to share backup file: $e');
    }
  }

  // Get backup file info
  Future<Map<String, dynamic>?> getBackupInfo(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) return null;

      final jsonString = await file.readAsString();
      final backupData = jsonDecode(jsonString) as Map<String, dynamic>;

      return backupData['backup_info'] as Map<String, dynamic>?;
    } catch (e) {
      return null;
    }
  }

  // Validate backup file
  Future<bool> validateBackupFile(String filePath) async {
    try {
      final info = await getBackupInfo(filePath);
      return info != null && info.containsKey('version') && info.containsKey('type');
    } catch (e) {
      return false;
    }
  }

  // Clear all data (for restore)
  Future<void> clearAllData() async {
    try {
      // This is a dangerous operation - should only be used during restore
      await _firestoreService.clearAllErrors();
      await _firestoreService.clearAllCategories();
      await _firestoreService.clearAllManufacturers();
    } catch (e) {
      throw Exception('Failed to clear data: $e');
    }
  }
}
