import 'package:flutter/material.dart';
// import '../lib/screens/settings/cache_settings_screen.dart';
// import '../lib/models/cache_settings_model.dart';

/// تطبيق تجريبي لعرض شاشة إعدادات الكاش المتقدمة
void main() {
  runApp(const CacheSettingsDemoApp());
}

class CacheSettingsDemoApp extends StatelessWidget {
  const CacheSettingsDemoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'إعدادات الكاش المتقدمة - تجريبي',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Cairo',
        textTheme: const TextTheme(
          bodyLarge: TextStyle(fontSize: 16),
          bodyMedium: TextStyle(fontSize: 14),
          titleMedium: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
      ),
      home: const DemoHomeScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class DemoHomeScreen extends StatelessWidget {
  const DemoHomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تجريبي - إعدادات الكاش'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'مرحباً بك في تجريبي إعدادات الكاش المتقدمة',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'هذا التطبيق التجريبي يعرض الميزات الجديدة لإدارة الكاش:',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            _buildFeatureCard(
              icon: Icons.analytics,
              title: 'إحصائيات مفصلة',
              description: 'عرض تفاصيل استخدام الكاش مع مخططات بصرية',
              color: Colors.blue,
            ),
            const SizedBox(height: 12),
            _buildFeatureCard(
              icon: Icons.settings,
              title: 'إعدادات متقدمة',
              description: 'تحكم في الحجم، الاحتفاظ، والشبكة',
              color: Colors.green,
            ),
            const SizedBox(height: 12),
            _buildFeatureCard(
              icon: Icons.build,
              title: 'أدوات الصيانة',
              description: 'تحسين وصيانة الكاش تلقائياً',
              color: Colors.orange,
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _openCacheSettings(context),
                icon: const Icon(Icons.launch),
                label: const Text('فتح إعدادات الكاش'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  textStyle: const TextStyle(fontSize: 18),
                ),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => _showCurrentSettings(context),
                icon: const Icon(Icons.info),
                label: const Text('عرض الإعدادات الحالية'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  textStyle: const TextStyle(fontSize: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureCard({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _openCacheSettings(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const DemoCacheSettingsScreen(),
      ),
    );
  }

  void _showCurrentSettings(BuildContext context) {
    // عرض الإعدادات الحالية في dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الإعدادات الحالية'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('حجم الكاش الأقصى: 200 ميجابايت'),
              Text('مدة الاحتفاظ: 14 يوم'),
              Text('التنظيف التلقائي: مفعل'),
              Text('thumbnails الصور: مفعل'),
              Text('thumbnails الفيديوهات: مفعل'),
              Text('جودة thumbnails: 75%'),
              Text('WiFi فقط: غير مفعل'),
              Text('ضغط تلقائي: مفعل'),
              Text('تنظيف مجدول: مفعل'),
              Text('فترة التنظيف: 3 أيام'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}

/// Widget تجريبي لعرض إحصائيات وهمية
class MockCacheStats extends StatelessWidget {
  const MockCacheStats({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إحصائيات تجريبية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildStatRow('إجمالي الملفات', '42'),
            _buildStatRow('الحجم الإجمالي', '156.7 ميجابايت'),
            _buildStatRow('thumbnails الفيديوهات', '23.4 ميجابايت'),
            const SizedBox(height: 12),
            const LinearProgressIndicator(
              value: 0.78,
              backgroundColor: Colors.grey,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
            const SizedBox(height: 8),
            const Text(
              'استخدام الكاش: 78.4%',
              style: TextStyle(fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}

/// شاشة تجريبية لإعدادات الكاش
class DemoCacheSettingsScreen extends StatefulWidget {
  const DemoCacheSettingsScreen({super.key});

  @override
  State<DemoCacheSettingsScreen> createState() => _DemoCacheSettingsScreenState();
}

class _DemoCacheSettingsScreenState extends State<DemoCacheSettingsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // إعدادات تجريبية
  int _maxCacheSize = 200;
  bool _wifiOnly = false;
  bool _autoCompress = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات الكاش المتقدمة - تجريبي'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.analytics), text: 'الإحصائيات'),
            Tab(icon: Icon(Icons.settings), text: 'الإعدادات'),
            Tab(icon: Icon(Icons.build), text: 'الأدوات'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildStatsTab(),
          _buildSettingsTab(),
          _buildToolsTab(),
        ],
      ),
    );
  }

  Widget _buildStatsTab() {
    return const SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          MockCacheStats(),
          SizedBox(height: 16),
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'مخطط الاستخدام',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 16),
                  LinearProgressIndicator(
                    value: 0.78,
                    backgroundColor: Colors.grey,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                    minHeight: 8,
                  ),
                  SizedBox(height: 8),
                  Text('استخدام الكاش: 78.4%'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'إعدادات الحجم',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  Text('الحد الأقصى: $_maxCacheSize ميجابايت'),
                  Slider(
                    value: _maxCacheSize.toDouble(),
                    min: 50,
                    max: 1000,
                    divisions: 19,
                    label: '$_maxCacheSize MB',
                    onChanged: (value) {
                      setState(() {
                        _maxCacheSize = value.round();
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'إعدادات الشبكة',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SwitchListTile(
                    title: const Text('WiFi فقط'),
                    value: _wifiOnly,
                    onChanged: (value) {
                      setState(() {
                        _wifiOnly = value;
                      });
                    },
                  ),
                  SwitchListTile(
                    title: const Text('ضغط تلقائي'),
                    value: _autoCompress,
                    onChanged: (value) {
                      setState(() {
                        _autoCompress = value;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToolsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'أدوات الصيانة',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () => _showMessage('تم مسح الكاش بنجاح'),
                      icon: const Icon(Icons.clear_all),
                      label: const Text('مسح الكاش'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () => _showMessage('تم تحسين الكاش بنجاح'),
                      icon: const Icon(Icons.tune),
                      label: const Text('تحسين الكاش'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }
}
