import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'providers/theme_provider.dart';
import 'providers/locale_provider.dart';
import 'localization/app_localizations.dart';
import 'widgets/app_version_widget.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => LocaleProvider()),
      ],
      child: const MyWindowsApp(),
    ),
  );
}

class MyWindowsApp extends StatelessWidget {
  const MyWindowsApp({super.key});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final localeProvider = Provider.of<LocaleProvider>(context);

    return MaterialApp(
      title: 'HM Device Errors - Windows',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      darkTheme: ThemeData.dark(useMaterial3: true),
      themeMode: themeProvider.isDarkMode ? ThemeMode.dark : ThemeMode.light,
      debugShowCheckedModeBanner: false,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: AppLocalizations.supportedLocales,
      locale: localeProvider.locale,
      // Set text direction based on locale
      builder: (context, child) {
        return Directionality(
          textDirection: localeProvider.isRTL ? TextDirection.rtl : TextDirection.ltr,
          child: child!,
        );
      },
      home: const WindowsHomeScreen(),
    );
  }
}

class WindowsHomeScreen extends StatelessWidget {
  const WindowsHomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    return Scaffold(
      appBar: AppBar(
        title: Text(isRTL ? 'أخطاء الأجهزة HM - نسخة ويندوز' : 'HM Device Errors - Windows Version'),
        actions: [
          // Language toggle
          IconButton(
            icon: const Icon(Icons.language),
            tooltip: isRTL ? 'تغيير اللغة' : 'Change Language',
            onPressed: () {
              localeProvider.toggleLocale();
            },
          ),
          // Theme toggle
          IconButton(
            icon: Icon(
              Provider.of<ThemeProvider>(context).isDarkMode
                  ? Icons.light_mode
                  : Icons.dark_mode,
            ),
            tooltip: isRTL ? 'تغيير المظهر' : 'Toggle Theme',
            onPressed: () {
              Provider.of<ThemeProvider>(context, listen: false).toggleTheme();
            },
          ),
        ],
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.computer,
                size: 80,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 24),
              Text(
                isRTL ? 'نسخة ويندوز من تطبيق أخطاء الأجهزة HM' : 'Windows Version of HM Device Errors App',
                style: Theme.of(context).textTheme.headlineMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                isRTL
                    ? 'هذه نسخة مبسطة من التطبيق للعمل على نظام ويندوز. للحصول على جميع الميزات، يرجى استخدام تطبيق الويب أو الجوال.'
                    : 'This is a simplified version of the app for Windows. For full features, please use the web or mobile app.',
                style: Theme.of(context).textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 48),
              ElevatedButton.icon(
                icon: const Icon(Icons.info_outline),
                label: Text(isRTL ? 'معلومات التطبيق' : 'App Information'),
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: Text(isRTL ? 'معلومات التطبيق' : 'App Information'),
                      content: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AppVersionWidget(
                            prefix: isRTL ? 'الإصدار: ' : 'Version: ',
                            isRTL: isRTL,
                          ),
                          const SizedBox(height: 8),
                          Text(isRTL ? 'المطور: محمد راضي' : 'Developer: Mohamed Rady'),
                          const SizedBox(height: 8),
                          Text(
                            isRTL
                                ? 'هذه نسخة ويندوز مبسطة من تطبيق أخطاء الأجهزة HM.'
                                : 'This is a simplified Windows version of the HM Device Errors app.',
                          ),
                        ],
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: Text(isRTL ? 'إغلاق' : 'Close'),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
