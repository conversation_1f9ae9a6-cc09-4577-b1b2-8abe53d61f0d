import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:flutter/foundation.dart';

class FileInfo {
  final String name;
  final String path;
  final int size;
  final DateTime lastModified;
  final String type;
  final String extension;

  FileInfo({
    required this.name,
    required this.path,
    required this.size,
    required this.lastModified,
    required this.type,
    required this.extension,
  });

  String get formattedSize {
    if (size < 1024) return '$size B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)} KB';
    if (size < 1024 * 1024 * 1024) return '${(size / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'path': path,
      'size': size,
      'lastModified': lastModified.toIso8601String(),
      'type': type,
      'extension': extension,
    };
  }

  factory FileInfo.fromMap(Map<String, dynamic> map) {
    return FileInfo(
      name: map['name'] ?? '',
      path: map['path'] ?? '',
      size: map['size'] ?? 0,
      lastModified: DateTime.tryParse(map['lastModified'] ?? '') ?? DateTime.now(),
      type: map['type'] ?? '',
      extension: map['extension'] ?? '',
    );
  }
}

class StorageInfo {
  final int totalSpace;
  final int usedSpace;
  final int freeSpace;
  final int appUsedSpace;

  StorageInfo({
    required this.totalSpace,
    required this.usedSpace,
    required this.freeSpace,
    required this.appUsedSpace,
  });

  double get usedPercentage => totalSpace > 0 ? (usedSpace / totalSpace) * 100 : 0;
  double get appUsedPercentage => totalSpace > 0 ? (appUsedSpace / totalSpace) * 100 : 0;

  String formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}

class FileManagerService {
  static const String _appFolderName = 'HM_Device_Errors';
  static const String _imagesFolderName = 'images';
  static const String _backupsFolderName = 'backups';
  static const String _exportsFolderName = 'exports';
  static const String _cacheFolderName = 'cache';

  // الحصول على مجلد التطبيق الرئيسي
  static Future<Directory> getAppDirectory() async {
    final documentsDir = await getApplicationDocumentsDirectory();
    final appDir = Directory(path.join(documentsDir.path, _appFolderName));
    if (!await appDir.exists()) {
      await appDir.create(recursive: true);
    }
    return appDir;
  }

  // الحصول على مجلد الصور
  static Future<Directory> getImagesDirectory() async {
    final appDir = await getAppDirectory();
    final imagesDir = Directory(path.join(appDir.path, _imagesFolderName));
    if (!await imagesDir.exists()) {
      await imagesDir.create(recursive: true);
    }
    return imagesDir;
  }

  // الحصول على مجلد النسخ الاحتياطية
  static Future<Directory> getBackupsDirectory() async {
    final appDir = await getAppDirectory();
    final backupsDir = Directory(path.join(appDir.path, _backupsFolderName));
    if (!await backupsDir.exists()) {
      await backupsDir.create(recursive: true);
    }
    return backupsDir;
  }

  // الحصول على مجلد التصدير
  static Future<Directory> getExportsDirectory() async {
    final appDir = await getAppDirectory();
    final exportsDir = Directory(path.join(appDir.path, _exportsFolderName));
    if (!await exportsDir.exists()) {
      await exportsDir.create(recursive: true);
    }
    return exportsDir;
  }

  // الحصول على مجلد الكاش
  static Future<Directory> getCacheDirectory() async {
    final appDir = await getAppDirectory();
    final cacheDir = Directory(path.join(appDir.path, _cacheFolderName));
    if (!await cacheDir.exists()) {
      await cacheDir.create(recursive: true);
    }
    return cacheDir;
  }

  // الحصول على جميع الملفات في مجلد
  static Future<List<FileInfo>> getFilesInDirectory(Directory directory) async {
    final files = <FileInfo>[];

    try {
      await for (final entity in directory.list()) {
        if (entity is File) {
          final stat = await entity.stat();
          final fileInfo = FileInfo(
            name: path.basename(entity.path),
            path: entity.path,
            size: stat.size,
            lastModified: stat.modified,
            type: _getFileType(entity.path),
            extension: path.extension(entity.path),
          );
          files.add(fileInfo);
        }
      }
    } catch (e) {
      debugPrint('Error reading directory: $e');
    }

    return files;
  }

  // تحديد نوع الملف
  static String _getFileType(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    switch (extension) {
      case '.jpg':
      case '.jpeg':
      case '.png':
      case '.gif':
      case '.webp':
        return 'image';
      case '.json':
        return 'backup';
      case '.xlsx':
      case '.xls':
        return 'excel';
      case '.pdf':
        return 'pdf';
      case '.txt':
        return 'text';
      default:
        return 'unknown';
    }
  }

  // الحصول على جميع ملفات التطبيق
  static Future<Map<String, List<FileInfo>>> getAllAppFiles() async {
    final result = <String, List<FileInfo>>{};

    try {
      final imagesDir = await getImagesDirectory();
      result['images'] = await getFilesInDirectory(imagesDir);

      final backupsDir = await getBackupsDirectory();
      result['backups'] = await getFilesInDirectory(backupsDir);

      final exportsDir = await getExportsDirectory();
      result['exports'] = await getFilesInDirectory(exportsDir);

      final cacheDir = await getCacheDirectory();
      result['cache'] = await getFilesInDirectory(cacheDir);
    } catch (e) {
      debugPrint('Error getting app files: $e');
    }

    return result;
  }

  // حساب المساحة المستخدمة
  static Future<StorageInfo> getStorageInfo() async {
    try {
      final appDir = await getAppDirectory();
      final appUsedSpace = await _calculateDirectorySize(appDir);

      // محاولة الحصول على معلومات التخزين العامة
      int totalSpace = 0;
      int freeSpace = 0;

      if (Platform.isAndroid || Platform.isIOS) {
        // للأجهزة المحمولة، نحتاج لمكتبة إضافية للحصول على معلومات التخزين
        // هنا نستخدم تقديرات
        totalSpace = 64 * 1024 * 1024 * 1024; // 64 GB تقديري
        freeSpace = totalSpace - (appUsedSpace * 10); // تقدير
      }

      return StorageInfo(
        totalSpace: totalSpace,
        usedSpace: totalSpace - freeSpace,
        freeSpace: freeSpace,
        appUsedSpace: appUsedSpace,
      );
    } catch (e) {
      debugPrint('Error getting storage info: $e');
      return StorageInfo(
        totalSpace: 0,
        usedSpace: 0,
        freeSpace: 0,
        appUsedSpace: 0,
      );
    }
  }

  // حساب حجم مجلد
  static Future<int> _calculateDirectorySize(Directory directory) async {
    int size = 0;
    try {
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          final stat = await entity.stat();
          size += stat.size;
        }
      }
    } catch (e) {
      debugPrint('Error calculating directory size: $e');
    }
    return size;
  }

  // حذف ملف
  static Future<bool> deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error deleting file: $e');
      return false;
    }
  }

  // حذف عدة ملفات
  static Future<int> deleteFiles(List<String> filePaths) async {
    int deletedCount = 0;
    for (final filePath in filePaths) {
      if (await deleteFile(filePath)) {
        deletedCount++;
      }
    }
    return deletedCount;
  }

  // تنظيف الكاش
  static Future<int> clearCache() async {
    try {
      final cacheDir = await getCacheDirectory();
      final files = await getFilesInDirectory(cacheDir);
      final filePaths = files.map((f) => f.path).toList();
      return await deleteFiles(filePaths);
    } catch (e) {
      debugPrint('Error clearing cache: $e');
      return 0;
    }
  }

  // حذف الملفات القديمة
  static Future<int> deleteOldFiles({int daysOld = 30}) async {
    try {
      final cutoffDate = DateTime.now().subtract(Duration(days: daysOld));
      final allFiles = await getAllAppFiles();
      final oldFiles = <String>[];

      for (final fileList in allFiles.values) {
        for (final file in fileList) {
          if (file.lastModified.isBefore(cutoffDate)) {
            oldFiles.add(file.path);
          }
        }
      }

      return await deleteFiles(oldFiles);
    } catch (e) {
      debugPrint('Error deleting old files: $e');
      return 0;
    }
  }

  // نسخ ملف
  static Future<bool> copyFile(String sourcePath, String destinationPath) async {
    try {
      final sourceFile = File(sourcePath);

      if (await sourceFile.exists()) {
        await sourceFile.copy(destinationPath);
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error copying file: $e');
      return false;
    }
  }

  // نقل ملف
  static Future<bool> moveFile(String sourcePath, String destinationPath) async {
    try {
      if (await copyFile(sourcePath, destinationPath)) {
        return await deleteFile(sourcePath);
      }
      return false;
    } catch (e) {
      debugPrint('Error moving file: $e');
      return false;
    }
  }

  // حفظ ملف من البيانات
  static Future<String?> saveFile(String fileName, Uint8List data, {String? subfolder}) async {
    try {
      final appDir = await getAppDirectory();
      final targetDir = subfolder != null
          ? Directory(path.join(appDir.path, subfolder))
          : appDir;

      if (!await targetDir.exists()) {
        await targetDir.create(recursive: true);
      }

      final filePath = path.join(targetDir.path, fileName);
      final file = File(filePath);
      await file.writeAsBytes(data);

      return filePath;
    } catch (e) {
      debugPrint('Error saving file: $e');
      return null;
    }
  }

  // قراءة ملف
  static Future<Uint8List?> readFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.readAsBytes();
      }
      return null;
    } catch (e) {
      debugPrint('Error reading file: $e');
      return null;
    }
  }
}
