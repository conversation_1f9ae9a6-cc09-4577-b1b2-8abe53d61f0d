import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart' show debugPrint, defaultTargetPlatform, TargetPlatform;
import '../firebase_options.dart';
import 'auth_service.dart';

class FirebaseService {
  static bool _isInitialized = false;

  static Future<void> initialize() async {
    try {
      // تحقق من أن Firebase لم يتم تهيئته مسبقاً
      if (_isInitialized) {
        debugPrint('Firebase already initialized, skipping...');
        return;
      }

      // Add special handling for Windows platform
      if (defaultTargetPlatform == TargetPlatform.windows) {
        // Set SSL/TLS configuration for Windows
        debugPrint('Initializing Firebase for Windows platform');
      }

      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      _isInitialized = true;
      debugPrint('Firebase initialized successfully');

      // Check and create default admin user if needed
      try {
        final authService = AuthService();
        await authService.checkAndCreateDefaultAdmin();
      } catch (authError) {
        // Don't let auth initialization errors prevent the app from starting
        debugPrint('Warning: Error in admin user initialization: $authError');
      }
    } catch (e) {
      if (e.toString().contains('duplicate-app')) {
        debugPrint('Firebase app already exists, continuing...');
        _isInitialized = true;
        return;
      }
      debugPrint('Error initializing Firebase: $e');
      // Don't rethrow to allow the app to continue even if Firebase init fails
      debugPrint('Continuing without Firebase initialization');
    }
  }

  /// Check if Firebase is initialized
  static bool get isInitialized => _isInitialized;
}
