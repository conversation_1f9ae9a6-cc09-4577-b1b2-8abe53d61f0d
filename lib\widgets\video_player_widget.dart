import 'dart:io';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import '../models/attachment_model.dart';
import '../services/attachment_cache_service.dart';

/// Widget لتشغيل الفيديوهات مع دعم الكاش والتحميل من السحابة
class VideoPlayerWidget extends StatefulWidget {
  final AttachmentModel attachment;
  final bool autoPlay;
  final bool showControls;
  final bool looping;
  final double? aspectRatio;

  const VideoPlayerWidget({
    super.key,
    required this.attachment,
    this.autoPlay = false,
    this.showControls = true,
    this.looping = false,
    this.aspectRatio,
  });

  @override
  State<VideoPlayerWidget> createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  VideoPlayerController? _videoController;
  ChewieController? _chewieController;
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  double _downloadProgress = 0.0;
  bool _isDownloading = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  @override
  void dispose() {
    _chewieController?.dispose();
    _videoController?.dispose();
    super.dispose();
  }

  Future<void> _initializeVideo() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
        _errorMessage = '';
      });

      final cacheService = AttachmentCacheService.instance;

      // التحقق من وجود الفيديو في الكاش أولاً
      File? cachedFile = await cacheService.getCachedFile(widget.attachment);

      if (cachedFile != null && await cachedFile.exists()) {
        // تشغيل الفيديو من الكاش
        await _initializeVideoFromFile(cachedFile);
      } else {
        // تحميل الفيديو وحفظه في الكاش
        await _downloadAndCacheVideo();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'خطأ في تحميل الفيديو: $e';
      });
    }
  }

  Future<void> _downloadAndCacheVideo() async {
    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
    });

    try {
      final cacheService = AttachmentCacheService.instance;

      final cachedFile = await cacheService.cacheFile(
        widget.attachment,
        onProgress: (progress) {
          setState(() {
            _downloadProgress = progress;
          });
        },
      );

      if (cachedFile != null && await cachedFile.exists()) {
        await _initializeVideoFromFile(cachedFile);
      } else {
        // إذا فشل التحميل، جرب التشغيل المباشر من الرابط
        await _initializeVideoFromUrl();
      }
    } catch (e) {
      // في حالة فشل التحميل، جرب التشغيل المباشر
      try {
        await _initializeVideoFromUrl();
      } catch (urlError) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = 'فشل في تحميل الفيديو: $e';
        });
      }
    } finally {
      setState(() {
        _isDownloading = false;
      });
    }
  }

  Future<void> _initializeVideoFromFile(File file) async {
    _videoController = VideoPlayerController.file(file);
    await _setupVideoController();
  }

  Future<void> _initializeVideoFromUrl() async {
    _videoController = VideoPlayerController.networkUrl(Uri.parse(widget.attachment.url));
    await _setupVideoController();
  }

  Future<void> _setupVideoController() async {
    if (_videoController == null) return;

    await _videoController!.initialize();

    _chewieController = ChewieController(
      videoPlayerController: _videoController!,
      autoPlay: widget.autoPlay,
      looping: widget.looping,
      showControls: widget.showControls,
      aspectRatio: widget.aspectRatio ?? _videoController!.value.aspectRatio,
      placeholder: Container(
        color: Colors.black,
        child: const Center(
          child: Icon(
            Icons.play_circle_outline,
            color: Colors.white,
            size: 64,
          ),
        ),
      ),
      errorBuilder: (context, errorMessage) {
        return Container(
          color: Colors.black,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 64,
                ),
                const SizedBox(height: 16),
                Text(
                  'خطأ في تشغيل الفيديو',
                  style: const TextStyle(color: Colors.white),
                ),
                const SizedBox(height: 8),
                Text(
                  errorMessage,
                  style: const TextStyle(color: Colors.white70, fontSize: 12),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading || _isDownloading) {
      return Container(
        color: Colors.black,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (_isDownloading) ...[
                const Icon(
                  Icons.download,
                  color: Colors.white,
                  size: 48,
                ),
                const SizedBox(height: 16),
                Text(
                  'جاري تحميل الفيديو...',
                  style: const TextStyle(color: Colors.white, fontSize: 16),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  width: 200,
                  child: LinearProgressIndicator(
                    value: _downloadProgress,
                    backgroundColor: Colors.white30,
                    valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${(_downloadProgress * 100).toInt()}%',
                  style: const TextStyle(color: Colors.white70),
                ),
              ] else ...[
                const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
                const SizedBox(height: 16),
                const Text(
                  'جاري تحضير الفيديو...',
                  style: TextStyle(color: Colors.white),
                ),
              ],
            ],
          ),
        ),
      );
    }

    if (_hasError) {
      return Container(
        color: Colors.black,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 64,
              ),
              const SizedBox(height: 16),
              Text(
                _errorMessage,
                style: const TextStyle(color: Colors.white),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _initializeVideo,
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }

    if (_chewieController == null) {
      return Container(
        color: Colors.black,
        child: const Center(
          child: Text(
            'فشل في تحضير مشغل الفيديو',
            style: TextStyle(color: Colors.white),
          ),
        ),
      );
    }

    return Chewie(controller: _chewieController!);
  }
}

/// Dialog لعرض الفيديو في وضع ملء الشاشة
class VideoPlayerDialog extends StatelessWidget {
  final AttachmentModel attachment;

  const VideoPlayerDialog({
    super.key,
    required this.attachment,
  });

  static void show(BuildContext context, AttachmentModel attachment) {
    showDialog(
      context: context,
      barrierColor: Colors.black,
      builder: (context) => VideoPlayerDialog(attachment: attachment),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog.fullscreen(
      backgroundColor: Colors.black,
      child: Stack(
        children: [
          Center(
            child: VideoPlayerWidget(
              attachment: attachment,
              autoPlay: true,
              showControls: true,
            ),
          ),
          Positioned(
            top: 40,
            right: 16,
            child: IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(
                Icons.close,
                color: Colors.white,
                size: 32,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
