@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🚀 بدء تشغيل اختبارات إعدادات الكاش المتقدمة...
echo ==================================================

REM التحقق من وجود Flutter
where flutter >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Flutter غير مثبت. يرجى تثبيت Flutter أولاً.
    pause
    exit /b 1
)

echo ✅ Flutter متوفر

REM التحقق من وجود المشروع
if not exist "pubspec.yaml" (
    echo ❌ لم يتم العثور على ملف pubspec.yaml. تأكد من تشغيل السكريبت من مجلد المشروع.
    pause
    exit /b 1
)

echo ✅ ملف المشروع موجود

REM تحديث التبعيات
echo 📦 تحديث التبعيات...
flutter pub get

if %errorlevel% neq 0 (
    echo ❌ فشل في تحديث التبعيات
    pause
    exit /b 1
)

echo ✅ تم تحديث التبعيات بنجاح

REM تشغيل اختبارات إعدادات الكاش
echo 🧪 تشغيل اختبارات إعدادات الكاش...
flutter test test/screens/cache_settings_screen_test.dart --reporter=expanded

if %errorlevel% equ 0 (
    echo ✅ جميع اختبارات إعدادات الكاش نجحت!
) else (
    echo ❌ بعض اختبارات إعدادات الكاش فشلت
    set TEST_FAILED=1
)

REM تشغيل اختبارات نموذج الإعدادات
echo 🧪 تشغيل اختبارات نموذج الإعدادات...
flutter test test/models/ --reporter=expanded

if %errorlevel% equ 0 (
    echo ✅ جميع اختبارات النموذج نجحت!
) else (
    echo ❌ بعض اختبارات النموذج فشلت
    set TEST_FAILED=1
)

REM تشغيل تحليل الكود
echo 🔍 تشغيل تحليل الكود...
flutter analyze lib/screens/settings/cache_settings_screen.dart
flutter analyze lib/models/cache_settings_model.dart

if %errorlevel% equ 0 (
    echo ✅ تحليل الكود نجح!
) else (
    echo ⚠️  توجد تحذيرات في تحليل الكود
)

REM تشغيل فحص التنسيق
echo 🎨 فحص تنسيق الكود...
flutter format --dry-run lib/screens/settings/cache_settings_screen.dart
flutter format --dry-run lib/models/cache_settings_model.dart

echo.
echo ==================================================
echo 🎉 انتهى تشغيل جميع الاختبارات!
echo.
echo 📊 ملخص النتائج:
echo    ✅ اختبارات الوحدة: مكتملة
echo    ✅ اختبارات الواجهة: مكتملة
echo    ✅ تحليل الكود: مكتمل
echo    ✅ فحص التنسيق: مكتمل
echo.

if defined TEST_FAILED (
    echo ⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.
) else (
    echo 🎉 جميع الاختبارات نجحت!
)

echo.
echo 🚀 يمكنك الآن تشغيل التطبيق التجريبي:
echo    flutter run example/cache_settings_demo.dart
echo.
echo 📖 للمزيد من المعلومات، راجع:
echo    - ADVANCED_CACHE_SETTINGS_README.md
echo    - FUTURE_IMPROVEMENTS.md
echo ==================================================

pause
