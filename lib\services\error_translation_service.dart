import 'package:flutter/foundation.dart';

/// خدمة ترجمة أخطاء Firebase والأخطاء العامة إلى اللغة العربية
class ErrorTranslationService {
  
  /// ترجمة رسائل الخطأ إلى العربية
  static String translateError(String error, {bool isRTL = true}) {
    if (!isRTL) return error;

    // إزالة البادئات الشائعة
    String cleanError = _cleanErrorMessage(error);
    
    // التحقق من الرسائل المترجمة مسبقاً
    if (_isAlreadyTranslated(cleanError)) {
      return cleanError;
    }

    // ترجمة أخطاء Firebase Auth
    String? authError = _translateFirebaseAuthError(cleanError);
    if (authError != null) return authError;

    // ترجمة أخطاء Firestore
    String? firestoreError = _translateFirestoreError(cleanError);
    if (firestoreError != null) return firestoreError;

    // ترجمة أخطاء الشبكة
    String? networkError = _translateNetworkError(cleanError);
    if (networkError != null) return networkError;

    // ترجمة أخطاء التخزين
    String? storageError = _translateStorageError(cleanError);
    if (storageError != null) return storageError;

    // ترجمة أخطاء عامة
    String? generalError = _translateGeneralError(cleanError);
    if (generalError != null) return generalError;

    // إذا لم نجد ترجمة محددة، نعيد الرسالة الأصلية
    return cleanError;
  }

  /// تنظيف رسالة الخطأ من البادئات غير المرغوب فيها
  static String _cleanErrorMessage(String error) {
    String cleanError = error.trim();
    
    // إزالة البادئات الشائعة
    final prefixes = [
      'Exception: ',
      'FirebaseException: ',
      'PlatformException: ',
      'Error: ',
      'firebase_auth/',
      'cloud_firestore/',
      'firebase_storage/',
    ];

    for (final prefix in prefixes) {
      if (cleanError.startsWith(prefix)) {
        cleanError = cleanError.substring(prefix.length);
      }
    }

    return cleanError.trim();
  }

  /// التحقق من كون الرسالة مترجمة مسبقاً
  static bool _isAlreadyTranslated(String error) {
    final arabicIndicators = [
      'المستخدم غير موجود',
      'تم إيقاف حسابك',
      'انتهت صلاحية حسابك',
      'انتهت مهلة الاتصال',
      'لا يمكن التحقق من صحة المستخدم',
      'كلمة المرور الحالية غير صحيحة',
      'كلمة المرور الجديدة ضعيفة',
      'يجب تسجيل الدخول مرة أخرى',
      'لا يوجد مستخدم مسجل دخول',
      'انتهت صلاحية جلستك',
      'تحقق من اتصال الإنترنت',
      'خطأ في',
      'فشل في',
      'تم',
      'لا يمكن',
    ];

    return arabicIndicators.any((indicator) => error.contains(indicator));
  }

  /// ترجمة أخطاء Firebase Authentication
  static String? _translateFirebaseAuthError(String error) {
    final errorLower = error.toLowerCase();

    // أخطاء تسجيل الدخول
    if (errorLower.contains('invalid-email') || 
        errorLower.contains('invalid email')) {
      return 'البريد الإلكتروني غير صحيح';
    }
    
    if (errorLower.contains('invalid-credential') || 
        errorLower.contains('invalid credential') ||
        errorLower.contains('invalid email or password')) {
      return 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
    }
    
    if (errorLower.contains('user-not-found') || 
        errorLower.contains('user not found')) {
      return 'المستخدم غير موجود';
    }
    
    if (errorLower.contains('wrong-password') || 
        errorLower.contains('wrong password')) {
      return 'كلمة المرور خاطئة';
    }

    // أخطاء التسجيل
    if (errorLower.contains('email-already-in-use') || 
        errorLower.contains('email already in use')) {
      return 'البريد الإلكتروني مستخدم بالفعل';
    }
    
    if (errorLower.contains('weak-password') || 
        errorLower.contains('weak password')) {
      return 'كلمة المرور ضعيفة جداً. يجب أن تحتوي على 6 أحرف على الأقل';
    }

    // أخطاء الحساب
    if (errorLower.contains('user-disabled') || 
        errorLower.contains('user disabled')) {
      return 'تم إيقاف هذا الحساب. تواصل مع المدير';
    }
    
    if (errorLower.contains('account-exists-with-different-credential')) {
      return 'يوجد حساب بهذا البريد الإلكتروني بطريقة تسجيل دخول مختلفة';
    }

    // أخطاء إعادة المصادقة
    if (errorLower.contains('requires-recent-login') || 
        errorLower.contains('requires recent login')) {
      return 'يجب تسجيل الدخول مرة أخرى لإتمام هذه العملية';
    }

    // أخطاء كثرة المحاولات
    if (errorLower.contains('too-many-requests') || 
        errorLower.contains('too many requests')) {
      return 'محاولات كثيرة جداً. حاول مرة أخرى بعد قليل';
    }

    // أخطاء العمليات
    if (errorLower.contains('operation-not-allowed') || 
        errorLower.contains('operation not allowed')) {
      return 'هذه العملية غير مسموحة';
    }

    return null;
  }

  /// ترجمة أخطاء Firestore
  static String? _translateFirestoreError(String error) {
    final errorLower = error.toLowerCase();

    if (errorLower.contains('permission-denied') || 
        errorLower.contains('permission denied')) {
      return 'ليس لديك صلاحية للوصول إلى هذه البيانات';
    }
    
    if (errorLower.contains('not-found') || 
        errorLower.contains('document not found')) {
      return 'البيانات المطلوبة غير موجودة';
    }
    
    if (errorLower.contains('already-exists') || 
        errorLower.contains('already exists')) {
      return 'البيانات موجودة بالفعل';
    }
    
    if (errorLower.contains('quota-exceeded') || 
        errorLower.contains('quota exceeded')) {
      return 'تم تجاوز الحد المسموح للاستخدام';
    }
    
    if (errorLower.contains('unavailable') || 
        errorLower.contains('service unavailable')) {
      return 'الخدمة غير متاحة حالياً. حاول مرة أخرى';
    }

    return null;
  }

  /// ترجمة أخطاء الشبكة
  static String? _translateNetworkError(String error) {
    final errorLower = error.toLowerCase();

    if (errorLower.contains('network') || 
        errorLower.contains('connection') ||
        errorLower.contains('internet')) {
      return 'خطأ في الاتصال بالشبكة. تحقق من اتصال الإنترنت';
    }
    
    if (errorLower.contains('timeout') || 
        errorLower.contains('timed out')) {
      return 'انتهت مهلة الاتصال. حاول مرة أخرى';
    }
    
    if (errorLower.contains('offline') || 
        errorLower.contains('no internet')) {
      return 'لا يوجد اتصال بالإنترنت';
    }
    
    if (errorLower.contains('server') && errorLower.contains('error')) {
      return 'خطأ في الخادم. حاول مرة أخرى لاحقاً';
    }

    return null;
  }

  /// ترجمة أخطاء التخزين
  static String? _translateStorageError(String error) {
    final errorLower = error.toLowerCase();

    if (errorLower.contains('storage') && errorLower.contains('permission')) {
      return 'ليس لديك صلاحية لرفع الملفات';
    }
    
    if (errorLower.contains('file') && errorLower.contains('too large')) {
      return 'حجم الملف كبير جداً';
    }
    
    if (errorLower.contains('invalid') && errorLower.contains('file')) {
      return 'نوع الملف غير مدعوم';
    }
    
    if (errorLower.contains('upload') && errorLower.contains('failed')) {
      return 'فشل في رفع الملف. حاول مرة أخرى';
    }

    return null;
  }

  /// ترجمة الأخطاء العامة
  static String? _translateGeneralError(String error) {
    final errorLower = error.toLowerCase();

    // أخطاء كلمة المرور
    if (errorLower.contains('password')) {
      return 'خطأ متعلق بكلمة المرور';
    }
    
    // أخطاء البريد الإلكتروني
    if (errorLower.contains('email')) {
      return 'خطأ متعلق بالبريد الإلكتروني';
    }
    
    // أخطاء الصلاحيات
    if (errorLower.contains('permission') || 
        errorLower.contains('unauthorized')) {
      return 'ليس لديك صلاحية لتنفيذ هذه العملية';
    }
    
    // أخطاء التحقق
    if (errorLower.contains('validation') || 
        errorLower.contains('invalid')) {
      return 'البيانات المدخلة غير صحيحة';
    }
    
    // أخطاء عامة
    if (errorLower.contains('failed') || 
        errorLower.contains('error')) {
      return 'حدث خطأ غير متوقع. حاول مرة أخرى';
    }

    return null;
  }

  /// ترجمة أخطاء محددة للتطبيق
  static String translateAppSpecificError(String error, {bool isRTL = true}) {
    if (!isRTL) return error;

    final errorLower = error.toLowerCase();

    // أخطاء إدارة المستخدمين
    if (errorLower.contains('user management')) {
      return 'خطأ في إدارة المستخدمين';
    }
    
    // أخطاء الأعطال
    if (errorLower.contains('device error') || errorLower.contains('fault')) {
      return 'خطأ في إدارة أعطال الأجهزة';
    }
    
    // أخطاء الفئات
    if (errorLower.contains('category')) {
      return 'خطأ في إدارة الفئات';
    }
    
    // أخطاء الشركات المصنعة
    if (errorLower.contains('manufacturer')) {
      return 'خطأ في إدارة الشركات المصنعة';
    }

    return translateError(error, isRTL: isRTL);
  }

  /// طباعة معلومات الخطأ للتطوير
  static void logError(String originalError, String translatedError) {
    if (kDebugMode) {
      debugPrint('=== Error Translation ===');
      debugPrint('Original: $originalError');
      debugPrint('Translated: $translatedError');
      debugPrint('========================');
    }
  }
}
