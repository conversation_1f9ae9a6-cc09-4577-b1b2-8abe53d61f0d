import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/locale_provider.dart';


class DataManagementScreen extends StatefulWidget {
  const DataManagementScreen({super.key});

  @override
  State<DataManagementScreen> createState() => _DataManagementScreenState();
}

class _DataManagementScreenState extends State<DataManagementScreen> {
  final bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    // Check if user is admin
    if (!authProvider.isAdmin) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Unauthorized / غير مصرح'),
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error,
                size: 80,
                color: Colors.red,
              ),
              SizedBox(height: 16),
              Text(
                'You are not authorized to access this page\nغير مصرح لك بالوصول إلى هذه الصفحة',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 18),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isRTL ? 'إدارة البيانات' : 'Data Management',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: false,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    isRTL ? 'إدارة البيانات والنسخ الاحتياطي' : 'Data Management & Backup',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    isRTL
                        ? 'استيراد وتصدير البيانات وإنشاء نسخ احتياطية'
                        : 'Import, export data and create backups',
                    style: TextStyle(
                      fontSize: 16,
                      color: Theme.of(context).colorScheme.onSurface.withAlpha(178),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Export Section
                  _buildSectionCard(
                    title: isRTL ? 'تصدير البيانات' : 'Export Data',
                    icon: Icons.file_download,
                    children: [
                      _buildActionTile(
                        icon: Icons.bug_report,
                        title: isRTL ? 'تصدير الأعطال' : 'Export Errors',
                        subtitle: isRTL ? 'تصدير جميع الأعطال إلى ملف Excel' : 'Export all errors to Excel file',
                        onTap: _exportErrors,
                      ),
                      _buildActionTile(
                        icon: Icons.category,
                        title: isRTL ? 'تصدير الفئات' : 'Export Categories',
                        subtitle: isRTL ? 'تصدير جميع الفئات إلى ملف Excel' : 'Export all categories to Excel file',
                        onTap: _exportCategories,
                      ),
                      _buildActionTile(
                        icon: Icons.business,
                        title: isRTL ? 'تصدير الشركات والموديلات' : 'Export Manufacturers & Models',
                        subtitle: isRTL ? 'تصدير الشركات والموديلات إلى ملف Excel' : 'Export manufacturers and models to Excel file',
                        onTap: _exportManufacturers,
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Import Section
                  _buildSectionCard(
                    title: isRTL ? 'استيراد البيانات' : 'Import Data',
                    icon: Icons.file_upload,
                    children: [
                      _buildActionTile(
                        icon: Icons.bug_report,
                        title: isRTL ? 'استيراد الأعطال' : 'Import Errors',
                        subtitle: isRTL ? 'استيراد الأعطال من ملف Excel' : 'Import errors from Excel file',
                        onTap: _importErrors,
                      ),
                      _buildActionTile(
                        icon: Icons.category,
                        title: isRTL ? 'استيراد الفئات' : 'Import Categories',
                        subtitle: isRTL ? 'استيراد الفئات من ملف Excel' : 'Import categories from Excel file',
                        onTap: _importCategories,
                      ),
                      _buildActionTile(
                        icon: Icons.business,
                        title: isRTL ? 'استيراد الشركات والموديلات' : 'Import Manufacturers & Models',
                        subtitle: isRTL ? 'استيراد الشركات والموديلات من ملف Excel' : 'Import manufacturers and models from Excel file',
                        onTap: _importManufacturers,
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Backup Section
                  _buildSectionCard(
                    title: isRTL ? 'النسخ الاحتياطي' : 'Backup & Restore',
                    icon: Icons.backup,
                    children: [
                      _buildActionTile(
                        icon: Icons.cloud_upload,
                        title: isRTL ? 'إنشاء نسخة احتياطية' : 'Create Backup',
                        subtitle: isRTL ? 'إنشاء نسخة احتياطية كاملة من قاعدة البيانات' : 'Create a complete database backup',
                        onTap: _createBackup,
                      ),
                      _buildActionTile(
                        icon: Icons.cloud_download,
                        title: isRTL ? 'استعادة النسخة الاحتياطية' : 'Restore Backup',
                        subtitle: isRTL ? 'استعادة البيانات من نسخة احتياطية' : 'Restore data from backup file',
                        onTap: _restoreBackup,
                      ),
                    ],
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isDark
              ? Theme.of(context).colorScheme.outline.withAlpha(120)
              : const Color(0xFF2563EB).withAlpha(100), // حدود زرقاء فاتحة
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
              ? Colors.black.withAlpha(120)
              : const Color(0xFF2563EB).withAlpha(40), // ظل أزرق فاتح
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: isDark
              ? Colors.black.withAlpha(60)
              : const Color(0xFF2563EB).withAlpha(20), // ظل ثانوي
            blurRadius: 6,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Card(
        elevation: 0,
        margin: EdgeInsets.zero,
        color: isDark
            ? Theme.of(context).colorScheme.surface
            : const Color(0xFFF8FAFF), // خلفية زرقاء فاتحة جداً
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isDark ? [
                Theme.of(context).colorScheme.surface,
                Theme.of(context).colorScheme.surface.withAlpha(230),
              ] : [
                const Color(0xFFF8FAFF), // زرقاء فاتحة جداً
                const Color(0xFFF1F5FF), // زرقاء فاتحة أكثر قليلاً
                const Color(0xFFEBF2FF), // زرقاء فاتحة أكثر
              ],
              stops: isDark ? null : [0.0, 0.5, 1.0],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary.withAlpha(25),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.primary.withAlpha(100),
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        icon,
                        color: Theme.of(context).colorScheme.primary,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: isDark
                              ? Colors.white
                              : const Color(0xFF1E40AF), // لون أزرق داكن للنص
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                ...children,
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withAlpha(25),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: Theme.of(context).colorScheme.primary),
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.arrow_forward_ios),
      onTap: onTap,
    );
  }

  // Export methods
  Future<void> _exportErrors() async {
    // Implementation will be added
    _showNotImplementedDialog('Export Errors');
  }

  Future<void> _exportCategories() async {
    // Implementation will be added
    _showNotImplementedDialog('Export Categories');
  }

  Future<void> _exportManufacturers() async {
    // Implementation will be added
    _showNotImplementedDialog('Export Manufacturers');
  }

  // Import methods
  Future<void> _importErrors() async {
    // Implementation will be added
    _showNotImplementedDialog('Import Errors');
  }

  Future<void> _importCategories() async {
    // Implementation will be added
    _showNotImplementedDialog('Import Categories');
  }

  Future<void> _importManufacturers() async {
    // Implementation will be added
    _showNotImplementedDialog('Import Manufacturers');
  }

  // Backup methods
  Future<void> _createBackup() async {
    // Implementation will be added
    _showNotImplementedDialog('Create Backup');
  }

  Future<void> _restoreBackup() async {
    // Implementation will be added
    _showNotImplementedDialog('Restore Backup');
  }

  void _showNotImplementedDialog(String feature) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'قريباً' : 'Coming Soon'),
        content: Text(
          isRTL
              ? 'ستتم إضافة هذه الميزة قريباً: $feature'
              : 'This feature will be added soon: $feature',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'موافق' : 'OK'),
          ),
        ],
      ),
    );
  }
}
