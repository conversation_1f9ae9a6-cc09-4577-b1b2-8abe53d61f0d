import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../providers/locale_provider.dart';
import '../../providers/auth_provider.dart';
import '../../services/remote_config_service.dart';


class GoogleDriveSetupScreen extends StatefulWidget {
  const GoogleDriveSetupScreen({super.key});

  @override
  State<GoogleDriveSetupScreen> createState() => _GoogleDriveSetupScreenState();
}

class _GoogleDriveSetupScreenState extends State<GoogleDriveSetupScreen> {
  bool _isLoading = false;
  Map<String, dynamic>? _debugInfo;

  @override
  void initState() {
    super.initState();
    _loadDebugInfo();
  }

  void _loadDebugInfo() {
    setState(() {
      _debugInfo = RemoteConfigService.getGoogleDriveDebugInfo();
    });
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final isRTL = localeProvider.isRTL;

    // Check admin access
    if (authProvider.user?.role.toString().split('.').last != 'admin') {
      return Scaffold(
        appBar: AppBar(
          title: Text(
            isRTL ? 'غير مصرح' : 'Unauthorized',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
          elevation: 4,
          iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
          centerTitle: true,
        ),
        body: Center(
          child: Text(
            isRTL ? 'ليس لديك صلاحية للوصول لهذه الصفحة' : 'You don\'t have permission to access this page',
            style: const TextStyle(fontSize: 18),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isRTL ? 'إعداد Google Drive' : 'Google Drive Setup',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: false,
        actions: [
          IconButton(
            onPressed: _isLoading ? null : _refreshConfig,
            icon: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                )
              : const Icon(Icons.refresh),
            tooltip: isRTL ? 'تحديث' : 'Refresh',
          ),
        ],
      ),
      body: _isLoading
        ? const Center(child: CircularProgressIndicator())
        : ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Current Status Card
              _buildStatusCard(context, isRTL),
              const SizedBox(height: 16),

              // Setup Instructions Card
              _buildInstructionsCard(context, isRTL),
              const SizedBox(height: 16),

              // Required Keys Card
              _buildRequiredKeysCard(context, isRTL),
              const SizedBox(height: 16),

              // Debug Information Card
              if (_debugInfo != null) _buildDebugCard(context, isRTL),
              const SizedBox(height: 16),

              // Actions Card
              _buildActionsCard(context, isRTL),
            ],
          ),
    );
  }

  Widget _buildStatusCard(BuildContext context, bool isRTL) {
    final isEnabled = RemoteConfigService.isGoogleDriveEnabled();
    final useRemoteConfig = RemoteConfigService.useRemoteConfigAsPrimary();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isEnabled ? Icons.cloud_done : Icons.cloud_off,
                  color: isEnabled ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'حالة Google Drive' : 'Google Drive Status',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatusRow(
              isRTL ? 'مفعل' : 'Enabled',
              isEnabled,
              isRTL,
            ),
            _buildStatusRow(
              isRTL ? 'Remote Config كمصدر أساسي' : 'Remote Config Primary',
              useRemoteConfig,
              isRTL,
            ),
            _buildStatusRow(
              isRTL ? 'التكوين مكتمل' : 'Configuration Complete',
              _isConfigurationComplete(),
              isRTL,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionsCard(BuildContext context, bool isRTL) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.help_outline, color: Colors.blue[600]),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'خطوات الإعداد' : 'Setup Instructions',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInstructionStep(
              '1',
              isRTL ? 'إنشاء Service Account في Google Cloud Console' : 'Create Service Account in Google Cloud Console',
              isRTL,
            ),
            _buildInstructionStep(
              '2',
              isRTL ? 'تفعيل Google Drive API' : 'Enable Google Drive API',
              isRTL,
            ),
            _buildInstructionStep(
              '3',
              isRTL ? 'إنشاء مجلد في Google Drive ومشاركته مع Service Account' : 'Create folder in Google Drive and share with Service Account',
              isRTL,
            ),
            _buildInstructionStep(
              '4',
              isRTL ? 'إضافة المفاتيح في Firebase Remote Config' : 'Add keys to Firebase Remote Config',
              isRTL,
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showDetailedInstructions(context, isRTL),
                icon: const Icon(Icons.book),
                label: Text(isRTL ? 'عرض التعليمات المفصلة' : 'Show Detailed Instructions'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRequiredKeysCard(BuildContext context, bool isRTL) {
    final requiredKeys = [
      'google_drive_enabled',
      'google_drive_client_id',
      'google_drive_client_email',
      'google_drive_project_id',
      'google_drive_private_key',
      'google_drive_parent_folder_id',
    ];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.key, color: Colors.orange[600]),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'المفاتيح المطلوبة' : 'Required Keys',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...requiredKeys.map((key) => _buildKeyRow(key, isRTL)),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => _copyKeysTemplate(isRTL),
                icon: const Icon(Icons.copy),
                label: Text(isRTL ? 'نسخ قالب المفاتيح' : 'Copy Keys Template'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDebugCard(BuildContext context, bool isRTL) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.bug_report, color: Colors.purple[600]),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'معلومات التشخيص' : 'Debug Information',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._debugInfo!.entries.map((entry) =>
              _buildDebugRow(entry.key, entry.value.toString(), isRTL)
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionsCard(BuildContext context, bool isRTL) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.settings, color: Colors.green[600]),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'الإجراءات' : 'Actions',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _refreshConfig,
                icon: const Icon(Icons.refresh),
                label: Text(isRTL ? 'تحديث التكوين' : 'Refresh Configuration'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => _testConnection(isRTL),
                icon: const Icon(Icons.wifi_tethering),
                label: Text(isRTL ? 'اختبار الاتصال' : 'Test Connection'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, bool value, bool isRTL) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Icon(
            value ? Icons.check_circle : Icons.cancel,
            color: value ? Colors.green : Colors.red,
            size: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildInstructionStep(String number, String text, bool isRTL) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                number,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKeyRow(String key, bool isRTL) {
    final hasValue = _debugInfo?[key.replaceAll('google_drive_', '')] != 'empty';

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              key,
              style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
            ),
          ),
          Icon(
            hasValue ? Icons.check_circle : Icons.error,
            color: hasValue ? Colors.green : Colors.red,
            size: 16,
          ),
        ],
      ),
    );
  }

  Widget _buildDebugRow(String key, String value, bool isRTL) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            key,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Text(
            value,
            style: TextStyle(
              color: value == 'true' ? Colors.green :
                     value == 'false' ? Colors.red :
                     value == 'empty' ? Colors.red : Colors.blue,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  bool _isConfigurationComplete() {
    if (_debugInfo == null) return false;

    final requiredFields = ['client_id', 'client_email', 'project_id', 'private_key'];
    return _debugInfo!['enabled'] == true &&
           requiredFields.every((field) => _debugInfo![field] != 'empty');
  }

  Future<void> _refreshConfig() async {
    setState(() => _isLoading = true);
    try {
      await RemoteConfigService.fetchAndActivate();
      _loadDebugInfo();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              Provider.of<LocaleProvider>(context, listen: false).isRTL
                ? 'تم تحديث التكوين بنجاح'
                : 'Configuration refreshed successfully',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _copyKeysTemplate(bool isRTL) {
    const template = '''
{
  "google_drive_enabled": true,
  "google_drive_client_id": "YOUR_CLIENT_ID",
  "google_drive_client_secret": "YOUR_CLIENT_SECRET",
  "google_drive_project_id": "YOUR_PROJECT_ID",
  "google_drive_private_key_id": "YOUR_PRIVATE_KEY_ID",
  "google_drive_private_key": "YOUR_PRIVATE_KEY",
  "google_drive_client_email": "YOUR_SERVICE_ACCOUNT_EMAIL",
  "google_drive_parent_folder_id": "YOUR_FOLDER_ID",
  "google_drive_config_source": "remote",
  "google_drive_use_remote_config": true
}
''';

    Clipboard.setData(const ClipboardData(text: template));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isRTL ? 'تم نسخ قالب المفاتيح' : 'Keys template copied to clipboard',
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _testConnection(bool isRTL) {
    final config = RemoteConfigService.getGoogleDriveConfig();

    if (config == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isRTL ? 'التكوين غير مكتمل' : 'Configuration incomplete',
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isRTL ? 'التكوين صحيح - جاهز للاستخدام' : 'Configuration valid - ready to use',
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showDetailedInstructions(BuildContext context, bool isRTL) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'التعليمات المفصلة' : 'Detailed Instructions'),
        content: SingleChildScrollView(
          child: Text(
            isRTL
              ? '''1. انتقل إلى Google Cloud Console
2. أنشئ Service Account جديد
3. حمل ملف JSON للمفاتيح
4. فعل Google Drive API
5. أنشئ مجلد في Google Drive
6. شارك المجلد مع Service Account
7. أضف المفاتيح في Firebase Remote Config
8. فعل google_drive_enabled = true'''
              : '''1. Go to Google Cloud Console
2. Create new Service Account
3. Download JSON key file
4. Enable Google Drive API
5. Create folder in Google Drive
6. Share folder with Service Account
7. Add keys to Firebase Remote Config
8. Set google_drive_enabled = true''',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'إغلاق' : 'Close'),
          ),
        ],
      ),
    );
  }
}
