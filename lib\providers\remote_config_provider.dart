import 'package:flutter/foundation.dart';
import '../services/remote_config_service.dart';
import '../models/storage_config_model.dart';

class RemoteConfigProvider extends ChangeNotifier {
  bool _isLoading = false;
  String? _error;
  bool _isInitialized = false;
  StorageConfigModel? _googleDriveConfig;
  Map<String, dynamic> _debugInfo = {};

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isInitialized => _isInitialized;
  StorageConfigModel? get googleDriveConfig => _googleDriveConfig;
  Map<String, dynamic> get debugInfo => _debugInfo;

  /// Initialize the remote config provider
  Future<void> initialize() async {
    if (_isInitialized) return;

    _setLoading(true);
    _clearError();

    try {
      // Initialize remote config service
      await RemoteConfigService.initialize();

      // Load Google Drive configuration
      await loadGoogleDriveConfig();

      _isInitialized = true;
      debugPrint('Remote Config Provider initialized successfully');
    } catch (e) {
      _setError('Failed to initialize remote config: $e');
      debugPrint('Error initializing Remote Config Provider: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Fetch and activate latest remote config
  Future<void> fetchAndActivate() async {
    _setLoading(true);
    _clearError();

    try {
      await RemoteConfigService.fetchAndActivate();

      // Reload configurations after fetching
      await loadGoogleDriveConfig();

      debugPrint('Remote config fetched and activated successfully');
    } catch (e) {
      _setError('Failed to fetch remote config: $e');
      debugPrint('Error fetching remote config: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Load Google Drive configuration from remote config
  Future<void> loadGoogleDriveConfig() async {
    try {
      _googleDriveConfig = RemoteConfigService.getGoogleDriveConfig();
      _debugInfo = RemoteConfigService.getGoogleDriveDebugInfo();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading Google Drive config from remote config: $e');
    }
  }

  /// Check if Google Drive is enabled
  bool isGoogleDriveEnabled() {
    return RemoteConfigService.isGoogleDriveEnabled();
  }

  /// Get specific remote config value
  String getString(String key) {
    return RemoteConfigService.getString(key);
  }

  bool getBool(String key) {
    return RemoteConfigService.getBool(key);
  }

  int getInt(String key) {
    return RemoteConfigService.getInt(key);
  }

  double getDouble(String key) {
    return RemoteConfigService.getDouble(key);
  }

  /// Get all Google Drive configuration keys for admin interface
  Map<String, String> getGoogleDriveConfigKeys() {
    return {
      'google_drive_enabled': 'Enable/Disable Google Drive integration',
      'google_drive_use_remote_config': 'Use Remote Config as primary source (true/false)',
      'google_drive_config_source': 'Configuration source preference (remote/database)',
      'google_drive_client_id': 'Google Drive Client ID',
      'google_drive_client_email': 'Google Drive Service Account Email',
      'google_drive_project_id': 'Google Cloud Project ID',
      'google_drive_private_key_id': 'Private Key ID',
      'google_drive_private_key': 'Private Key (PEM format)',
      'google_drive_parent_folder_id': 'Parent Folder ID (optional)',
      'google_drive_auth_uri': 'Auth URI (default: https://accounts.google.com/o/oauth2/auth)',
      'google_drive_token_uri': 'Token URI (default: https://oauth2.googleapis.com/token)',
      'google_drive_auth_provider_cert_url': 'Auth Provider X509 Cert URL',
      'google_drive_client_cert_url': 'Client X509 Cert URL (optional, auto-generated if empty)',
      'google_drive_universe_domain': 'Universe Domain (default: googleapis.com)',
    };
  }

  /// Get configuration status for admin interface
  Map<String, dynamic> getConfigurationStatus() {
    final config = _googleDriveConfig;

    return {
      'remote_config_initialized': RemoteConfigService.isInitialized,
      'google_drive_enabled': isGoogleDriveEnabled(),
      'use_remote_config_primary': RemoteConfigService.useRemoteConfigAsPrimary(),
      'config_source_preference': RemoteConfigService.getConfigSource(),
      'google_drive_configured': config != null,
      'has_client_id': config?.clientId.isNotEmpty ?? false,
      'has_client_email': config?.clientEmail?.isNotEmpty ?? false,
      'has_project_id': config?.projectId?.isNotEmpty ?? false,
      'has_private_key': config?.privateKey?.isNotEmpty ?? false,
      'has_private_key_id': config?.privateKeyId?.isNotEmpty ?? false,
      'has_parent_folder': config?.parentFolderId?.isNotEmpty ?? false,
      'has_auth_uri': config?.authUri?.isNotEmpty ?? false,
      'has_token_uri': config?.tokenUri?.isNotEmpty ?? false,
      'has_auth_provider_cert_url': config?.authProviderCertUrl?.isNotEmpty ?? false,
      'has_client_cert_url': config?.clientCertUrl?.isNotEmpty ?? false,
      'has_universe_domain': config?.universeDomain?.isNotEmpty ?? false,
      'config_source': config?.id == 'remote_config_google_drive' ? 'remote_config' : 'database',
      'last_updated': config?.updatedAt.toIso8601String(),
    };
  }

  /// Check if remote config is being used as primary source
  bool useRemoteConfigAsPrimary() {
    return RemoteConfigService.useRemoteConfigAsPrimary();
  }

  /// Get configuration source preference
  String getConfigSourcePreference() {
    return RemoteConfigService.getConfigSource();
  }

  /// Set configuration source preference (updates remote config)
  Future<void> setConfigSourcePreference(String source) async {
    await RemoteConfigService.setConfigSource(source);
    // Reload configuration after change
    await loadGoogleDriveConfig();
  }

  /// Validate Google Drive configuration
  bool validateGoogleDriveConfig() {
    if (!isGoogleDriveEnabled()) {
      return false;
    }

    final config = _googleDriveConfig;
    if (config == null) {
      return false;
    }

    // Check required fields
    return config.clientId.isNotEmpty &&
           (config.clientEmail?.isNotEmpty ?? false) &&
           (config.projectId?.isNotEmpty ?? false) &&
           (config.privateKey?.isNotEmpty ?? false);
  }

  /// Get validation errors
  List<String> getValidationErrors() {
    final errors = <String>[];

    if (!RemoteConfigService.isInitialized) {
      errors.add('Remote Config service is not initialized');
      return errors;
    }

    if (!isGoogleDriveEnabled()) {
      errors.add('Google Drive is disabled in remote config');
      return errors;
    }

    final config = _googleDriveConfig;
    if (config == null) {
      errors.add('Google Drive configuration not found in remote config');
      return errors;
    }

    if (config.clientId.isEmpty) {
      errors.add('Client ID is missing');
    }

    if (config.clientEmail?.isEmpty ?? true) {
      errors.add('Client Email is missing');
    }

    if (config.projectId?.isEmpty ?? true) {
      errors.add('Project ID is missing');
    }

    if (config.privateKey?.isEmpty ?? true) {
      errors.add('Private Key is missing');
    }

    return errors;
  }

  /// Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  /// Dispose resources
  @override
  void dispose() {
    super.dispose();
  }
}
