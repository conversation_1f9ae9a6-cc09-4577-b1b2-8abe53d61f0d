import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/login_log_model.dart';

class LoginLogProvider with ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  List<LoginLog> _logs = [];
  bool _isLoading = false;
  String? _error;
  
  List<LoginLog> get logs => _logs;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  // Fetch all login logs
  Future<void> fetchLogs({int limit = 100}) async {
    _setLoading(true);
    _clearError();
    
    try {
      final logsQuery = await _firestore.collection('login_logs')
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();
      
      _logs = logsQuery.docs.map((doc) => LoginLog.fromMap(doc.id, doc.data())).toList();
      notifyListeners();
    } catch (e) {
      debugPrint('Error fetching login logs: $e');
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }
  
  // Fetch logs for a specific user
  Future<void> fetchLogsForUser(String userId, {int limit = 50}) async {
    _setLoading(true);
    _clearError();
    
    try {
      final logsQuery = await _firestore.collection('login_logs')
          .where('userId', isEqualTo: userId)
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();
      
      _logs = logsQuery.docs.map((doc) => LoginLog.fromMap(doc.id, doc.data())).toList();
      notifyListeners();
    } catch (e) {
      debugPrint('Error fetching login logs for user: $e');
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }
  
  // Add a new login log
  Future<bool> addLog(LoginLog log) async {
    _setLoading(true);
    _clearError();
    
    try {
      await _firestore.collection('login_logs').add(log.toMap());
      return true;
    } catch (e) {
      debugPrint('Error adding login log: $e');
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // Helper methods
  void _setLoading(bool value) {
    if (_isLoading != value) {
      _isLoading = value;
      notifyListeners();
    }
  }
  
  void _setError(String error) {
    if (_error != error) {
      _error = error;
      notifyListeners();
    }
  }
  
  void _clearError() {
    if (_error != null) {
      _error = null;
      notifyListeners();
    }
  }
}
