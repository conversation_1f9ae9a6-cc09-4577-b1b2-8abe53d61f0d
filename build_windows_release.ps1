# HM Device Errors - Windows Release Build Script
# This script builds an optimized Windows executable

param(
    [switch]$Clean = $false,
    [switch]$Verbose = $false
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Colors for output
$Green = "Green"
$Yellow = "Yellow"
$Red = "Red"
$Cyan = "Cyan"
$White = "White"

Write-Host "🚀 Starting HM Device Errors Windows Release Build..." -ForegroundColor $Green
Write-Host "📱 Version: 1.0.4" -ForegroundColor $Cyan
Write-Host "🎯 Target: Windows x64 (Release)" -ForegroundColor $Cyan
Write-Host ""

# Check if Flutter is installed
try {
    $flutterVersionOutput = flutter --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Flutter not found"
    }
    # Extract version info for display
    $flutterVersion = ($flutterVersionOutput | Select-String "Flutter" | Select-Object -First 1).ToString().Split()[1]
    Write-Host "✅ Flutter detected (v$flutterVersion)" -ForegroundColor $Green
} catch {
    Write-Host "❌ Flutter is not installed or not in PATH" -ForegroundColor $Red
    exit 1
}

# Check if Visual Studio Build Tools are available
try {
    $vsWhere = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere.exe"
    if (Test-Path $vsWhere) {
        $vsInstallation = & $vsWhere -latest -products * -requires Microsoft.VisualStudio.Component.VC.Tools.x86.x64 -property installationPath
        if ($vsInstallation) {
            Write-Host "✅ Visual Studio Build Tools detected" -ForegroundColor $Green
        } else {
            Write-Host "⚠️  Visual Studio Build Tools not found, but continuing..." -ForegroundColor $Yellow
        }
    }
} catch {
    Write-Host "⚠️  Could not detect Visual Studio Build Tools" -ForegroundColor $Yellow
}

# Clean previous builds if requested
if ($Clean) {
    Write-Host "🧹 Cleaning previous builds..." -ForegroundColor $Yellow
    flutter clean
    if (Test-Path "build\windows") {
        Remove-Item -Recurse -Force "build\windows"
        Write-Host "   Removed build\windows directory" -ForegroundColor $Yellow
    }
}

# Get dependencies
Write-Host "📦 Getting dependencies..." -ForegroundColor $Yellow
flutter pub get

# Analyze code for issues
Write-Host "🔍 Analyzing code..." -ForegroundColor $Yellow
flutter analyze
if ($LASTEXITCODE -ne 0) {
    Write-Host "⚠️  Code analysis found issues, but continuing..." -ForegroundColor $Yellow
}

# Enable Windows desktop
Write-Host "🖥️  Enabling Windows desktop..." -ForegroundColor $Yellow
flutter config --enable-windows-desktop

# Build Windows release
Write-Host "🔨 Building optimized Windows release..." -ForegroundColor $Yellow
Write-Host "   This may take several minutes..." -ForegroundColor $White

$buildArgs = @(
    "build", "windows",
    "--release",
    "--dart-define=FLUTTER_WEB_USE_SKIA=true"
)

if ($Verbose) {
    $buildArgs += "--verbose"
}

& flutter @buildArgs

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed!" -ForegroundColor $Red
    exit 1
}

# Check build results
$exePath = "build\windows\x64\runner\Release\hm_device_errors.exe"
$dataPath = "build\windows\x64\runner\Release\data"

Write-Host ""
Write-Host "✅ Build completed successfully!" -ForegroundColor $Green
Write-Host ""
Write-Host "📊 Build Results:" -ForegroundColor $Cyan

if (Test-Path $exePath) {
    $exeSize = [math]::Round((Get-Item $exePath).Length / 1MB, 2)
    Write-Host "📁 Executable: $exePath" -ForegroundColor $White
    Write-Host "📏 Executable Size: $exeSize MB" -ForegroundColor $Green

    # Get total build size
    $buildDir = "build\windows\x64\runner\Release"
    if (Test-Path $buildDir) {
        $totalSize = [math]::Round((Get-ChildItem $buildDir -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB, 2)
        Write-Host "📏 Total Build Size: $totalSize MB" -ForegroundColor $Green
    }

    # Check for required DLLs
    $requiredFiles = @(
        "flutter_windows.dll",
        "data\icudtl.dat",
        "data\flutter_assets"
    )

    Write-Host ""
    Write-Host "🔍 Checking required files:" -ForegroundColor $Cyan
    foreach ($file in $requiredFiles) {
        $fullPath = Join-Path $buildDir $file
        if (Test-Path $fullPath) {
            Write-Host "   ✅ $file" -ForegroundColor $Green
        } else {
            Write-Host "   ❌ $file (missing)" -ForegroundColor $Red
        }
    }

} else {
    Write-Host "❌ Executable not found at expected location!" -ForegroundColor $Red
    exit 1
}

Write-Host ""
Write-Host "🎉 HM Device Errors v1.0.1 Windows build is ready!" -ForegroundColor $Green
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor $Cyan
Write-Host "   1. Test the executable on different Windows versions" -ForegroundColor $White
Write-Host "   2. Create installer package (optional)" -ForegroundColor $White
Write-Host "   3. Distribute the entire Release folder" -ForegroundColor $White
Write-Host "   4. Ensure Firebase configuration is properly set up" -ForegroundColor $White
Write-Host ""
Write-Host "🚀 To run the application:" -ForegroundColor $Cyan
Write-Host "   cd build\windows\x64\runner\Release" -ForegroundColor $White
Write-Host "   .\hm_device_errors.exe" -ForegroundColor $White
Write-Host ""

# Optional: Create a simple batch file to run the app
$batchContent = @"
@echo off
cd /d "%~dp0build\windows\x64\runner\Release"
start hm_device_errors.exe
"@

$batchPath = "run_hm_device_errors.bat"
$batchContent | Out-File -FilePath $batchPath -Encoding ASCII
Write-Host "📝 Created launcher script: $batchPath" -ForegroundColor $Green

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor $Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
