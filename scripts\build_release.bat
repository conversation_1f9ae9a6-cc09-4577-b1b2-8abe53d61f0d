@echo off
echo ========================================
echo HM Device Errors - Release Build Script
echo ========================================
echo.

:: Set error handling
setlocal enabledelayedexpansion

:: Check if Flutter is installed
flutter --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Flutter is not installed or not in PATH
    pause
    exit /b 1
)

echo [1/6] Cleaning previous build...
flutter clean
if errorlevel 1 (
    echo ERROR: Failed to clean project
    pause
    exit /b 1
)

echo [2/6] Getting dependencies...
flutter pub get
if errorlevel 1 (
    echo ERROR: Failed to get dependencies
    pause
    exit /b 1
)

echo [3/6] Running code analysis...
flutter analyze
if errorlevel 1 (
    echo WARNING: Code analysis found issues
    echo Continue anyway? (y/n)
    set /p continue=
    if /i not "!continue!"=="y" (
        echo Build cancelled
        pause
        exit /b 1
    )
)

echo [4/6] Building APK (Release)...
flutter build apk --release --shrink --obfuscate --split-debug-info=build/debug-info
if errorlevel 1 (
    echo ERROR: Failed to build APK
    pause
    exit /b 1
)

echo [5/6] Building App Bundle (Release)...
flutter build appbundle --release --shrink --obfuscate --split-debug-info=build/debug-info
if errorlevel 1 (
    echo ERROR: Failed to build App Bundle
    pause
    exit /b 1
)

echo [6/6] Analyzing build size...
echo.
echo ========================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.

:: Get file sizes
for %%f in (build\app\outputs\flutter-apk\app-release.apk) do (
    set apk_size=%%~zf
    set /a apk_mb=!apk_size!/1024/1024
    echo APK Size: !apk_mb! MB
)

for %%f in (build\app\outputs\bundle\release\app-release.aab) do (
    set aab_size=%%~zf
    set /a aab_mb=!aab_size!/1024/1024
    echo AAB Size: !aab_mb! MB
)

echo.
echo Output files:
echo - APK: build\app\outputs\flutter-apk\app-release.apk
echo - AAB: build\app\outputs\bundle\release\app-release.aab
echo - Debug Info: build\debug-info\
echo.

:: Check if files exist and show success message
if exist "build\app\outputs\flutter-apk\app-release.apk" (
    echo ✓ APK build successful
) else (
    echo ✗ APK build failed
)

if exist "build\app\outputs\bundle\release\app-release.aab" (
    echo ✓ App Bundle build successful
) else (
    echo ✗ App Bundle build failed
)

echo.
echo Build completed at: %date% %time%
echo ========================================

pause
