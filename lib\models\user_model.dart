import 'package:cloud_firestore/cloud_firestore.dart';
import 'permission_model.dart';

enum UserRole { user, admin }
enum Gender { male, female, other }

class UserModel {
  final String id;
  final String email;
  final String? displayName;
  final String? phoneNumber;
  final UserRole role;
  final DateTime createdAt;
  final bool isActive;
  final bool isMainAdmin;
  final Gender? gender;
  final DateTime? birthDate;
  final DateTime? expiryDate;
  final String? profileImageUrl;
  final String? roleId; // Reference to UserRole from permission_model
  final List<PermissionType> customPermissions; // Additional permissions beyond role

  UserModel({
    required this.id,
    required this.email,
    this.displayName,
    this.phoneNumber,
    required this.role,
    required this.createdAt,
    this.isActive = true,
    this.isMainAdmin = false,
    this.gender,
    this.birthDate,
    this.expiryDate,
    this.profileImageUrl,
    this.roleId,
    this.customPermissions = const [],
  });

  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    final email = data['email'] ?? '';

    return UserModel(
      id: doc.id,
      email: email,
      displayName: data['displayName'],
      phoneNumber: data['phoneNumber'],
      role: data['role'] == 'admin' ? UserRole.admin : UserRole.user,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      isActive: data['isActive'] ?? true,
      isMainAdmin: email == '<EMAIL>' || data['isMainAdmin'] == true,
      gender: data['gender'] != null ? _parseGender(data['gender']) : null,
      birthDate: data['birthDate'] != null ? (data['birthDate'] as Timestamp).toDate() : null,
      expiryDate: data['expiryDate'] != null ? (data['expiryDate'] as Timestamp).toDate() : null,
      profileImageUrl: data['profileImageUrl'],
      roleId: data['roleId'],
      customPermissions: (data['customPermissions'] as List<dynamic>?)
          ?.map((p) => PermissionType.values.firstWhere(
                (type) => type.toString() == p,
                orElse: () => PermissionType.viewErrors,
              ))
          .toList() ?? [],
    );
  }

  factory UserModel.fromMap(Map<String, dynamic> data) {
    final email = data['email'] ?? '';

    return UserModel(
      id: data['id'] ?? '',
      email: email,
      displayName: data['displayName'],
      phoneNumber: data['phoneNumber'],
      role: data['role'] == 'admin' ? UserRole.admin : UserRole.user,
      createdAt: data['createdAt'] is Timestamp
          ? (data['createdAt'] as Timestamp).toDate()
          : DateTime.tryParse(data['createdAt'] ?? '') ?? DateTime.now(),
      isActive: data['isActive'] ?? true,
      isMainAdmin: email == '<EMAIL>' || data['isMainAdmin'] == true,
      gender: data['gender'] != null ? _parseGender(data['gender']) : null,
      birthDate: data['birthDate'] != null
          ? (data['birthDate'] is Timestamp
              ? (data['birthDate'] as Timestamp).toDate()
              : DateTime.tryParse(data['birthDate']))
          : null,
      expiryDate: data['expiryDate'] != null
          ? (data['expiryDate'] is Timestamp
              ? (data['expiryDate'] as Timestamp).toDate()
              : DateTime.tryParse(data['expiryDate']))
          : null,
      profileImageUrl: data['profileImageUrl'],
      roleId: data['roleId'],
      customPermissions: (data['customPermissions'] as List<dynamic>?)
          ?.map((p) => PermissionType.values.firstWhere(
                (type) => type.toString() == p,
                orElse: () => PermissionType.viewErrors,
              ))
          .toList() ?? [],
    );
  }

  static Gender _parseGender(String genderString) {
    switch (genderString) {
      case 'male':
        return Gender.male;
      case 'female':
        return Gender.female;
      case 'other':
        return Gender.other;
      default:
        return Gender.other;
    }
  }

  Map<String, dynamic> toMap() {
    return {
      'email': email,
      'displayName': displayName,
      'phoneNumber': phoneNumber,
      'role': role == UserRole.admin ? 'admin' : 'user',
      'createdAt': Timestamp.fromDate(createdAt),
      'isActive': isActive,
      'isMainAdmin': isMainAdmin || email == '<EMAIL>',
      'gender': gender?.toString().split('.').last,
      'birthDate': birthDate != null ? Timestamp.fromDate(birthDate!) : null,
      'expiryDate': expiryDate != null ? Timestamp.fromDate(expiryDate!) : null,
      'profileImageUrl': profileImageUrl,
      'roleId': roleId,
      'customPermissions': customPermissions.map((p) => p.toString()).toList(),
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? displayName,
    String? phoneNumber,
    UserRole? role,
    DateTime? createdAt,
    bool? isActive,
    bool? isMainAdmin,
    Gender? gender,
    DateTime? birthDate,
    DateTime? expiryDate,
    String? profileImageUrl,
    String? roleId,
    List<PermissionType>? customPermissions,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      role: role ?? this.role,
      createdAt: createdAt ?? this.createdAt,
      isActive: isActive ?? this.isActive,
      isMainAdmin: isMainAdmin ?? this.isMainAdmin,
      gender: gender ?? this.gender,
      birthDate: birthDate ?? this.birthDate,
      expiryDate: expiryDate ?? this.expiryDate,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      roleId: roleId ?? this.roleId,
      customPermissions: customPermissions ?? this.customPermissions,
    );
  }

  // Helper methods
  String get genderDisplayName {
    switch (gender) {
      case Gender.male:
        return 'ذكر';
      case Gender.female:
        return 'أنثى';
      case Gender.other:
        return 'آخر';
      default:
        return 'غير محدد';
    }
  }

  bool get isExpired {
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate!);
  }

  int? get age {
    if (birthDate == null) return null;
    final now = DateTime.now();
    int age = now.year - birthDate!.year;
    if (now.month < birthDate!.month ||
        (now.month == birthDate!.month && now.day < birthDate!.day)) {
      age--;
    }
    return age;
  }

  // Permission helper methods
  bool hasPermission(PermissionType permission) {
    // Main admin has all permissions
    if (isMainAdmin) return true;

    // Check custom permissions first
    if (customPermissions.contains(permission)) return true;

    // Check role-based permissions
    if (roleId != null) {
      final systemRole = SystemRoles.allSystemRoles.firstWhere(
        (role) => role.id == roleId,
        orElse: () => SystemRoles.user,
      );
      return systemRole.hasPermission(permission);
    }

    // Fallback to legacy role system
    if (role == UserRole.admin) {
      return SystemRoles.admin.hasPermission(permission);
    } else {
      return SystemRoles.user.hasPermission(permission);
    }
  }

  bool hasAnyPermission(List<PermissionType> permissions) {
    return permissions.any((permission) => hasPermission(permission));
  }

  bool hasAllPermissions(List<PermissionType> permissions) {
    return permissions.every((permission) => hasPermission(permission));
  }

  List<PermissionType> get allPermissions {
    Set<PermissionType> permissions = Set.from(customPermissions);

    if (isMainAdmin) {
      permissions.addAll(PermissionType.values);
    } else if (roleId != null) {
      final systemRole = SystemRoles.allSystemRoles.firstWhere(
        (role) => role.id == roleId,
        orElse: () => SystemRoles.user,
      );
      permissions.addAll(systemRole.permissions);
    } else {
      // Fallback to legacy role system
      if (role == UserRole.admin) {
        permissions.addAll(SystemRoles.admin.permissions);
      } else {
        permissions.addAll(SystemRoles.user.permissions);
      }
    }

    return permissions.toList();
  }

  String get effectiveRoleName {
    if (isMainAdmin) return 'Super Administrator';

    if (roleId != null) {
      final systemRole = SystemRoles.allSystemRoles.firstWhere(
        (role) => role.id == roleId,
        orElse: () => SystemRoles.user,
      );
      return systemRole.name;
    }

    // Fallback to legacy role system
    return role == UserRole.admin ? 'Administrator' : 'Regular User';
  }

  String get effectiveRoleNameAr {
    if (isMainAdmin) return 'المدير العام';

    if (roleId != null) {
      final systemRole = SystemRoles.allSystemRoles.firstWhere(
        (role) => role.id == roleId,
        orElse: () => SystemRoles.user,
      );
      return systemRole.nameAr;
    }

    // Fallback to legacy role system
    return role == UserRole.admin ? 'مدير' : 'مستخدم عادي';
  }
}
