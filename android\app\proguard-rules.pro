# ProGuard rules for Flutter, Firebase, Gson, Kotlin Coroutines, Android, security, Google Drive API, logging removal, string/resource obfuscation, APK size optimization, advanced optimizations, unused code removal, debug logging removal, Firebase/Google API/image library optimizations, annotation processor removal, image/file/network operations, and architecture/serialization support.

# Flutter specific rules - CRITICAL for app startup
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }
-keep class io.flutter.embedding.** { *; }
-keep class io.flutter.embedding.android.** { *; }
-keep class io.flutter.embedding.engine.** { *; }

# Keep Flutter main application class
-keep class com.mohamedrady.hmdeviceerrors.MainActivity { *; }
-keep class com.mohamedrady.hmdeviceerrors.MainApplication { *; }

# Keep all Flutter method channels
-keep class * extends io.flutter.plugin.common.MethodChannel$MethodCallHandler { *; }
-keep class * extends io.flutter.plugin.common.EventChannel$StreamHandler { *; }

# Firebase rules
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.firebase.**
-dontwarn com.google.android.gms.**

# Gson rules
-keepattributes Signature
-keepattributes *Annotation*
-dontwarn sun.misc.**
-keep class com.google.gson.** { *; }

# Keep model classes used by Gson
-keep class com.mohamedrady.hmdeviceerrors.models.** { *; }

# Keep Kotlin Coroutines
-keepnames class kotlinx.coroutines.internal.MainDispatcherFactory {}
-keepnames class kotlinx.coroutines.CoroutineExceptionHandler {}
-keepclassmembernames class kotlinx.** {
    volatile <fields>;
}

# General Android rules
-keepattributes SourceFile,LineNumberTable
-keep public class * extends java.lang.Exception

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep the BuildConfig
-keep class com.mohamedrady.hmdeviceerrors.BuildConfig { *; }

# Keep the support library
-keep class androidx.** { *; }
-keep interface androidx.** { *; }
-keep class com.google.android.material.** { *; }
-dontwarn androidx.**
-dontwarn com.google.android.material.**

# Flutter deferred components rules - ignore missing Google Play Core classes
-dontwarn com.google.android.play.core.**
-dontwarn io.flutter.embedding.engine.deferredcomponents.**
-dontwarn io.flutter.embedding.android.FlutterPlayStoreSplitApplication

# Ignore missing classes that are not used in our app
-dontwarn com.google.android.play.core.splitcompat.SplitCompatApplication
-dontwarn com.google.android.play.core.splitinstall.**
-dontwarn com.google.android.play.core.tasks.**

# Security and Encryption rules
-keep class javax.crypto.** { *; }
-keep class java.security.** { *; }
-dontwarn javax.crypto.**
-dontwarn java.security.**

# Google Drive API
-keep class com.google.api.** { *; }
-keep class com.google.auth.** { *; }
-dontwarn com.google.api.**
-dontwarn com.google.auth.**

# Enable aggressive logging removal for production builds
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
}

# Obfuscate sensitive strings and resources
-adaptclassstrings
-adaptresourcefilenames
-adaptresourcefilecontents **.properties,**.xml,**.json

# Aggressive optimization settings for maximum size reduction
-optimizations !code/simplification/variable,!code/simplification/arithmetic
-optimizationpasses 5
-allowaccessmodification
-dontpreverify
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement

# Additional security measures - REDUCED for stability
# -repackageclasses ''  # DISABLED - can cause startup issues
-printmapping mapping.txt

# DISABLE advanced size optimizations that can cause startup issues
# -overloadaggressively
# -mergeinterfacesaggressively
# -flattenpackagehierarchy

# DISABLE aggressive code removal that can cause startup issues
# -assumenosideeffects class kotlin.jvm.internal.Intrinsics {
#     static void checkParameterIsNotNull(java.lang.Object, java.lang.String);
#     static void checkNotNullParameter(java.lang.Object, java.lang.String);
#     static void checkExpressionValueIsNotNull(java.lang.Object, java.lang.String);
#     static void checkNotNullExpressionValue(java.lang.Object, java.lang.String);
#     static void checkReturnedValueIsNotNull(java.lang.Object, java.lang.String);
#     static void checkFieldIsNotNull(java.lang.Object, java.lang.String);
#     static void throwUninitializedPropertyAccessException(java.lang.String);
# }

# DISABLE debug logging removal for troubleshooting
# -assumenosideeffects class android.util.Log {
#     public static *** d(...);
#     public static *** v(...);
#     public static *** i(...);
# }

# DISABLE Flutter debug code removal
# -assumenosideeffects class io.flutter.Log {
#     public static *** d(...);
#     public static *** v(...);
#     public static *** i(...);
# }

# Optimize Firebase
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.firebase.**
-dontwarn com.google.android.gms.**

# Remove unused Firebase features (keep only what we use)
-dontwarn com.google.firebase.analytics.**
-dontwarn com.google.firebase.crashlytics.**
-dontwarn com.google.firebase.perf.**
-dontwarn com.google.firebase.ml.**
-dontwarn com.google.firebase.functions.**

# Optimize Google APIs
-keep class com.google.api.client.** { *; }
-dontwarn com.google.api.client.**
-dontwarn com.google.common.**

# Remove unused annotation processors
-dontwarn javax.annotation.**
-dontwarn javax.inject.**
-dontwarn org.checkerframework.**

# Optimize image libraries
-keep class com.bumptech.glide.** { *; }
-dontwarn com.bumptech.glide.**

# Remove reflection usage where possible
-dontwarn java.lang.invoke.**
-dontwarn **$$serializer

# Optimize for specific architectures only
-keepattributes *Annotation*,Signature,InnerClasses,EnclosingMethod

# Keep serializable classes
-keep class * extends java.io.Serializable { *; }
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Image processing and file operations
-keep class android.graphics.** { *; }
-keep class java.io.** { *; }
-keep class java.nio.** { *; }
-dontwarn android.graphics.**
-dontwarn java.io.**
-dontwarn java.nio.**

# Network operations
-keep class java.net.** { *; }
-keep class javax.net.** { *; }
-dontwarn java.net.**
-dontwarn javax.net.**

# DISABLE aggressive optimizations that can cause startup issues
# Additional optimizations for version 1.0.6 - DISABLED for stability
# Remove unused resources more aggressively
# -assumenosideeffects class java.lang.System {
#     public static void gc();
#     public static long currentTimeMillis();
#     public static void arraycopy(java.lang.Object, int, java.lang.Object, int, int);
# }

# Optimize string operations - DISABLED
# -assumenosideeffects class java.lang.String {
#     public java.lang.String intern();
# }

# Remove debug assertions - DISABLED
# -assumenosideeffects class java.lang.Class {
#     public boolean desiredAssertionStatus();
# }

# Optimize collections
-dontwarn java.util.concurrent.**
-keep class java.util.** { *; }

# Enhanced memory optimization
-optimizations !code/simplification/variable,!code/simplification/arithmetic
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses

# Remove unused Flutter plugins
-dontwarn io.flutter.plugins.webviewflutter.**
-dontwarn io.flutter.plugins.camera.**
-dontwarn io.flutter.plugins.videoplayer.**

# Optimize for app size (version 1.0.6 specific)
-keepattributes RuntimeVisibleAnnotations,RuntimeInvisibleAnnotations
-keepattributes RuntimeVisibleParameterAnnotations,RuntimeInvisibleParameterAnnotations
-keepattributes AnnotationDefault

# Remove unused third-party libraries
-dontwarn okhttp3.**
-dontwarn retrofit2.**
-dontwarn com.squareup.**

# CRITICAL: Keep all application classes to prevent startup issues
-keep class com.mohamedrady.hmdeviceerrors.** { *; }

# Keep all provider classes
-keep class * extends android.content.ContentProvider { *; }
-keep class * extends android.app.Application { *; }
-keep class * extends android.app.Activity { *; }
-keep class * extends android.app.Service { *; }

# Keep all Flutter plugin registrants
-keep class io.flutter.plugins.GeneratedPluginRegistrant { *; }

# Keep SharedPreferences and related classes
-keep class android.content.SharedPreferences { *; }
-keep class android.content.SharedPreferences$Editor { *; }

# Keep Path Provider classes
-keep class io.flutter.plugins.pathprovider.** { *; }

# Keep all enum classes
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep all classes with @Keep annotation
-keep @androidx.annotation.Keep class * { *; }
-keepclassmembers class * {
    @androidx.annotation.Keep *;
}
