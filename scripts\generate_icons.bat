@echo off
echo Generating HM Device Errors app icons...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

REM Check if required packages are installed
python -c "import PIL, numpy" >nul 2>&1
if errorlevel 1 (
    echo Installing required packages...
    pip install Pillow numpy
    if errorlevel 1 (
        echo Error: Failed to install required packages
        pause
        exit /b 1
    )
)

REM Run the icon generation script
python scripts\generate_icons.py

if errorlevel 1 (
    echo Error: Icon generation failed
    pause
    exit /b 1
)

echo.
echo Icons generated successfully!
echo You can now build and test your app.
pause
