name: hm_device_errors
description: "A professional app for managing home device errors and solutions."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.8+10

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # Firebase - Optimized versions
  firebase_core: ^3.13.1
  firebase_auth: ^5.5.4
  cloud_firestore: ^5.6.8
  firebase_storage: ^12.4.6
  firebase_messaging: ^15.1.6
  firebase_remote_config: ^5.1.6
  flutter_local_notifications: ^17.2.3

  # State Management
  provider: ^6.1.2

  # UI
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.3.1
  image_picker: ^1.0.7
  file_picker: ^8.1.2
  # flutter_staggered_grid_view: ^0.7.0 # Removed for size optimization - using GridView instead

  # Device Info
  device_info_plus: ^9.1.2
  package_info_plus: ^8.3.0

  # Cloud Storage
  googleapis: ^12.0.0
  googleapis_auth: ^1.4.1
  url_launcher: ^6.2.5
  http: ^1.2.1
  shared_preferences: ^2.2.2

  # Location and IP detection
  geolocator: ^10.1.0
  permission_handler: ^11.3.1

  # Image handling
  path_provider: ^2.1.2
  path: ^1.8.3

  # Video player
  video_player: ^2.8.2
  chewie: ^1.7.5
  video_thumbnail: ^0.5.3

  # Additional UI components - Removed for size optimization
  # flutter_spinkit: ^5.2.0 # Replaced with CircularProgressIndicator
  # shimmer: ^3.0.0 # Not used in codebase

  # Localization
  intl: ^0.19.0
  flutter_localization: ^0.2.1

  # Excel import/export
  excel: ^4.0.6
  share_plus: ^7.2.2

  # Security & Encryption
  encrypt: ^5.0.3
  crypto: ^3.0.3

  # Performance optimization
  flutter_launcher_icons: ^0.13.1
  flutter_image_compress: ^2.3.0
  image: ^4.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/

  # Generate separate APKs for different architectures to reduce size
  # This will be handled by the build system

  # Size optimization settings
  generate:
    # Enable tree shaking for smaller bundle size
    tree-shake-icons: true
    # Split debug info for smaller APK
    split-debug-info: true
    # Enable obfuscation for smaller size
    obfuscate: true

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # Highly optimized fonts configuration (minimal for smallest app size)
  fonts:
    - family: AwanZaman
      fonts:
        - asset: assets/fonts/AwanZaman-Regular.ttf
          weight: 400
        - asset: assets/fonts/AwanZaman-Bold.ttf
          weight: 700
        # Removed Light and Medium weights to reduce app size by ~2-3MB
        # Only Regular (400) and Bold (700) for optimal performance
