import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/storage_config_model.dart';
import 'encryption_service.dart';

class StorageConfigService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collectionName = 'storageConfig';
  static const String _appSettingsDoc = 'app_storage_settings';

  // Singleton pattern
  static final StorageConfigService _instance = StorageConfigService._internal();
  factory StorageConfigService() => _instance;
  StorageConfigService._internal();

  // Cache for storage configurations
  final Map<String, StorageConfigModel> _configCache = {};
  AppStorageSettings? _appSettings;
  bool _isInitialized = false;

  /// Initialize the service and load configurations
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadConfigurations();
      await _loadAppSettings();
      _isInitialized = true;
      debugPrint('StorageConfigService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing StorageConfigService: $e');
      rethrow;
    }
  }

  /// Load all storage configurations from Firestore
  Future<void> _loadConfigurations() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collectionName)
          .where('isActive', isEqualTo: true)
          .get();

      _configCache.clear();
      for (final doc in querySnapshot.docs) {
        final config = StorageConfigModel.fromFirestore(doc);
        _configCache[config.configName] = config;
      }

      debugPrint('Loaded ${_configCache.length} storage configurations');
    } catch (e) {
      debugPrint('Error loading storage configurations: $e');
      throw Exception('Failed to load storage configurations: $e');
    }
  }

  /// Load app storage settings
  Future<void> _loadAppSettings() async {
    try {
      final doc = await _firestore
          .collection(_collectionName)
          .doc(_appSettingsDoc)
          .get();

      if (doc.exists) {
        _appSettings = AppStorageSettings.fromMap(doc.data()!);
      } else {
        _appSettings = const AppStorageSettings();
        // Save default settings
        await _saveAppSettings(_appSettings!);
      }
    } catch (e) {
      debugPrint('Error loading app storage settings: $e');
      _appSettings = const AppStorageSettings();
    }
  }

  /// Get storage configuration by name
  StorageConfigModel? getConfig(String configName) {
    return _configCache[configName];
  }

  /// Get Google Drive configuration
  StorageConfigModel? get googleDriveConfig => getConfig('google_drive');

  /// Get OneDrive configuration
  StorageConfigModel? get oneDriveConfig => getConfig('onedrive');

  /// Get default storage configuration
  StorageConfigModel? get defaultConfig {
    // First try to find the one marked as default
    for (final config in _configCache.values) {
      if (config.isDefault) return config;
    }

    // Fallback to Google Drive if available
    return googleDriveConfig ?? _configCache.values.firstOrNull;
  }

  /// Get app storage settings
  AppStorageSettings get appSettings => _appSettings ?? const AppStorageSettings();

  /// Get all active configurations
  List<StorageConfigModel> get allConfigs => _configCache.values.toList();

  /// Save or update storage configuration
  Future<void> saveConfig(StorageConfigModel config, String userId) async {
    try {
      debugPrint('Attempting to save storage config: ${config.configName}');
      debugPrint('User ID: $userId');
      debugPrint('Config ID: ${config.id}');

      // Encrypt sensitive data only if not empty
      String encryptedSecret = config.clientSecret;
      if (config.clientSecret.isNotEmpty) {
        try {
          encryptedSecret = EncryptionService.encryptText(config.clientSecret);
          debugPrint('Client secret encrypted successfully');
        } catch (e) {
          debugPrint('Warning: Failed to encrypt client secret: $e');
          // Continue with unencrypted secret for now
        }
      }

      final encryptedConfig = config.copyWith(
        clientSecret: encryptedSecret,
        updatedAt: DateTime.now(),
        updatedBy: userId,
      );

      // If this is being set as default, unset other defaults
      if (config.isDefault) {
        await _unsetOtherDefaults(config.configName);
      }

      debugPrint('Saving to Firestore...');
      await _firestore
          .collection(_collectionName)
          .doc(config.id)
          .set(encryptedConfig.toMap(), SetOptions(merge: true));

      // Update cache with original (unencrypted) config
      _configCache[config.configName] = config;

      debugPrint('Storage configuration saved successfully: ${config.configName}');
    } catch (e) {
      debugPrint('Error saving storage configuration: $e');
      debugPrint('Error type: ${e.runtimeType}');
      if (e.toString().contains('permission-denied')) {
        throw Exception('Permission denied: You need admin privileges to save storage configuration');
      } else if (e.toString().contains('network')) {
        throw Exception('Network error: Please check your internet connection');
      } else {
        throw Exception('Failed to save storage configuration: $e');
      }
    }
  }

  /// Create new storage configuration
  Future<StorageConfigModel> createConfig({
    required String configName,
    required String clientId,
    required String clientSecret,
    String? redirectUri,
    String? parentFolderId,
    String? projectId,
    String? privateKeyId,
    String? privateKey,
    String? clientEmail,
    String? authUri,
    String? tokenUri,
    String? authProviderCertUrl,
    String? clientCertUrl,
    String? universeDomain,
    Map<String, dynamic>? additionalSettings,
    bool isDefault = false,
    required String userId,
  }) async {
    try {
      final docRef = _firestore.collection(_collectionName).doc();
      final now = DateTime.now();

      final config = StorageConfigModel(
        id: docRef.id,
        configName: configName,
        clientId: clientId,
        clientSecret: clientSecret,
        redirectUri: redirectUri,
        parentFolderId: parentFolderId,
        projectId: projectId,
        privateKeyId: privateKeyId,
        privateKey: privateKey,
        clientEmail: clientEmail,
        authUri: authUri,
        tokenUri: tokenUri,
        authProviderCertUrl: authProviderCertUrl,
        clientCertUrl: clientCertUrl,
        universeDomain: universeDomain,
        additionalSettings: additionalSettings ?? {},
        isDefault: isDefault,
        createdAt: now,
        updatedAt: now,
        createdBy: userId,
        updatedBy: userId,
      );

      await saveConfig(config, userId);
      return config;
    } catch (e) {
      debugPrint('Error creating storage configuration: $e');
      throw Exception('Failed to create storage configuration: $e');
    }
  }

  /// Update existing storage configuration
  Future<void> updateConfig(
    String configId,
    Map<String, dynamic> updates,
    String userId,
  ) async {
    try {
      final existingConfig = _configCache.values
          .firstWhere((config) => config.id == configId);

      final updatedConfig = StorageConfigModel.fromMap({
        ...existingConfig.toMap(),
        ...updates,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
        'updatedBy': userId,
      });

      await saveConfig(updatedConfig, userId);
    } catch (e) {
      debugPrint('Error updating storage configuration: $e');
      throw Exception('Failed to update storage configuration: $e');
    }
  }

  /// Delete storage configuration
  Future<void> deleteConfig(String configId) async {
    try {
      await _firestore.collection(_collectionName).doc(configId).delete();

      // Remove from cache
      _configCache.removeWhere((key, config) => config.id == configId);

      debugPrint('Storage configuration deleted: $configId');
    } catch (e) {
      debugPrint('Error deleting storage configuration: $e');
      throw Exception('Failed to delete storage configuration: $e');
    }
  }

  /// Set configuration as default
  Future<void> setAsDefault(String configName, String userId) async {
    try {
      final config = _configCache[configName];
      if (config == null) {
        throw Exception('Configuration not found: $configName');
      }

      await _unsetOtherDefaults(configName);

      final updatedConfig = config.copyWith(
        isDefault: true,
        updatedAt: DateTime.now(),
        updatedBy: userId,
      );

      await saveConfig(updatedConfig, userId);
    } catch (e) {
      debugPrint('Error setting default configuration: $e');
      throw Exception('Failed to set default configuration: $e');
    }
  }

  /// Unset other configurations as default
  Future<void> _unsetOtherDefaults(String excludeConfigName) async {
    try {
      final batch = _firestore.batch();

      for (final config in _configCache.values) {
        if (config.configName != excludeConfigName && config.isDefault) {
          final docRef = _firestore.collection(_collectionName).doc(config.id);
          batch.update(docRef, {'isDefault': false});

          // Update cache
          _configCache[config.configName] = config.copyWith(isDefault: false);
        }
      }

      await batch.commit();
    } catch (e) {
      debugPrint('Error unsetting other defaults: $e');
    }
  }

  /// Save app storage settings
  Future<void> _saveAppSettings(AppStorageSettings settings) async {
    try {
      await _firestore
          .collection(_collectionName)
          .doc(_appSettingsDoc)
          .set(settings.toMap());

      _appSettings = settings;
      debugPrint('App storage settings saved');
    } catch (e) {
      debugPrint('Error saving app storage settings: $e');
      throw Exception('Failed to save app storage settings: $e');
    }
  }

  /// Update app storage settings
  Future<void> updateAppSettings(AppStorageSettings settings) async {
    await _saveAppSettings(settings);
  }

  /// Test storage configuration
  Future<bool> testConfig(StorageConfigModel config) async {
    try {
      // This would implement actual testing logic for each storage service
      // For now, just validate the configuration
      return config.isValid;
    } catch (e) {
      debugPrint('Error testing storage configuration: $e');
      return false;
    }
  }

  /// Refresh configurations from Firestore
  Future<void> refresh() async {
    await _loadConfigurations();
    await _loadAppSettings();
  }

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  /// Get configuration by service type
  StorageConfigModel? getConfigByType(String serviceType) {
    return _configCache[serviceType];
  }
}
