import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../utils/platform_utils.dart';

class AppSignatureService {

  static const String _appSecret = 'hm_device_errors_secret_key_2024';

  /// Verify app signature and integrity
  static Future<bool> verifyAppSignature() async {
    try {
      if (kDebugMode) {
        // Skip signature verification in debug mode
        return true;
      }

      final signature = await _getAppSignature();
      final isValid = _validateSignature(signature);

      if (!isValid) {
        await _handleSignatureFailure();
      }

      return isValid;
    } catch (e) {
      debugPrint('Error verifying app signature: $e');
      return false;
    }
  }

  /// Get app signature based on platform
  static Future<String?> _getAppSignature() async {
    try {
      if (PlatformUtils.isAndroid) {
        return await _getAndroidSignature();
      } else if (PlatformUtils.isIOS) {
        return await _getIOSSignature();
      } else {
        return await _getGenericSignature();
      }
    } catch (e) {
      debugPrint('Error getting app signature: $e');
      return null;
    }
  }

  /// Get Android app signature
  static Future<String?> _getAndroidSignature() async {
    try {
      // Use method channel to get Android signature
      const platform = MethodChannel('com.mohamedrady.hmdeviceerrors/signature');
      final signature = await platform.invokeMethod('getSignature');
      return signature as String?;
    } catch (e) {
      debugPrint('Error getting Android signature: $e');
      // Fallback to generic signature
      return await _getGenericSignature();
    }
  }

  /// Get iOS app signature
  static Future<String?> _getIOSSignature() async {
    try {
      // Use method channel to get iOS signature
      const platform = MethodChannel('com.mohamedrady.hmdeviceerrors/signature');
      final signature = await platform.invokeMethod('getSignature');
      return signature as String?;
    } catch (e) {
      debugPrint('Error getting iOS signature: $e');
      // Fallback to generic signature
      return await _getGenericSignature();
    }
  }

  /// Get generic signature for other platforms
  static Future<String> _getGenericSignature() async {
    try {
      // Create a signature based on app characteristics
      final signatureData = {
        'app_id': 'com.mohamedrady.hmdeviceerrors',
        'platform': PlatformUtils.operatingSystem,
        'secret': _appSecret,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      final jsonString = jsonEncode(signatureData);
      final bytes = utf8.encode(jsonString);
      final digest = sha256.convert(bytes);

      return digest.toString();
    } catch (e) {
      debugPrint('Error generating generic signature: $e');
      return 'fallback_signature';
    }
  }

  /// Validate signature
  static bool _validateSignature(String? signature) {
    if (signature == null) {
      return false;
    }

    // In debug mode, always return true
    if (kDebugMode) {
      return true;
    }

    // For now, we'll use a simple validation
    // In production, this should be more sophisticated
    return signature.isNotEmpty && signature.length >= 32;
  }

  /// Handle signature failure
  static Future<void> _handleSignatureFailure() async {
    debugPrint('SECURITY ALERT: App signature verification failed!');

    // In production, you might want to:
    // - Log the incident to server
    // - Disable app functionality
    // - Show security warning
    // - Exit the app

    if (kReleaseMode) {
      // In release mode, take more serious action
      await _reportSecurityIncident();
    }
  }

  /// Report security incident
  static Future<void> _reportSecurityIncident() async {
    try {
      // In production, send this to your security monitoring system
      final incident = {
        'type': 'signature_verification_failed',
        'timestamp': DateTime.now().toIso8601String(),
        'platform': PlatformUtils.operatingSystem,
        'app_version': '1.0.0', // Get from package info
      };

      debugPrint('Security incident: ${jsonEncode(incident)}');

      // Send to Firebase Firestore for security monitoring
      await _sendSecurityIncidentToFirestore(incident);
    } catch (e) {
      debugPrint('Error reporting security incident: $e');
    }
  }

  /// Generate app certificate
  static Future<String> generateAppCertificate() async {
    try {
      final signature = await _getAppSignature();
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      final certificateData = {
        'signature': signature,
        'timestamp': timestamp,
        'app_id': 'com.mohamedrady.hmdeviceerrors',
        'version': '1.0.0',
        'platform': PlatformUtils.operatingSystem,
        'secret_hash': _hashSecret(),
      };

      final jsonString = jsonEncode(certificateData);
      final bytes = utf8.encode(jsonString);
      final digest = sha256.convert(bytes);

      return digest.toString();
    } catch (e) {
      debugPrint('Error generating app certificate: $e');
      rethrow;
    }
  }

  /// Hash the app secret
  static String _hashSecret() {
    final bytes = utf8.encode(_appSecret);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Verify app certificate
  static Future<bool> verifyAppCertificate(String certificate) async {
    try {
      final currentCertificate = await generateAppCertificate();
      return certificate == currentCertificate;
    } catch (e) {
      debugPrint('Error verifying app certificate: $e');
      return false;
    }
  }

  /// Check if app is tampered
  static Future<bool> isAppTampered() async {
    try {
      // Check multiple indicators of tampering
      final checks = await Future.wait([
        _checkSignature(),
        _checkDebugMode(),
        _checkRootAccess(),
        _checkEmulator(),
      ]);

      // If any check fails, consider app tampered
      return checks.any((check) => !check);
    } catch (e) {
      debugPrint('Error checking app tampering: $e');
      return true; // Assume tampered if we can't check
    }
  }

  /// Check signature validity
  static Future<bool> _checkSignature() async {
    return await verifyAppSignature();
  }

  /// Check if running in debug mode
  static Future<bool> _checkDebugMode() async {
    // In production, debug mode should be false
    return kReleaseMode || kDebugMode; // Allow debug mode for development
  }

  /// Check for root access (Android) or jailbreak (iOS)
  static Future<bool> _checkRootAccess() async {
    try {
      if (PlatformUtils.isAndroid) {
        return await _checkAndroidRoot();
      } else if (PlatformUtils.isIOS) {
        return await _checkIOSJailbreak();
      }
      return true; // Assume safe for other platforms
    } catch (e) {
      debugPrint('Error checking root access: $e');
      return false;
    }
  }

  /// Check for Android root
  static Future<bool> _checkAndroidRoot() async {
    try {
      // Check for common root indicators
      final rootPaths = [
        '/system/app/Superuser.apk',
        '/sbin/su',
        '/system/bin/su',
        '/system/xbin/su',
        '/data/local/xbin/su',
        '/data/local/bin/su',
        '/system/sd/xbin/su',
        '/system/bin/failsafe/su',
        '/data/local/su',
      ];

      for (final path in rootPaths) {
        if (await File(path).exists()) {
          return false; // Root detected
        }
      }

      return true; // No root detected
    } catch (e) {
      debugPrint('Error checking Android root: $e');
      return false;
    }
  }

  /// Check for iOS jailbreak
  static Future<bool> _checkIOSJailbreak() async {
    try {
      // Check for common jailbreak indicators
      final jailbreakPaths = [
        '/Applications/Cydia.app',
        '/Library/MobileSubstrate/MobileSubstrate.dylib',
        '/bin/bash',
        '/usr/sbin/sshd',
        '/etc/apt',
        '/private/var/lib/apt/',
      ];

      for (final path in jailbreakPaths) {
        if (await File(path).exists()) {
          return false; // Jailbreak detected
        }
      }

      return true; // No jailbreak detected
    } catch (e) {
      debugPrint('Error checking iOS jailbreak: $e');
      return false;
    }
  }

  /// Check if running on emulator
  static Future<bool> _checkEmulator() async {
    try {
      if (PlatformUtils.isAndroid) {
        return await _checkAndroidEmulator();
      } else if (PlatformUtils.isIOS) {
        return await _checkIOSSimulator();
      }
      return true; // Assume real device for other platforms
    } catch (e) {
      debugPrint('Error checking emulator: $e');
      return false;
    }
  }

  /// Check for Android emulator
  static Future<bool> _checkAndroidEmulator() async {
    try {
      // Use method channel to check emulator indicators
      const platform = MethodChannel('com.mohamedrady.hmdeviceerrors/security');
      final isEmulator = await platform.invokeMethod('isEmulator');
      return !(isEmulator as bool? ?? false);
    } catch (e) {
      debugPrint('Error checking Android emulator: $e');
      return true; // Assume real device if we can't check
    }
  }

  /// Check for iOS simulator
  static Future<bool> _checkIOSSimulator() async {
    try {
      // Use method channel to check simulator
      const platform = MethodChannel('com.mohamedrady.hmdeviceerrors/security');
      final isSimulator = await platform.invokeMethod('isSimulator');
      return !(isSimulator as bool? ?? false);
    } catch (e) {
      debugPrint('Error checking iOS simulator: $e');
      return true; // Assume real device if we can't check
    }
  }

  /// Get security report
  static Future<Map<String, dynamic>> getSecurityReport() async {
    try {
      final signature = await _getAppSignature();
      final isSignatureValid = _validateSignature(signature);
      final isTampered = await isAppTampered();

      return {
        'signature': signature,
        'signature_valid': isSignatureValid,
        'is_tampered': isTampered,
        'is_debug_mode': kDebugMode,
        'is_release_mode': kReleaseMode,
        'platform': PlatformUtils.operatingSystem,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('Error generating security report: $e');
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Send security incident to Firestore
  static Future<void> _sendSecurityIncidentToFirestore(Map<String, dynamic> incident) async {
    try {
      final firestore = FirebaseFirestore.instance;

      // Add additional device information
      final enhancedIncident = {
        ...incident,
        'device_id': await _getDeviceId(),
        'app_signature': await _getAppSignature(),
        'security_report': await getSecurityReport(),
      };

      // Store in security_incidents collection
      await firestore.collection('security_incidents').add(enhancedIncident);

      debugPrint('Security incident sent to Firestore successfully');
    } catch (e) {
      debugPrint('Error sending security incident to Firestore: $e');
      // Don't rethrow to avoid breaking the app
    }
  }

  /// Get device ID for tracking
  static Future<String> _getDeviceId() async {
    try {
      // Use method channel to get device ID
      const platform = MethodChannel('com.mohamedrady.hmdeviceerrors/device');
      final deviceId = await platform.invokeMethod('getDeviceId');
      return deviceId as String? ?? 'unknown_device';
    } catch (e) {
      debugPrint('Error getting device ID: $e');
      return 'unknown_device';
    }
  }
}
