{"buildFiles": ["C:\\Users\\<USER>\\dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\work\\HMDeviceErrors\\hm_device_errors\\android\\app\\.cxx\\Debug\\4h1s2r57\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\work\\HMDeviceErrors\\hm_device_errors\\android\\app\\.cxx\\Debug\\4h1s2r57\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}