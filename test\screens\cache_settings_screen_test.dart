import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import '../../lib/screens/settings/cache_settings_screen.dart';
import '../../lib/models/cache_settings_model.dart';
import '../../lib/services/attachment_cache_service.dart';
import '../../lib/services/video_thumbnail_service.dart';

// Generate mocks
@GenerateMocks([
  AttachmentCacheService,
  VideoThumbnailService,
  CacheSettingsService,
])
import 'cache_settings_screen_test.mocks.dart';

void main() {
  group('CacheSettingsScreen Tests', () {
    late MockAttachmentCacheService mockCacheService;
    late MockVideoThumbnailService mockVideoService;
    late MockCacheSettingsService mockSettingsService;

    setUp(() {
      mockCacheService = MockAttachmentCacheService();
      mockVideoService = MockVideoThumbnailService();
      mockSettingsService = MockCacheSettingsService();
    });

    testWidgets('should display all three tabs', (WidgetTester tester) async {
      // Arrange
      when(mockSettingsService.loadSettings()).thenAnswer(
        (_) async => const CacheSettingsModel(),
      );
      when(mockCacheService.getCacheInfo()).thenAnswer(
        (_) async => CacheInfo(fileCount: 10, totalSize: 1024 * 1024),
      );
      when(mockVideoService.getThumbnailCacheSize()).thenAnswer(
        (_) async => 512 * 1024,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: const CacheSettingsScreen(),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('الإحصائيات'), findsOneWidget);
      expect(find.text('الإعدادات'), findsOneWidget);
      expect(find.text('الأدوات'), findsOneWidget);
    });

    testWidgets('should navigate between tabs correctly', (WidgetTester tester) async {
      // Arrange
      when(mockSettingsService.loadSettings()).thenAnswer(
        (_) async => const CacheSettingsModel(),
      );
      when(mockCacheService.getCacheInfo()).thenAnswer(
        (_) async => CacheInfo(fileCount: 10, totalSize: 1024 * 1024),
      );
      when(mockVideoService.getThumbnailCacheSize()).thenAnswer(
        (_) async => 512 * 1024,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: const CacheSettingsScreen(),
        ),
      );
      await tester.pumpAndSettle();

      // Navigate to settings tab
      await tester.tap(find.text('الإعدادات'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('إعدادات الحجم'), findsOneWidget);
      expect(find.text('إعدادات الاحتفاظ'), findsOneWidget);
      expect(find.text('إعدادات الصور المصغرة'), findsOneWidget);
      expect(find.text('إعدادات الشبكة'), findsOneWidget);
    });

    testWidgets('should display cache statistics correctly', (WidgetTester tester) async {
      // Arrange
      when(mockSettingsService.loadSettings()).thenAnswer(
        (_) async => const CacheSettingsModel(),
      );
      when(mockCacheService.getCacheInfo()).thenAnswer(
        (_) async => CacheInfo(fileCount: 25, totalSize: 50 * 1024 * 1024),
      );
      when(mockVideoService.getThumbnailCacheSize()).thenAnswer(
        (_) async => 10 * 1024 * 1024,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: const CacheSettingsScreen(),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('إحصائيات الكاش'), findsOneWidget);
      expect(find.text('25'), findsOneWidget); // File count
      expect(find.textContaining('47.7 ميجابايت'), findsOneWidget); // Total size
    });

    testWidgets('should update cache size setting', (WidgetTester tester) async {
      // Arrange
      const initialSettings = CacheSettingsModel(maxCacheSizeMB: 200);
      when(mockSettingsService.loadSettings()).thenAnswer(
        (_) async => initialSettings,
      );
      when(mockCacheService.getCacheInfo()).thenAnswer(
        (_) async => CacheInfo(fileCount: 10, totalSize: 1024 * 1024),
      );
      when(mockVideoService.getThumbnailCacheSize()).thenAnswer(
        (_) async => 512 * 1024,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: const CacheSettingsScreen(),
        ),
      );
      await tester.pumpAndSettle();

      // Navigate to settings tab
      await tester.tap(find.text('الإعدادات'));
      await tester.pumpAndSettle();

      // Find and interact with cache size slider
      final slider = find.byType(Slider).first;
      await tester.drag(slider, const Offset(100, 0));
      await tester.pumpAndSettle();

      // Assert - the slider value should have changed
      expect(find.text('200 MB'), findsNothing);
    });

    testWidgets('should toggle WiFi-only download setting', (WidgetTester tester) async {
      // Arrange
      const initialSettings = CacheSettingsModel(wifiOnlyDownload: false);
      when(mockSettingsService.loadSettings()).thenAnswer(
        (_) async => initialSettings,
      );
      when(mockCacheService.getCacheInfo()).thenAnswer(
        (_) async => CacheInfo(fileCount: 10, totalSize: 1024 * 1024),
      );
      when(mockVideoService.getThumbnailCacheSize()).thenAnswer(
        (_) async => 512 * 1024,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: const CacheSettingsScreen(),
        ),
      );
      await tester.pumpAndSettle();

      // Navigate to settings tab
      await tester.tap(find.text('الإعدادات'));
      await tester.pumpAndSettle();

      // Find and toggle WiFi-only switch
      final wifiSwitch = find.widgetWithText(SwitchListTile, 'التحميل على WiFi فقط');
      await tester.tap(wifiSwitch);
      await tester.pumpAndSettle();

      // Assert - switch should be toggled
      final switchWidget = tester.widget<SwitchListTile>(wifiSwitch);
      expect(switchWidget.value, true);
    });

    testWidgets('should show clear cache confirmation dialog', (WidgetTester tester) async {
      // Arrange
      when(mockSettingsService.loadSettings()).thenAnswer(
        (_) async => const CacheSettingsModel(),
      );
      when(mockCacheService.getCacheInfo()).thenAnswer(
        (_) async => CacheInfo(fileCount: 10, totalSize: 1024 * 1024),
      );
      when(mockVideoService.getThumbnailCacheSize()).thenAnswer(
        (_) async => 512 * 1024,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: const CacheSettingsScreen(),
        ),
      );
      await tester.pumpAndSettle();

      // Navigate to tools tab
      await tester.tap(find.text('الأدوات'));
      await tester.pumpAndSettle();

      // Tap clear cache button
      await tester.tap(find.text('مسح الكاش'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('مسح الكاش'), findsWidgets);
      expect(find.text('هل أنت متأكد من مسح جميع الملفات المخزنة مؤقتاً؟'), findsOneWidget);
    });

    testWidgets('should show optimize cache button', (WidgetTester tester) async {
      // Arrange
      when(mockSettingsService.loadSettings()).thenAnswer(
        (_) async => const CacheSettingsModel(),
      );
      when(mockCacheService.getCacheInfo()).thenAnswer(
        (_) async => CacheInfo(fileCount: 10, totalSize: 1024 * 1024),
      );
      when(mockVideoService.getThumbnailCacheSize()).thenAnswer(
        (_) async => 512 * 1024,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: const CacheSettingsScreen(),
        ),
      );
      await tester.pumpAndSettle();

      // Navigate to tools tab
      await tester.tap(find.text('الأدوات'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('تحسين الكاش'), findsOneWidget);
    });

    testWidgets('should display scheduled cleanup settings', (WidgetTester tester) async {
      // Arrange
      const initialSettings = CacheSettingsModel(
        enableScheduledCleanup: true,
        cleanupIntervalDays: 3,
      );
      when(mockSettingsService.loadSettings()).thenAnswer(
        (_) async => initialSettings,
      );
      when(mockCacheService.getCacheInfo()).thenAnswer(
        (_) async => CacheInfo(fileCount: 10, totalSize: 1024 * 1024),
      );
      when(mockVideoService.getThumbnailCacheSize()).thenAnswer(
        (_) async => 512 * 1024,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: const CacheSettingsScreen(),
        ),
      );
      await tester.pumpAndSettle();

      // Navigate to tools tab
      await tester.tap(find.text('الأدوات'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('الصيانة التلقائية'), findsOneWidget);
      expect(find.textContaining('تنظيف تلقائي كل 3 أيام'), findsOneWidget);
    });
  });
}

// Helper class for mock data
class CacheInfo {
  final int fileCount;
  final int totalSize;

  CacheInfo({required this.fileCount, required this.totalSize});
}
