import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:hm_device_errors/screens/settings/cache_settings_screen.dart';
import 'package:hm_device_errors/models/cache_settings_model.dart';

void main() {
  group('CacheSettingsScreen Tests', () {
    testWidgets('should display cache settings screen', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: const CacheSettingsScreen(),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('إعدادات الكاش المتقدمة'), findsOneWidget);
    });

    testWidgets('should display all three tabs', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: const CacheSettingsScreen(),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('الإحصائيات'), findsOneWidget);
      expect(find.text('الإعدادات'), findsOneWidget);
      expect(find.text('الأدوات'), findsOneWidget);
    });

    testWidgets('should navigate between tabs correctly', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: const CacheSettingsScreen(),
        ),
      );
      await tester.pumpAndSettle();

      // Navigate to settings tab
      await tester.tap(find.text('الإعدادات'));
      await tester.pumpAndSettle();

      // Assert - should show settings content
      expect(find.text('إعدادات الحجم'), findsOneWidget);
    });

    testWidgets('should show tools tab content', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: const CacheSettingsScreen(),
        ),
      );
      await tester.pumpAndSettle();

      // Navigate to tools tab
      await tester.tap(find.text('الأدوات'));
      await tester.pumpAndSettle();

      // Assert - should show tools content
      expect(find.text('إجراءات سريعة'), findsOneWidget);
    });

    testWidgets('should display cache settings model correctly', (WidgetTester tester) async {
      // Test CacheSettingsModel
      const settings = CacheSettingsModel(
        maxCacheSizeMB: 300,
        retentionDays: 7,
        autoCleanup: true,
        enableThumbnails: true,
        enableVideoThumbnails: false,
        thumbnailQuality: 80,
        wifiOnlyDownload: true,
        autoCompressImages: false,
        enableScheduledCleanup: true,
        cleanupIntervalDays: 5,
      );

      // Assert
      expect(settings.maxCacheSizeMB, 300);
      expect(settings.retentionDays, 7);
      expect(settings.wifiOnlyDownload, true);
      expect(settings.cleanupIntervalDays, 5);
    });

    testWidgets('should test CacheSettingsModel copyWith method', (WidgetTester tester) async {
      // Test copyWith functionality
      const originalSettings = CacheSettingsModel(
        maxCacheSizeMB: 200,
        retentionDays: 14,
        wifiOnlyDownload: false,
      );

      final updatedSettings = originalSettings.copyWith(
        maxCacheSizeMB: 500,
        wifiOnlyDownload: true,
      );

      // Assert
      expect(updatedSettings.maxCacheSizeMB, 500);
      expect(updatedSettings.retentionDays, 14); // Should remain unchanged
      expect(updatedSettings.wifiOnlyDownload, true);
    });

    testWidgets('should test CacheSettingsModel toMap and fromMap', (WidgetTester tester) async {
      // Test serialization
      const settings = CacheSettingsModel(
        maxCacheSizeMB: 300,
        retentionDays: 7,
        autoCleanup: true,
        enableThumbnails: false,
        wifiOnlyDownload: true,
        cleanupIntervalDays: 5,
      );

      final map = settings.toMap();
      final reconstructedSettings = CacheSettingsModel.fromMap(map);

      // Assert
      expect(reconstructedSettings.maxCacheSizeMB, settings.maxCacheSizeMB);
      expect(reconstructedSettings.retentionDays, settings.retentionDays);
      expect(reconstructedSettings.autoCleanup, settings.autoCleanup);
      expect(reconstructedSettings.enableThumbnails, settings.enableThumbnails);
      expect(reconstructedSettings.wifiOnlyDownload, settings.wifiOnlyDownload);
      expect(reconstructedSettings.cleanupIntervalDays, settings.cleanupIntervalDays);
    });

    testWidgets('should test CacheStatsModel calculations', (WidgetTester tester) async {
      // Test CacheStatsModel
      final stats = CacheStatsModel(
        totalFiles: 50,
        totalSizeBytes: 100 * 1024 * 1024, // 100 MB
        thumbnailFiles: 10,
        thumbnailSizeBytes: 5 * 1024 * 1024, // 5 MB
        videoThumbnailFiles: 5,
        videoThumbnailSizeBytes: 10 * 1024 * 1024, // 10 MB
        lastCleanup: DateTime.now(),
        oldestFileAgeInDays: 15,
      );

      // Assert calculations
      expect(stats.totalSizeMB, closeTo(95.37, 0.1)); // ~100 MB
      expect(stats.thumbnailSizeMB, closeTo(4.77, 0.1)); // ~5 MB
      expect(stats.videoThumbnailSizeMB, closeTo(9.54, 0.1)); // ~10 MB

      // Test usage percentage
      final maxSize = 200 * 1024 * 1024; // 200 MB
      final usagePercentage = stats.getUsagePercentage(maxSize);
      expect(usagePercentage, closeTo(50.0, 1.0)); // ~50%
    });
  });
}
