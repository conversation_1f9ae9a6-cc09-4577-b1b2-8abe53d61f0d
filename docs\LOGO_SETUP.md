# HM Device Errors - Logo and Icon Setup

This document explains how the new HM logo has been implemented across all platforms and how to maintain it.

## Logo Design

The new logo features:
- **Letters**: "HM" in bold, modern typography
- **Colors**: Gradient from pink (#E91E63) through purple (#9C27B0) to blue (#2196F3)
- **Background**: White circular background for contrast
- **Style**: Clean, professional, and easily recognizable

## Files Updated

### Android
- `android/app/src/main/res/drawable/app_logo.xml` - Main vector logo
- `android/app/src/main/res/drawable/ic_notification.xml` - Notification icon
- `android/app/src/main/res/drawable/launch_background.xml` - Launch screen
- `android/app/src/main/res/drawable-v21/launch_background.xml` - Launch screen (API 21+)
- `android/app/src/main/res/drawable-night/launch_background.xml` - Launch screen (dark mode)
- `android/app/src/main/res/mipmap-*/ic_launcher.png` - Launcher icons (all densities)

### iOS
- `ios/Runner/Assets.xcassets/AppIcon.appiconset/` - All iOS app icons

### macOS
- `macos/Runner/Assets.xcassets/AppIcon.appiconset/` - All macOS app icons

### Web
- `web/icons/` - Web app icons
- `web/favicon.png` - Browser favicon
- `web/manifest.json` - Updated with new colors and branding

## Color Scheme

The logo uses a consistent gradient:
- **Primary Pink**: #E91E63
- **Purple**: #9C27B0  
- **Blue**: #2196F3
- **Background**: #FFFFFF (white)

## Generating Icons

### Method 1: Python Script (Recommended)
```bash
# Install dependencies
pip install Pillow numpy

# Run the generation script
python scripts/generate_icons.py
```

### Method 2: Windows Batch Script
```cmd
# Double-click or run from command prompt
scripts\generate_icons.bat
```

### Method 3: Flutter Launcher Icons Package
```bash
# Add to pubspec.yaml dev_dependencies
flutter_launcher_icons: ^0.13.1

# Run generation
flutter pub get
flutter pub run flutter_launcher_icons:main -f flutter_launcher_icons.yaml
```

## Manual Icon Creation

If you need to create icons manually:

1. **Base Size**: Start with 1024x1024px
2. **Background**: White circle with 5% margin
3. **Letters**: HM in bold font with gradient fill
4. **Export**: PNG format with transparency
5. **Resize**: Use image editing software to create required sizes

### Required Sizes

**Android:**
- 48px (mdpi), 72px (hdpi), 96px (xhdpi), 144px (xxhdpi), 192px (xxxhdpi)

**iOS:**
- 20px, 29px, 40px, 58px, 60px, 76px, 80px, 87px, 120px, 152px, 167px, 180px, 1024px

**Web:**
- 192px, 512px (regular and maskable versions)

**macOS:**
- 16px, 32px, 64px, 128px, 256px, 512px, 1024px

## Testing

After generating icons:

1. **Clean Build**: `flutter clean && flutter pub get`
2. **Build Android**: `flutter build apk` or `flutter build appbundle`
3. **Build iOS**: `flutter build ios`
4. **Test Web**: `flutter build web`

Check that icons appear correctly in:
- App launcher/home screen
- App switcher/recent apps
- Settings/app info
- Notifications
- Browser tabs (web)

## Troubleshooting

### Icons Not Updating
- Clean and rebuild the project
- Clear device cache
- Uninstall and reinstall the app

### Vector Drawable Issues (Android)
- Ensure the XML is valid
- Check that gradients are properly defined
- Verify path data is correct

### iOS Icon Issues
- Ensure all required sizes are present
- Check that PNG files are not corrupted
- Verify Contents.json references correct files

## Future Updates

To update the logo:

1. Modify `android/app/src/main/res/drawable/app_logo.xml`
2. Run the icon generation script
3. Test on all target platforms
4. Update this documentation if needed

## Brand Guidelines

- Always maintain the gradient direction (diagonal, top-left to bottom-right)
- Keep the white circular background for contrast
- Ensure the HM letters are clearly readable
- Use consistent colors across all platforms
- Maintain proper spacing and proportions
