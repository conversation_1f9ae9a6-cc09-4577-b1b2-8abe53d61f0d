import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/locale_provider.dart';
import '../../providers/permission_provider.dart';
import '../../models/permission_model.dart';

import '../../widgets/gradient_background.dart';

class RolesManagementScreen extends StatefulWidget {
  const RolesManagementScreen({super.key});

  @override
  State<RolesManagementScreen> createState() => _RolesManagementScreenState();
}

class _RolesManagementScreenState extends State<RolesManagementScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<PermissionProvider>(context, listen: false).loadRoles();
    });
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    // Check if user has permission to manage roles
    if (!authProvider.canViewUsers) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Unauthorized / غير مصرح'),
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error,
                size: 80,
                color: Colors.red,
              ),
              SizedBox(height: 16),
              Text(
                'You are not authorized to access this page\nغير مصرح لك بالوصول إلى هذه الصفحة',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 18),
              ),
            ],
          ),
        ),
      );
    }

    return GradientScaffold(
      appBar: AppBar(
        title: Text(
          isRTL ? 'إدارة الأدوار والصلاحيات' : 'Roles & Permissions Management',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: false,
        actions: [
          if (authProvider.isAdmin)
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _showCreateRoleDialog(),
              tooltip: isRTL ? 'إضافة دور جديد' : 'Add New Role',
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              Provider.of<PermissionProvider>(context, listen: false).loadRoles();
            },
            tooltip: isRTL ? 'تحديث' : 'Refresh',
          ),
        ],
      ),
      body: Consumer<PermissionProvider>(
        builder: (context, permissionProvider, child) {
          if (permissionProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (permissionProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(
                    isRTL ? 'خطأ في تحميل البيانات' : 'Error loading data',
                    style: const TextStyle(fontSize: 18),
                  ),
                  const SizedBox(height: 8),
                  Text(permissionProvider.error!),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => permissionProvider.loadRoles(),
                    child: Text(isRTL ? 'إعادة المحاولة' : 'Retry'),
                  ),
                ],
              ),
            );
          }

          if (permissionProvider.roles.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.admin_panel_settings, size: 64, color: Colors.grey),
                  const SizedBox(height: 16),
                  Text(
                    isRTL ? 'لا توجد أدوار' : 'No roles found',
                    style: const TextStyle(fontSize: 18),
                  ),
                  if (authProvider.isAdmin) ...[
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => _showCreateRoleDialog(),
                      child: Text(isRTL ? 'إضافة دور جديد' : 'Add New Role'),
                    ),
                  ],
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: permissionProvider.roles.length,
            itemBuilder: (context, index) {
              final role = permissionProvider.roles[index];
              return _buildRoleCard(role, isRTL, authProvider);
            },
          );
        },
      ),
    );
  }

  Widget _buildRoleCard(UserRole role, bool isRTL, AuthProvider authProvider) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isDark
              ? Theme.of(context).colorScheme.outline.withAlpha(120)
              : const Color(0xFF2563EB).withAlpha(100), // حدود زرقاء فاتحة
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
              ? Colors.black.withAlpha(120)
              : const Color(0xFF2563EB).withAlpha(40), // ظل أزرق فاتح
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: isDark
              ? Colors.black.withAlpha(60)
              : const Color(0xFF2563EB).withAlpha(20), // ظل ثانوي
            blurRadius: 6,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Card(
        elevation: 0,
        margin: EdgeInsets.zero,
        color: isDark
            ? Theme.of(context).colorScheme.surface
            : const Color(0xFFF8FAFF), // خلفية زرقاء فاتحة جداً
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: role.isSystemRole
                        ? Theme.of(context).colorScheme.primary.withAlpha(25)
                        : Theme.of(context).colorScheme.secondary.withAlpha(25),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      role.isSystemRole ? Icons.security : Icons.admin_panel_settings,
                      color: role.isSystemRole
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.secondary,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isRTL ? role.nameAr : role.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          isRTL ? role.descriptionAr : role.description,
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface.withAlpha(178),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (!role.isSystemRole && authProvider.isAdmin) ...[
                    IconButton(
                      icon: const Icon(Icons.edit),
                      onPressed: () => _showEditRoleDialog(role),
                      tooltip: isRTL ? 'تعديل' : 'Edit',
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete),
                      color: Theme.of(context).colorScheme.error,
                      onPressed: () => _showDeleteRoleDialog(role),
                      tooltip: isRTL ? 'حذف' : 'Delete',
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 16),

              // Role type badge
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: role.isSystemRole
                      ? Colors.blue.withAlpha(25)
                      : Colors.green.withAlpha(25),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  role.isSystemRole
                      ? (isRTL ? 'دور النظام' : 'System Role')
                      : (isRTL ? 'دور مخصص' : 'Custom Role'),
                  style: TextStyle(
                    color: role.isSystemRole ? Colors.blue : Colors.green,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              const SizedBox(height: 12),

              // Permissions count
              Text(
                isRTL
                    ? 'عدد الصلاحيات: ${role.permissions.length}'
                    : 'Permissions: ${role.permissions.length}',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface.withAlpha(178),
                  fontSize: 14,
                ),
              ),

              const SizedBox(height: 8),

              // View permissions button
              TextButton.icon(
                icon: const Icon(Icons.visibility),
                label: Text(isRTL ? 'عرض الصلاحيات' : 'View Permissions'),
                onPressed: () => _showPermissionsDialog(role),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showCreateRoleDialog() {
    // Implementation will be added
    _showNotImplementedDialog('Create Role');
  }

  void _showEditRoleDialog(UserRole role) {
    // Implementation will be added
    _showNotImplementedDialog('Edit Role');
  }

  void _showDeleteRoleDialog(UserRole role) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'تأكيد الحذف' : 'Confirm Delete'),
        content: Text(
          isRTL
              ? 'هل أنت متأكد من حذف الدور "${role.nameAr}"؟'
              : 'Are you sure you want to delete the role "${role.name}"?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              navigator.pop();
              final success = await Provider.of<PermissionProvider>(context, listen: false)
                  .deleteRole(role.id);

              if (mounted) {
                scaffoldMessenger.showSnackBar(
                  SnackBar(
                    content: Text(
                      success
                          ? (isRTL ? 'تم حذف الدور بنجاح' : 'Role deleted successfully')
                          : (isRTL ? 'فشل في حذف الدور' : 'Failed to delete role'),
                    ),
                    backgroundColor: success ? Colors.green : Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(isRTL ? 'حذف' : 'Delete'),
          ),
        ],
      ),
    );
  }

  void _showPermissionsDialog(UserRole role) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          isRTL
              ? 'صلاحيات ${role.nameAr}'
              : '${role.name} Permissions',
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: role.permissions.isEmpty
              ? Center(
                  child: Text(
                    isRTL ? 'لا توجد صلاحيات' : 'No permissions',
                    style: const TextStyle(fontSize: 16),
                  ),
                )
              : ListView.builder(
                  itemCount: role.permissions.length,
                  itemBuilder: (context, index) {
                    final permission = role.permissions[index];
                    return ListTile(
                      leading: const Icon(Icons.check_circle, color: Colors.green),
                      title: Text(
                        PermissionHelper.getPermissionName(permission, isRTL),
                      ),
                      subtitle: Text(
                        PermissionHelper.getPermissionCategory(permission, isRTL),
                      ),
                    );
                  },
                ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'إغلاق' : 'Close'),
          ),
        ],
      ),
    );
  }

  void _showNotImplementedDialog(String feature) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'قريباً' : 'Coming Soon'),
        content: Text(
          isRTL
              ? 'ستتم إضافة هذه الميزة قريباً: $feature'
              : 'This feature will be added soon: $feature',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'موافق' : 'OK'),
          ),
        ],
      ),
    );
  }
}
