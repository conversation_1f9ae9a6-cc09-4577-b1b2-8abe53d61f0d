import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/error_provider.dart';
import '../../providers/category_provider.dart';
import '../../providers/manufacturer_provider.dart';
import '../../providers/locale_provider.dart';
import '../../widgets/enhanced_error_card.dart';
import '../../widgets/gradient_background.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final _searchController = TextEditingController();
  bool _hasSearched = false;
  bool _showAdvancedFilters = false;

  // Advanced filter variables
  String? _selectedCategory;
  String? _selectedManufacturer;
  String? _selectedModel;
  String _searchType = 'all'; // all, manufacturer, model, errorCode

  @override
  void initState() {
    super.initState();
    // Initialize providers
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        Provider.of<CategoryProvider>(context, listen: false).fetchCategories(forceRefresh: false);
        Provider.of<ManufacturerProvider>(context, listen: false).fetchManufacturers();
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _performSearch() {
    final query = _searchController.text.trim();
    if (query.isNotEmpty || _hasAdvancedFilters()) {
      setState(() {
        _hasSearched = true;
      });

      // Perform advanced search with filters
      Provider.of<ErrorProvider>(context, listen: false).searchErrorsAdvanced(
        query: query.isNotEmpty ? query : null,
        categoryId: _selectedCategory,
        manufacturer: _selectedManufacturer,
        model: _selectedModel,
        searchType: _searchType,
      );
    }
  }

  bool _hasAdvancedFilters() {
    return _selectedCategory != null ||
           _selectedManufacturer != null ||
           _selectedModel != null;
  }

  void _clearFilters() {
    setState(() {
      _selectedCategory = null;
      _selectedManufacturer = null;
      _selectedModel = null;
      _searchType = 'all';
      _searchController.clear();
      _hasSearched = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    return GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        resizeToAvoidBottomInset: false,
        body: LayoutBuilder(
          builder: (context, constraints) {
            return CustomScrollView(
              physics: const ClampingScrollPhysics(),
              slivers: [
                // Search header as sliver
                SliverToBoxAdapter(
                  child: Container(
                    padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 12.0),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: SafeArea(
                      bottom: false,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Main search bar
                          SizedBox(
                            height: 48,
                            child: TextField(
                              controller: _searchController,
                              decoration: InputDecoration(
                                hintText: isRTL ? 'ابحث عن عطل...' : 'Search for errors...',
                                hintStyle: const TextStyle(fontSize: 16),
                                prefixIcon: const Icon(Icons.search, size: 22),
                                suffixIcon: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    IconButton(
                                      icon: Icon(
                                        _showAdvancedFilters ? Icons.filter_list : Icons.filter_list_off,
                                        size: 24,
                                        color: _showAdvancedFilters
                                            ? Theme.of(context).colorScheme.primary
                                            : null,
                                      ),
                                      onPressed: () {
                                        setState(() {
                                          _showAdvancedFilters = !_showAdvancedFilters;
                                        });
                                      },
                                      tooltip: isRTL ? 'فلاتر متقدمة' : 'Advanced Filters',
                                    ),
                                    IconButton(
                                      icon: const Icon(Icons.clear, size: 22),
                                      onPressed: _clearFilters,
                                      tooltip: isRTL ? 'مسح' : 'Clear',
                                    ),
                                  ],
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(24),
                                  borderSide: BorderSide(
                                    color: Theme.of(context).colorScheme.outline.withAlpha(100),
                                  ),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(24),
                                  borderSide: BorderSide(
                                    color: Theme.of(context).colorScheme.outline.withAlpha(100),
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(24),
                                  borderSide: BorderSide(
                                    color: Theme.of(context).colorScheme.primary,
                                    width: 2,
                                  ),
                                ),
                                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                              ),
                              onSubmitted: (_) => _performSearch(),
                            ),
                          ),

                          const SizedBox(height: 12),

                          // Advanced filters
                          if (_showAdvancedFilters)
                            _buildAdvancedFilters(),

                          // Search buttons
                          Padding(
                            padding: const EdgeInsets.only(top: 12),
                            child: Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: _performSearch,
                                    icon: const Icon(Icons.search, size: 20),
                                    label: Text(
                                      isRTL ? 'بحث' : 'Search',
                                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                                    ),
                                    style: ElevatedButton.styleFrom(
                                      minimumSize: const Size.fromHeight(48),
                                      padding: const EdgeInsets.symmetric(vertical: 14),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      elevation: 2,
                                    ),
                                  ),
                                ),
                                if (_hasAdvancedFilters() || _hasSearched) ...[
                                  const SizedBox(width: 12),
                                  Expanded(
                                    flex: 1,
                                    child: ElevatedButton.icon(
                                      onPressed: _clearFilters,
                                      icon: const Icon(Icons.clear_all, size: 20),
                                      label: Text(
                                        isRTL ? 'مسح' : 'Clear',
                                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                                      ),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Theme.of(context).colorScheme.secondary,
                                        foregroundColor: Theme.of(context).colorScheme.onSecondary,
                                        minimumSize: const Size.fromHeight(48),
                                        padding: const EdgeInsets.symmetric(vertical: 14),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                        elevation: 2,
                                      ),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // Results as sliver
                _hasSearched
                    ? _buildSearchResultsSliver()
                    : SliverFillRemaining(
                        child: _buildSearchPlaceholder(),
                      ),

                // Add bottom padding to prevent overflow
                const SliverToBoxAdapter(
                  child: SizedBox(),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildSearchTypeChip(
    BuildContext context, {
    required String label,
    required String value,
    required bool isSelected,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: () {
        setState(() {
          _searchType = value;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : (isDarkMode
                  ? Theme.of(context).colorScheme.surface
                  : Theme.of(context).colorScheme.surfaceContainerHighest),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline.withAlpha(128),
            width: 1.5,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: Theme.of(context).colorScheme.primary.withAlpha(76),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            color: isSelected
                ? Theme.of(context).colorScheme.onPrimary
                : Theme.of(context).colorScheme.onSurface,
          ),
        ),
      ),
    );
  }

  Widget _buildAdvancedFilters() {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;
    final categoryProvider = Provider.of<CategoryProvider>(context);
    final manufacturerProvider = Provider.of<ManufacturerProvider>(context);

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Theme.of(context).colorScheme.surface
            : Theme.of(context).colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withAlpha(102),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withAlpha(51)
                : Colors.grey.withAlpha(51),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(
                Icons.filter_list,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                isRTL ? 'فلاتر متقدمة' : 'Advanced Filters',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Search type selector
          Text(
            isRTL ? 'نوع البحث:' : 'Search Type:',
            style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 12),
          ),
          const SizedBox(height: 8),
          // Horizontal row of search type options
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildSearchTypeChip(
                  context,
                  label: isRTL ? 'الكل' : 'All',
                  value: 'all',
                  isSelected: _searchType == 'all',
                ),
                const SizedBox(width: 8),
                _buildSearchTypeChip(
                  context,
                  label: isRTL ? 'الشركة المصنعة' : 'Manufacturer',
                  value: 'manufacturer',
                  isSelected: _searchType == 'manufacturer',
                ),
                const SizedBox(width: 8),
                _buildSearchTypeChip(
                  context,
                  label: isRTL ? 'الموديل' : 'Model',
                  value: 'model',
                  isSelected: _searchType == 'model',
                ),
                const SizedBox(width: 8),
                _buildSearchTypeChip(
                  context,
                  label: isRTL ? 'كود الخطأ' : 'Error Code',
                  value: 'errorCode',
                  isSelected: _searchType == 'errorCode',
                ),
              ],
            ),
          ),

          const SizedBox(height: 8),

          // Category filter
          categoryProvider.isLoading
              ? Container(
                  height: 44,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const SizedBox(
                          width: 14,
                          height: 14,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        const SizedBox(width: 6),
                        Text(
                          isRTL ? 'جاري تحميل الفئات...' : 'Loading categories...',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                )
              : SizedBox(
                  height: 48,
                  child: DropdownButtonFormField<String>(
                    decoration: InputDecoration(
                      labelText: isRTL ? 'الفئة' : 'Category',
                      labelStyle: const TextStyle(fontSize: 13),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: Theme.of(context).colorScheme.outline.withAlpha(128),
                          width: 1.5,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: Theme.of(context).colorScheme.outline.withAlpha(128),
                          width: 1.5,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: Theme.of(context).colorScheme.primary,
                          width: 2,
                        ),
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                      isDense: true,
                      filled: true,
                      fillColor: isDarkMode
                          ? Theme.of(context).colorScheme.surfaceContainerHighest
                          : Theme.of(context).colorScheme.surfaceContainerLow,
                    ),
                    value: _selectedCategory,
                    items: [
                      DropdownMenuItem<String>(
                        value: null,
                        child: Text(
                          isRTL ? 'جميع الفئات' : 'All Categories',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      ...categoryProvider.categories.map((category) {
                        return DropdownMenuItem<String>(
                          value: category.id,
                          child: Text(
                            category.name.isNotEmpty && !categoryProvider.isFirebaseId(category.name)
                                ? category.name
                                : 'فئة غير محددة',
                            style: const TextStyle(fontSize: 12),
                          ),
                        );
                      }),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedCategory = value;
                      });
                    },
                  ),
                ),

          const SizedBox(height: 8),

          // Manufacturer filter
          SizedBox(
            height: 48,
            child: DropdownButtonFormField<String>(
              decoration: InputDecoration(
                labelText: isRTL ? 'الشركة المصنعة' : 'Manufacturer',
                labelStyle: const TextStyle(fontSize: 13),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.outline.withAlpha(128),
                    width: 1.5,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.outline.withAlpha(128),
                    width: 1.5,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  ),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                isDense: true,
                filled: true,
                fillColor: isDarkMode
                    ? Theme.of(context).colorScheme.surfaceContainerHighest
                    : Theme.of(context).colorScheme.surfaceContainerLow,
              ),
              value: _selectedManufacturer,
              items: [
                DropdownMenuItem<String>(
                  value: null,
                  child: Text(
                    isRTL ? 'جميع الشركات' : 'All Manufacturers',
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
                ...manufacturerProvider.manufacturers.map((manufacturer) {
                  return DropdownMenuItem<String>(
                    value: manufacturer,
                    child: Text(
                      manufacturer,
                      style: const TextStyle(fontSize: 12),
                    ),
                  );
                }),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedManufacturer = value;
                  _selectedModel = null; // Reset model when manufacturer changes
                });
                if (value != null) {
                  manufacturerProvider.fetchModelsForManufacturer(value);
                }
              },
            ),
          ),

          const SizedBox(height: 8),

          // Model filter
          SizedBox(
            height: 48,
            child: DropdownButtonFormField<String>(
              decoration: InputDecoration(
                labelText: isRTL ? 'الموديل' : 'Model',
                labelStyle: const TextStyle(fontSize: 13),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.outline.withAlpha(128),
                    width: 1.5,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.outline.withAlpha(128),
                    width: 1.5,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  ),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                isDense: true,
                filled: true,
                fillColor: isDarkMode
                    ? Theme.of(context).colorScheme.surfaceContainerHighest
                    : Theme.of(context).colorScheme.surfaceContainerLow,
              ),
              value: _selectedModel,
              items: [
                DropdownMenuItem<String>(
                  value: null,
                  child: Text(
                    isRTL ? 'جميع الموديلات' : 'All Models',
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
                if (_selectedManufacturer != null)
                  ...manufacturerProvider.getModelsForManufacturer(_selectedManufacturer!).map((model) {
                    return DropdownMenuItem<String>(
                      value: model,
                      child: Text(
                        model,
                        style: const TextStyle(fontSize: 12),
                      ),
                    );
                  }),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedModel = value;
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchPlaceholder() {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 32.0),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withAlpha(25),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.search,
                size: 64,
                color: Theme.of(context).colorScheme.primary.withAlpha(180),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              isRTL ? 'ابحث عن أعطال الأجهزة' : 'Search for Device Errors',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                isRTL
                    ? 'يمكنك البحث باسم الشركة، موديل الجهاز، أو كود الخطأ'
                    : 'You can search by manufacturer, device model, or error code',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
                  height: 1.4,
                ),
              ),
            ),
            const SizedBox(height: 32),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerLow,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withAlpha(76),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.tips_and_updates,
                    size: 20,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    isRTL ? 'استخدم الفلاتر للبحث المتقدم' : 'Use filters for advanced search',
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).colorScheme.onSurface.withAlpha(179),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResultsSliver() {
    final errorProvider = Provider.of<ErrorProvider>(context);
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    if (errorProvider.isLoading) {
      return const SliverFillRemaining(
        child: Center(child: CircularProgressIndicator()),
      );
    }

    if (errorProvider.error != null) {
      return SliverFillRemaining(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 32.0),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.red.withAlpha(25),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red.withAlpha(180),
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  isRTL ? 'حدث خطأ' : 'Error Occurred',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Text(
                    errorProvider.error!,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
                      height: 1.4,
                    ),
                  ),
                ),
                const SizedBox(height: 32),
                ElevatedButton.icon(
                  onPressed: _performSearch,
                  icon: const Icon(Icons.refresh, size: 20),
                  label: Text(
                    isRTL ? 'إعادة المحاولة' : 'Try Again',
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    if (errorProvider.filteredErrors.isEmpty) {
      return SliverFillRemaining(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 32.0),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.orange.withAlpha(25),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.search_off,
                    size: 64,
                    color: Colors.orange.withAlpha(180),
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  isRTL ? 'لا توجد نتائج' : 'No Results Found',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Text(
                    _searchController.text.isNotEmpty
                        ? (isRTL
                            ? 'لم يتم العثور على نتائج لـ "${_searchController.text}"'
                            : 'No results found for "${_searchController.text}"')
                        : (isRTL
                            ? 'لم يتم العثور على نتائج للفلاتر المحددة'
                            : 'No results found for the selected filters'),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
                      height: 1.4,
                    ),
                  ),
                ),
                const SizedBox(height: 32),
                ElevatedButton.icon(
                  onPressed: _clearFilters,
                  icon: const Icon(Icons.refresh, size: 20),
                  label: Text(
                    isRTL ? 'مسح الفلاتر وإعادة المحاولة' : 'Clear Filters & Try Again',
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final error = errorProvider.filteredErrors[index];
          return Padding(
            padding: EdgeInsets.fromLTRB(
              16,
              index == 0 ? 8 : 0,
              16,
              index == errorProvider.filteredErrors.length - 1 ? 24 : 12
            ),
            child: EnhancedErrorCard(
              error: error,
              isCompact: false,
            ),
          );
        },
        childCount: errorProvider.filteredErrors.length,
      ),
    );
  }
}
