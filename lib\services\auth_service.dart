import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart' show debugPrint;
import '../models/user_model.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Auth state changes stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Check user validity before sign in
  Future<UserModel?> checkUserValidity(String email) async {
    try {
      final usersSnapshot = await _firestore
          .collection('users')
          .where('email', isEqualTo: email)
          .limit(1)
          .get()
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              throw Exception('انتهت مهلة الاتصال بقاعدة البيانات. تحقق من اتصال الإنترنت');
            },
          );

      if (usersSnapshot.docs.isEmpty) {
        throw Exception('المستخدم غير موجود');
      }

      final userDoc = usersSnapshot.docs.first;
      final userData = UserModel.fromFirestore(userDoc);

      // Check if user is active
      if (!userData.isActive) {
        throw Exception('تم إيقاف حسابك. يرجى التواصل مع المدير');
      }

      // Check if user is expired
      if (userData.isExpired) {
        throw Exception('انتهت صلاحية حسابك. يرجى التواصل مع المدير لتجديد الحساب');
      }

      return userData;
    } catch (e) {
      debugPrint('Error checking user validity: $e');

      // Handle offline errors
      if (e.toString().contains('unavailable') ||
          e.toString().contains('offline') ||
          e.toString().contains('network')) {
        throw Exception('لا يمكن التحقق من صحة المستخدم. تحقق من اتصال الإنترنت وحاول مرة أخرى');
      }

      rethrow;
    }
  }

  // Sign in with email and password
  Future<UserCredential> signInWithEmailAndPassword(
      String email, String password) async {
    try {
      // First check user validity before attempting sign in
      await checkUserValidity(email);

      // If user is valid, proceed with sign in
      return await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
    } catch (e) {
      // Check if this is the main admin user and doesn't exist
      if (email == '<EMAIL>' &&
          (e.toString().contains('المستخدم غير موجود') ||
           e.toString().contains('user-not-found'))) {
        debugPrint('Main admin user not found. Creating...');

        try {
          // Create the main admin user
          await _createMainAdminUser();

          // Try to sign in again
          return await _auth.signInWithEmailAndPassword(
            email: email,
            password: password,
          );
        } catch (createError) {
          debugPrint('Error creating main admin user: $createError');
          rethrow;
        }
      }

      debugPrint('Error signing in: $e');
      rethrow;
    }
  }

  // Create main admin user (called only when needed)
  Future<void> _createMainAdminUser() async {
    const defaultAdminEmail = '<EMAIL>';
    const defaultAdminPassword = 'Admin@123456';
    const defaultAdminName = 'Mohamed Rady';
    const defaultAdminPhone = '+************';

    try {
      // Create the main admin user in Firebase Auth
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: defaultAdminEmail,
        password: defaultAdminPassword,
      ).timeout(const Duration(seconds: 15));

      if (userCredential.user != null) {
        // Create user in Firestore
        final userModel = UserModel(
          id: userCredential.user!.uid,
          email: defaultAdminEmail,
          displayName: defaultAdminName,
          phoneNumber: defaultAdminPhone,
          role: UserRole.admin,
          createdAt: DateTime.now(),
          isActive: true,
          isMainAdmin: true,
        );

        await _firestore
            .collection('users')
            .doc(userCredential.user!.uid)
            .set(userModel.toMap())
            .timeout(const Duration(seconds: 10));

        // Update display name in Firebase Auth
        await userCredential.user!.updateDisplayName(defaultAdminName)
            .timeout(const Duration(seconds: 5));

        debugPrint('Main admin user created successfully');

        // Sign out after creation
        await _auth.signOut();
      }
    } catch (e) {
      debugPrint('Error creating main admin user: $e');
      rethrow;
    }
  }

  // Check and create default admin user if needed (deprecated - kept for compatibility)
  Future<void> checkAndCreateDefaultAdmin() async {
    // This method is now deprecated and does nothing
    // Admin user creation is handled on-demand during login
    debugPrint('checkAndCreateDefaultAdmin called - admin creation is now on-demand');
  }

  // Register with email and password
  Future<UserCredential> registerWithEmailAndPassword(
      String email, String password, String? displayName) async {
    try {
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Create user in Firestore
      if (userCredential.user != null) {
        await _createUserInFirestore(
          userCredential.user!.uid,
          email,
          displayName,
        );

        // Update display name in Firebase Auth
        await userCredential.user!.updateDisplayName(displayName);
      }

      return userCredential;
    } catch (e) {
      debugPrint('Error registering: $e');
      rethrow;
    }
  }

  // Create user in Firestore
  Future<void> _createUserInFirestore(
      String uid, String email, String? displayName) async {
    try {
      // Check if this is the first user (make them admin)
      final usersSnapshot = await _firestore.collection('users').get();
      final isFirstUser = usersSnapshot.docs.isEmpty;

      final userModel = UserModel(
        id: uid,
        email: email,
        displayName: displayName,
        role: isFirstUser ? UserRole.admin : UserRole.user,
        createdAt: DateTime.now(),
        isActive: true,
      );

      await _firestore.collection('users').doc(uid).set(userModel.toMap());
    } catch (e) {
      debugPrint('Error creating user in Firestore: $e');
      rethrow;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      debugPrint('Error signing out: $e');
      rethrow;
    }
  }

  // Get user data from Firestore
  Future<UserModel?> getUserData() async {
    try {
      if (currentUser == null) return null;

      final doc = await _firestore
          .collection('users')
          .doc(currentUser!.uid)
          .get()
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              throw Exception('انتهت مهلة الاتصال بقاعدة البيانات');
            },
          );

      if (doc.exists) {
        return UserModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting user data: $e');

      // Handle offline errors gracefully
      if (e.toString().contains('unavailable') ||
          e.toString().contains('offline') ||
          e.toString().contains('network')) {
        // Return a basic user model for offline scenarios
        return UserModel(
          id: currentUser!.uid,
          email: currentUser!.email ?? '<EMAIL>',
          displayName: currentUser!.displayName,
          role: UserRole.user, // Default to user role when offline
          createdAt: DateTime.now(),
          isActive: true, // Assume active when offline
        );
      }

      return null;
    }
  }

  // Update user role
  Future<void> updateUserRole(String userId, UserRole role) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'role': role == UserRole.admin ? 'admin' : 'user',
      });
    } catch (e) {
      debugPrint('Error updating user role: $e');
      rethrow;
    }
  }

  // Get all users (admin only)
  Future<List<UserModel>> getAllUsers() async {
    try {
      final snapshot = await _firestore.collection('users').get();
      return snapshot.docs.map((doc) => UserModel.fromFirestore(doc)).toList();
    } catch (e) {
      debugPrint('Error getting all users: $e');
      return [];
    }
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      debugPrint('Error sending password reset email: $e');
      rethrow;
    }
  }

  // Change password
  Future<void> changePassword(String email, String currentPassword, String newPassword) async {
    try {
      if (currentUser == null) {
        throw Exception('لا يوجد مستخدم مسجل دخول');
      }

      // Re-authenticate the user with current password
      final credential = EmailAuthProvider.credential(
        email: email,
        password: currentPassword,
      );

      // Re-authenticate first
      await currentUser!.reauthenticateWithCredential(credential);

      // Update password
      await currentUser!.updatePassword(newPassword);

      debugPrint('Password changed successfully for user: $email');
    } catch (e) {
      debugPrint('Error changing password: $e');

      // Provide more specific error messages
      if (e.toString().contains('wrong-password') || e.toString().contains('invalid-credential')) {
        throw Exception('كلمة المرور الحالية غير صحيحة');
      } else if (e.toString().contains('weak-password')) {
        throw Exception('كلمة المرور الجديدة ضعيفة جداً');
      } else if (e.toString().contains('requires-recent-login')) {
        throw Exception('يجب تسجيل الدخول مرة أخرى لتغيير كلمة المرور');
      }

      rethrow;
    }
  }
}
