# 🔒 **تحديث قواعد Firestore - دعم Google Drive والميزات الجديدة**

## 📋 **نظرة عامة**

تم تحديث قواعد Firestore لدعم Google Drive integration والميزات الجديدة في التطبيق مع الحفاظ على أعلى مستويات الأمان.

## 🆕 **الميزات الجديدة المدعومة**

### **1. Google Drive Integration**
- ✅ **Storage Configuration**: دعم تكوين Google Drive
- ✅ **Backup Logs**: تتبع عمليات النسخ الاحتياطي
- ✅ **File Metadata**: معلومات الملفات المرفوعة
- ✅ **Sync Status**: حالة المزامنة

### **2. Remote Config Support**
- ✅ **Config Cache**: تخزين مؤقت للتكوين
- ✅ **Config Logs**: سجل تغييرات التكوين
- ✅ **Update Events**: أحداث التحديث

### **3. Enhanced Security**
- ✅ **File Validation**: التحقق من أنواع الملفات
- ✅ **Size Limits**: حدود أحجام الملفات
- ✅ **Audit Trail**: مسار التدقيق
- ✅ **Role-based Access**: التحكم بالوصول حسب الدور

## 🔧 **التحديثات الرئيسية**

### **1. Storage Configuration Rules**
```javascript
// دعم أنواع تخزين إضافية
data.configName in ['google_drive', 'onedrive', 'dropbox', 'aws_s3']

// التحقق من حقول Google Drive
(data.configName != 'google_drive' || 
 (data.keys().hasAny(['clientEmail', 'projectId']) &&
  data.clientEmail is string &&
  data.projectId is string))
```

### **2. New Collections Added**

#### **Google Drive Collections:**
- `backup_logs/{logId}` - سجلات النسخ الاحتياطي
- `google_drive_files/{fileId}` - معلومات ملفات Google Drive
- `storage_sync_status/{syncId}` - حالة المزامنة

#### **Remote Config Collections:**
- `remote_config_cache/{configKey}` - تخزين مؤقت للتكوين
- `remote_config_logs/{logId}` - سجل تغييرات Remote Config
- `app_update_events/{eventId}` - أحداث تحديث التطبيق

#### **Monitoring Collections:**
- `performance_metrics/{metricId}` - مقاييس الأداء
- `error_reports/{reportId}` - تقارير الأخطاء

### **3. Enhanced Storage Rules**

#### **File Type Validation:**
```javascript
// ملفات النسخ الاحتياطي
function isValidBackupFile() {
  return resource.contentType in ['application/json', 'application/zip', 'application/x-sqlite3'] &&
         resource.size < 100 * 1024 * 1024; // 100MB limit
}

// ملفات التصدير
function isValidExportFile() {
  return resource.contentType in ['application/json', 'text/csv', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'] &&
         resource.size < 50 * 1024 * 1024; // 50MB limit
}
```

#### **New Storage Paths:**
- `google_drive_backups/{backupId}` - نسخ احتياطية Google Drive
- `temp_uploads/{userId}/{fileId}` - رفع مؤقت للملفات
- `exports/{exportId}` - ملفات التصدير
- `sync_files/{syncId}` - ملفات المزامنة

## 🔐 **مستويات الأمان**

### **1. Authentication Levels**
- **Public**: أحداث التحديث وتقارير الأخطاء
- **Authenticated**: قراءة البيانات الأساسية
- **User**: إدارة البيانات الشخصية
- **Admin**: إدارة النظام والتكوين
- **Super Admin**: التكوين المتقدم والحذف

### **2. Data Validation**
- ✅ **Type Checking**: التحقق من أنواع البيانات
- ✅ **Size Limits**: حدود الأحجام
- ✅ **Required Fields**: الحقول المطلوبة
- ✅ **Format Validation**: التحقق من التنسيق

### **3. Audit Trail**
- ✅ **Immutable Logs**: سجلات غير قابلة للتعديل
- ✅ **User Tracking**: تتبع المستخدمين
- ✅ **Timestamp Validation**: التحقق من الوقت
- ✅ **Action Logging**: تسجيل الإجراءات

## 📊 **File Size Limits**

| نوع الملف | الحد الأقصى | الاستخدام |
|-----------|-------------|-----------|
| **Images** | 10 MB | صور الأعطال |
| **Videos** | 50 MB | فيديوهات الأعطال |
| **Documents** | 20 MB | مستندات مرفقة |
| **Backups** | 100 MB | نسخ احتياطية |
| **Exports** | 50 MB | ملفات التصدير |

## 🎯 **Access Control Matrix**

| Collection | Read | Create | Update | Delete |
|------------|------|--------|--------|--------|
| **users** | User/Admin | Admin | User(own)/Admin | Admin |
| **device_errors** | User | User | User(own)/Admin | Admin |
| **storage_config** | User | Admin | Admin | Admin |
| **backup_logs** | Admin/User(own) | Admin | ❌ | Admin |
| **google_drive_files** | Admin/User(own) | User | User(own)/Admin | User(own)/Admin |
| **remote_config_cache** | User | Admin | Admin | Admin |
| **performance_metrics** | Admin | System | ❌ | Admin |
| **error_reports** | Admin/User(own) | Anyone | ❌ | Admin |

## 🚀 **نشر القواعد**

### **1. Firebase Console**
```bash
# تسجيل الدخول إلى Firebase
firebase login

# نشر القواعد
firebase deploy --only firestore:rules

# التحقق من النشر
firebase firestore:rules:get
```

### **2. التحقق من القواعد**
```bash
# اختبار القواعد محلياً
firebase emulators:start --only firestore

# تشغيل اختبارات القواعد
npm run test:rules
```

## ⚠️ **ملاحظات مهمة**

### **1. Initial Setup Mode**
```javascript
function isInitialSetup() {
  return true; // Set to false for production
}
```
**تأكد من تعيين هذا إلى `false` في الإنتاج!**

### **2. Main Admin Protection**
- المدير الرئيسي محمي من الحذف
- البريد الإلكتروني `<EMAIL>` له صلاحيات خاصة
- لا يمكن تعديل صلاحيات المدير الرئيسي إلا من قبله

### **3. Sensitive Data**
- كلمات المرور مشفرة
- مفاتيح API محمية
- بيانات Google Drive مشفرة

## 🔍 **استكشاف الأخطاء**

### **مشاكل شائعة:**

#### **1. Permission Denied**
```
Error: Missing or insufficient permissions
```
**الحل**: تحقق من دور المستخدم وحالة الحساب

#### **2. Invalid Data**
```
Error: Document does not match required schema
```
**الحل**: تحقق من صحة البيانات المرسلة

#### **3. File Size Exceeded**
```
Error: File size exceeds maximum allowed
```
**الحل**: ضغط الملف أو تقسيمه

## 📈 **مراقبة الأداء**

### **1. Firestore Usage**
- راقب عدد القراءات/الكتابات
- تحقق من استخدام التخزين
- راقب الاستعلامات البطيئة

### **2. Security Monitoring**
- راجع سجلات الأمان
- تتبع محاولات الوصول المرفوضة
- راقب الأنشطة المشبوهة

## 🎉 **الخلاصة**

تم تحديث قواعد Firestore بنجاح لدعم:
- ✅ **Google Drive Integration**
- ✅ **Remote Config Management**
- ✅ **Enhanced Security**
- ✅ **Better File Management**
- ✅ **Comprehensive Audit Trail**

القواعد الآن جاهزة لدعم جميع ميزات التطبيق الجديدة مع الحفاظ على أعلى مستويات الأمان والأداء!
