import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/locale_provider.dart';
import '../../providers/permission_provider.dart';
import '../../providers/activity_log_provider.dart';
import '../../models/permission_model.dart';


class RoleEditorScreen extends StatefulWidget {
  final UserRole? roleToEdit;

  const RoleEditorScreen({super.key, this.roleToEdit});

  @override
  State<RoleEditorScreen> createState() => _RoleEditorScreenState();
}

class _RoleEditorScreenState extends State<RoleEditorScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _nameArController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _descriptionArController = TextEditingController();

  Set<PermissionType> _selectedPermissions = {};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.roleToEdit != null) {
      _loadRoleData();
    }
  }

  void _loadRoleData() {
    final role = widget.roleToEdit!;
    _nameController.text = role.name;
    _nameArController.text = role.nameAr;
    _descriptionController.text = role.description;
    _descriptionArController.text = role.descriptionAr;
    _selectedPermissions = Set.from(role.permissions);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _nameArController.dispose();
    _descriptionController.dispose();
    _descriptionArController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;
    final isEditing = widget.roleToEdit != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isEditing
              ? (isRTL ? 'تعديل الدور' : 'Edit Role')
              : (isRTL ? 'إنشاء دور جديد' : 'Create New Role'),
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveRole,
              tooltip: isRTL ? 'حفظ' : 'Save',
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Basic Information Card
              Card(
                elevation: 4,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            isRTL ? 'المعلومات الأساسية' : 'Basic Information',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Role Name (English)
                      TextFormField(
                        controller: _nameController,
                        decoration: InputDecoration(
                          labelText: isRTL ? 'اسم الدور (إنجليزي)' : 'Role Name (English)',
                          prefixIcon: const Icon(Icons.label),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return isRTL ? 'يرجى إدخال اسم الدور' : 'Please enter role name';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Role Name (Arabic)
                      TextFormField(
                        controller: _nameArController,
                        decoration: InputDecoration(
                          labelText: isRTL ? 'اسم الدور (عربي)' : 'Role Name (Arabic)',
                          prefixIcon: const Icon(Icons.label_outline),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return isRTL ? 'يرجى إدخال اسم الدور بالعربية' : 'Please enter Arabic role name';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Description (English)
                      TextFormField(
                        controller: _descriptionController,
                        maxLines: 3,
                        decoration: InputDecoration(
                          labelText: isRTL ? 'الوصف (إنجليزي)' : 'Description (English)',
                          prefixIcon: const Icon(Icons.description),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return isRTL ? 'يرجى إدخال وصف الدور' : 'Please enter role description';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Description (Arabic)
                      TextFormField(
                        controller: _descriptionArController,
                        maxLines: 3,
                        decoration: InputDecoration(
                          labelText: isRTL ? 'الوصف (عربي)' : 'Description (Arabic)',
                          prefixIcon: const Icon(Icons.description_outlined),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return isRTL ? 'يرجى إدخال وصف الدور بالعربية' : 'Please enter Arabic role description';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Permissions Card
              Card(
                elevation: 4,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.security,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            isRTL ? 'الصلاحيات' : 'Permissions',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            isRTL
                                ? 'المحددة: ${_selectedPermissions.length}'
                                : 'Selected: ${_selectedPermissions.length}',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      // Select All / Deselect All buttons
                      Row(
                        children: [
                          ElevatedButton.icon(
                            icon: const Icon(Icons.select_all, size: 16),
                            label: Text(isRTL ? 'تحديد الكل' : 'Select All'),
                            onPressed: () {
                              setState(() {
                                _selectedPermissions = Set.from(PermissionType.values);
                              });
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).colorScheme.primary,
                              foregroundColor: Colors.white,
                            ),
                          ),
                          const SizedBox(width: 8),
                          OutlinedButton.icon(
                            icon: const Icon(Icons.clear_all, size: 16),
                            label: Text(isRTL ? 'إلغاء التحديد' : 'Clear All'),
                            onPressed: () {
                              setState(() {
                                _selectedPermissions.clear();
                              });
                            },
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Permissions List
                      _buildPermissionsList(isRTL),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Save Button
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton.icon(
                  icon: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(Icons.save),
                  label: Text(
                    _isLoading
                        ? (isRTL ? 'جاري الحفظ...' : 'Saving...')
                        : (isEditing
                            ? (isRTL ? 'تحديث الدور' : 'Update Role')
                            : (isRTL ? 'إنشاء الدور' : 'Create Role')),
                    style: const TextStyle(fontSize: 16),
                  ),
                  onPressed: _isLoading ? null : _saveRole,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPermissionsList(bool isRTL) {
    // Group permissions by category
    final Map<String, List<PermissionType>> groupedPermissions = {};

    for (final permission in PermissionType.values) {
      final category = PermissionHelper.getPermissionCategory(permission, isRTL);
      groupedPermissions.putIfAbsent(category, () => []).add(permission);
    }

    return Column(
      children: groupedPermissions.entries.map((entry) {
        final category = entry.key;
        final permissions = entry.value;
        final selectedInCategory = permissions.where((p) => _selectedPermissions.contains(p)).length;

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ExpansionTile(
            title: Text(
              category,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            subtitle: Text(
              isRTL
                  ? 'محدد: $selectedInCategory من ${permissions.length}'
                  : 'Selected: $selectedInCategory of ${permissions.length}',
            ),
            children: permissions.map((permission) {
              return CheckboxListTile(
                title: Text(PermissionHelper.getPermissionName(permission, isRTL)),
                value: _selectedPermissions.contains(permission),
                onChanged: (value) {
                  setState(() {
                    if (value == true) {
                      _selectedPermissions.add(permission);
                    } else {
                      _selectedPermissions.remove(permission);
                    }
                  });
                },
                dense: true,
              );
            }).toList(),
          ),
        );
      }).toList(),
    );
  }

  Future<void> _saveRole() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedPermissions.isEmpty) {
      final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
      final isRTL = localeProvider.isRTL;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isRTL ? 'يرجى تحديد صلاحية واحدة على الأقل' : 'Please select at least one permission',
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final permissionProvider = Provider.of<PermissionProvider>(context, listen: false);
      final activityLogProvider = Provider.of<ActivityLogProvider>(context, listen: false);
      final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
      final isRTL = localeProvider.isRTL;

      final role = UserRole(
        id: widget.roleToEdit?.id ?? '',
        name: _nameController.text.trim(),
        nameAr: _nameArController.text.trim(),
        description: _descriptionController.text.trim(),
        descriptionAr: _descriptionArController.text.trim(),
        permissions: _selectedPermissions.toList(),
        isSystemRole: false,
        createdAt: widget.roleToEdit?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      bool success;
      if (widget.roleToEdit != null) {
        // Update existing role
        success = await permissionProvider.updateRole(role);
        if (success) {
          await activityLogProvider.logRoleUpdated(
            authProvider.user!.id,
            authProvider.user!.displayName ?? authProvider.user!.email,
            role.id,
            role.name,
          );
        }
      } else {
        // Create new role
        success = await permissionProvider.createRole(role);
        if (success) {
          await activityLogProvider.logRoleCreated(
            authProvider.user!.id,
            authProvider.user!.displayName ?? authProvider.user!.email,
            role.id,
            role.name,
          );
        }
      }

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.roleToEdit != null
                  ? (isRTL ? 'تم تحديث الدور بنجاح' : 'Role updated successfully')
                  : (isRTL ? 'تم إنشاء الدور بنجاح' : 'Role created successfully'),
            ),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.roleToEdit != null
                  ? (isRTL ? 'فشل في تحديث الدور' : 'Failed to update role')
                  : (isRTL ? 'فشل في إنشاء الدور' : 'Failed to create role'),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
