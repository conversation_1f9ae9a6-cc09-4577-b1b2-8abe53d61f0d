import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import '../models/device_category_model.dart';
import '../services/firestore_service.dart';

class CategoryProvider with ChangeNotifier {
  final FirestoreService _firestoreService = FirestoreService();

  List<DeviceCategory> _categories = [];
  bool _isLoading = false;
  bool _isInitialized = false;
  String? _error;

  List<DeviceCategory> get categories => _categories;
  bool get isLoading => _isLoading;
  bool get isInitialized => _isInitialized;
  String? get error => _error;

  // Fetch all categories
  Future<void> fetchCategories({bool forceRefresh = false}) async {
    // Don't fetch again if already initialized and not forcing refresh
    if (_isInitialized && !forceRefresh) {
      return;
    }

    _setLoading(true);
    _clearError();

    try {
      final fetchedCategories = await _firestoreService.getDeviceCategories();

      // Validate that categories have proper names
      final validCategories = <DeviceCategory>[];
      for (final category in fetchedCategories) {
        if (category.name.isNotEmpty && !_isFirebaseId(category.name)) {
          validCategories.add(category);
        } else {
          debugPrint('Warning: Category with invalid name detected: ${category.id} - ${category.name}');
        }
      }

      _categories = validCategories;
      _isInitialized = true;

      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    } catch (e) {
      debugPrint('Error getting categories: $e');
      // If we have permission issues, use some sample categories
      if (e.toString().contains('permission-denied') || e.toString().contains('collection-not-found')) {
        _categories = [
          DeviceCategory(
            id: 'washing_machines',
            name: 'غسالات',
            description: 'أعطال وإصلاحات الغسالات',
            deviceCount: 0,
          ),
          DeviceCategory(
            id: 'refrigerators',
            name: 'ثلاجات',
            description: 'أعطال وإصلاحات الثلاجات',
            deviceCount: 0,
          ),
          DeviceCategory(
            id: 'air_conditioners',
            name: 'مكيفات',
            description: 'أعطال وإصلاحات المكيفات',
            deviceCount: 0,
          ),
        ];
        _isInitialized = true;
        SchedulerBinding.instance.addPostFrameCallback((_) {
          notifyListeners();
        });
      } else {
        _setError(e.toString());
      }
    } finally {
      _setLoading(false);
    }
  }

  // Helper method to check if a string looks like a Firebase document ID
  bool _isFirebaseId(String text) {
    // Firebase document IDs are typically 20 characters long and contain alphanumeric characters
    if (text.length == 20 && RegExp(r'^[a-zA-Z0-9]+$').hasMatch(text)) {
      return true;
    }
    // Also check for the specific pattern from the image
    if (text.length > 15 && RegExp(r'^[a-zA-Z0-9]{15,}$').hasMatch(text)) {
      return true;
    }
    return false;
  }

  // Public method to check if a string looks like a Firebase document ID
  bool isFirebaseId(String text) {
    return _isFirebaseId(text);
  }

  // Get category by ID
  DeviceCategory? getCategoryById(String id) {
    try {
      return _categories.firstWhere((category) => category.id == id);
    } catch (e) {
      // If category not found and we haven't initialized yet, try to fetch
      if (!_isInitialized) {
        fetchCategories();
      }
      return null;
    }
  }

  // Get category name by ID with fallback
  String getCategoryNameById(String id) {
    final category = getCategoryById(id);
    if (category != null && category.name.isNotEmpty && !_isFirebaseId(category.name)) {
      return category.name;
    }

    // Return a user-friendly fallback instead of the ID
    return 'فئة غير محددة';
  }

  // Add new category
  Future<bool> addCategory(DeviceCategory category) async {
    _setLoading(true);
    _clearError();

    try {
      final id = await _firestoreService.addDeviceCategory(category);
      final newCategory = category.copyWith(id: id);
      _categories.add(newCategory);
      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update category
  Future<bool> updateCategory(DeviceCategory category) async {
    _setLoading(true);
    _clearError();

    try {
      await _firestoreService.updateDeviceCategory(category);
      final index = _categories.indexWhere((c) => c.id == category.id);
      if (index != -1) {
        _categories[index] = category;
        SchedulerBinding.instance.addPostFrameCallback((_) {
          notifyListeners();
        });
      }
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete category
  Future<bool> deleteCategory(String categoryId) async {
    _setLoading(true);
    _clearError();

    try {
      await _firestoreService.deleteDeviceCategory(categoryId);
      _categories.removeWhere((category) => category.id == categoryId);
      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Helper methods
  void _setLoading(bool value) {
    if (_isLoading != value) {
      _isLoading = value;
      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }

  void _setError(String error) {
    if (_error != error) {
      _error = error;
      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }

  void _clearError() {
    if (_error != null) {
      _error = null;
      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }
}
