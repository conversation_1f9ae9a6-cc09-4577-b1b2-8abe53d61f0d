import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'google_drive_service.dart';
import 'storage_config_service.dart';

class StorageService {
  final GoogleDriveService _googleDriveService = GoogleDriveService();
  final StorageConfigService _configService = StorageConfigService();

  /// Initialize the storage service
  Future<void> initialize() async {
    try {
      // Initialize config service first
      await _configService.initialize();

      // Initialize Google Drive service
      await _googleDriveService.initialize();
    } catch (e) {
      debugPrint('Error initializing storage service: $e');
      rethrow;
    }
  }

  /// Upload image file to Google Drive
  Future<String?> uploadImage(File file, String fileName, {String? preferredService}) async {
    try {
      // Ensure service is initialized
      if (!_googleDriveService.isInitialized) {
        await _googleDriveService.initialize();
      }

      // Generate unique filename with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(fileName);
      final uniqueFileName = 'device_error_$timestamp$extension';

      // Upload to Google Drive
      final fileId = await _googleDriveService.uploadFile(
        file: file,
        fileName: uniqueFileName,
      );

      if (fileId != null) {
        // Get public download URL
        final downloadUrl = await _googleDriveService.getPublicDownloadUrl(fileId);
        return downloadUrl;
      }

      return null;
    } catch (e) {
      debugPrint('Error uploading image: $e');
      return null;
    }
  }

  /// Upload user profile image
  Future<String?> uploadUserProfileImage(File file, String userId, {String? preferredService}) async {
    try {
      // Ensure service is initialized
      if (!_googleDriveService.isInitialized) {
        await _googleDriveService.initialize();
      }

      final extension = path.extension(file.path);
      final fileName = 'profile_${userId}_${DateTime.now().millisecondsSinceEpoch}$extension';

      // Upload to Google Drive
      final fileId = await _googleDriveService.uploadFile(
        file: file,
        fileName: fileName,
      );

      if (fileId != null) {
        // Get public download URL
        final downloadUrl = await _googleDriveService.getPublicDownloadUrl(fileId);
        return downloadUrl;
      }

      return null;
    } catch (e) {
      debugPrint('Error uploading user profile image: $e');
      return null;
    }
  }

  /// Upload document file to Google Drive
  Future<String?> uploadDocument(File file, String fileName) async {
    try {
      // Ensure service is initialized
      if (!_googleDriveService.isInitialized) {
        await _googleDriveService.initialize();
      }

      // Generate unique filename with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(fileName);
      final baseName = path.basenameWithoutExtension(fileName);
      final uniqueFileName = '${baseName}_$timestamp$extension';

      // Upload to Google Drive
      final fileId = await _googleDriveService.uploadFile(
        file: file,
        fileName: uniqueFileName,
      );

      if (fileId != null) {
        // Get public download URL
        final downloadUrl = await _googleDriveService.getPublicDownloadUrl(fileId);
        return downloadUrl;
      }

      return null;
    } catch (e) {
      debugPrint('Error uploading document: $e');
      return null;
    }
  }

  /// Upload file from bytes data to Google Drive
  Future<String?> uploadFile(Uint8List fileData, String fileName, String folderPath) async {
    try {
      // Ensure service is initialized
      if (!_googleDriveService.isInitialized) {
        await _googleDriveService.initialize();
      }

      // Create temporary file from bytes
      final tempDir = Directory.systemTemp;
      final tempFile = File('${tempDir.path}/$fileName');
      await tempFile.writeAsBytes(fileData);

      try {
        // Upload to Google Drive
        final fileId = await _googleDriveService.uploadFile(
          file: tempFile,
          fileName: fileName,
        );

        if (fileId != null) {
          // Get public download URL
          final downloadUrl = await _googleDriveService.getPublicDownloadUrl(fileId);
          return downloadUrl;
        }

        return null;
      } finally {
        // Clean up temporary file
        if (await tempFile.exists()) {
          await tempFile.delete();
        }
      }
    } catch (e) {
      debugPrint('Error uploading file from bytes: $e');
      return null;
    }
  }

  /// Delete file from Google Drive using file ID extracted from URL
  Future<bool> deleteFile(String fileUrl) async {
    try {
      // Ensure service is initialized
      if (!_googleDriveService.isInitialized) {
        await _googleDriveService.initialize();
      }

      // Extract file ID from Google Drive URL
      final fileId = _extractFileIdFromUrl(fileUrl);
      if (fileId == null) {
        debugPrint('Could not extract file ID from URL: $fileUrl');
        return false;
      }

      // Delete file from Google Drive
      await _googleDriveService.deleteFile(fileId);
      return true;
    } catch (e) {
      debugPrint('Error deleting file: $e');
      return false;
    }
  }

  /// Get file information
  Future<Map<String, dynamic>?> getFileInfo(String fileUrl) async {
    try {
      // Ensure service is initialized
      if (!_googleDriveService.isInitialized) {
        await _googleDriveService.initialize();
      }

      // Extract file ID from Google Drive URL
      final fileId = _extractFileIdFromUrl(fileUrl);
      if (fileId == null) {
        debugPrint('Could not extract file ID from URL: $fileUrl');
        return null;
      }

      // Get file info from Google Drive
      final fileInfo = await _googleDriveService.getFileInfo(fileId);
      if (fileInfo != null) {
        return {
          'id': fileInfo.id,
          'name': fileInfo.name,
          'mimeType': fileInfo.mimeType,
          'size': fileInfo.size,
          'createdTime': fileInfo.createdTime?.toIso8601String(),
          'modifiedTime': fileInfo.modifiedTime?.toIso8601String(),
        };
      }

      return null;
    } catch (e) {
      debugPrint('Error getting file info: $e');
      return null;
    }
  }

  /// Get storage usage information
  Future<Map<String, dynamic>?> getStorageInfo() async {
    try {
      // Ensure service is initialized
      if (!_googleDriveService.isInitialized) {
        await _googleDriveService.initialize();
      }

      return await _googleDriveService.getStorageUsage();
    } catch (e) {
      debugPrint('Error getting storage info: $e');
      return null;
    }
  }

  /// List files in the app folder
  Future<List<Map<String, dynamic>>> listFiles({String? query, int maxResults = 100}) async {
    try {
      // Ensure service is initialized
      if (!_googleDriveService.isInitialized) {
        await _googleDriveService.initialize();
      }

      final files = await _googleDriveService.listFiles(
        query: query,
        maxResults: maxResults,
      );

      return files.map((file) => {
        'id': file.id,
        'name': file.name,
        'mimeType': file.mimeType,
        'size': file.size,
        'createdTime': file.createdTime?.toIso8601String(),
        'modifiedTime': file.modifiedTime?.toIso8601String(),
      }).toList();
    } catch (e) {
      debugPrint('Error listing files: $e');
      return [];
    }
  }

  /// Extract file ID from Google Drive URL
  String? _extractFileIdFromUrl(String url) {
    try {
      // Handle different Google Drive URL formats
      if (url.contains('drive.google.com/uc?')) {
        // Format: https://drive.google.com/uc?export=download&id=FILE_ID
        final uri = Uri.parse(url);
        return uri.queryParameters['id'];
      } else if (url.contains('drive.google.com/file/d/')) {
        // Format: https://drive.google.com/file/d/FILE_ID/view
        final regex = RegExp(r'/file/d/([a-zA-Z0-9_-]+)');
        final match = regex.firstMatch(url);
        return match?.group(1);
      } else if (url.contains('drive.google.com/open?id=')) {
        // Format: https://drive.google.com/open?id=FILE_ID
        final uri = Uri.parse(url);
        return uri.queryParameters['id'];
      }

      // If it's already a file ID (no URL format)
      if (url.length > 20 && !url.contains('/') && !url.contains('?')) {
        return url;
      }

      return null;
    } catch (e) {
      debugPrint('Error extracting file ID from URL: $e');
      return null;
    }
  }

  /// Check if the service is initialized
  bool get isInitialized => _googleDriveService.isInitialized;

  /// Dispose resources
  void dispose() {
    _googleDriveService.dispose();
  }
}
