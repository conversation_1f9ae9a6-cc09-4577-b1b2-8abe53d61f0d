import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'app_cleanup_service.dart';
import 'performance_service.dart';
import 'asset_optimization_service.dart';
import 'attachment_cache_service.dart';

/// خدمة التحسين التلقائي للتطبيق
class AutoOptimizerService {
  static AutoOptimizerService? _instance;
  static AutoOptimizerService get instance => _instance ??= AutoOptimizerService._();

  AutoOptimizerService._();

  static const String _autoOptimizationKey = 'auto_optimization_settings';
  static const String _lastOptimizationKey = 'last_auto_optimization';
  static const String _optimizationStatsKey = 'optimization_stats';

  Timer? _optimizationTimer;
  bool _isOptimizationRunning = false;
  AutoOptimizationSettings _settings = const AutoOptimizationSettings();

  /// تهيئة خدمة التحسين التلقائي
  Future<void> initialize() async {
    try {
      await _loadSettings();

      if (_settings.isEnabled) {
        await _scheduleOptimization();
      }

      debugPrint('AutoOptimizerService initialized');
    } catch (e) {
      debugPrint('Error initializing AutoOptimizerService: $e');
    }
  }

  /// تحميل إعدادات التحسين التلقائي
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_autoOptimizationKey);

      if (settingsJson != null) {
        final settingsMap = jsonDecode(settingsJson) as Map<String, dynamic>;
        _settings = AutoOptimizationSettings.fromMap(settingsMap);
      }
    } catch (e) {
      debugPrint('Error loading auto optimization settings: $e');
    }
  }

  /// حفظ إعدادات التحسين التلقائي
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_autoOptimizationKey, jsonEncode(_settings.toMap()));
    } catch (e) {
      debugPrint('Error saving auto optimization settings: $e');
    }
  }

  /// جدولة التحسين التلقائي
  Future<void> _scheduleOptimization() async {
    _optimizationTimer?.cancel();

    if (!_settings.isEnabled) return;

    final interval = Duration(hours: _settings.intervalHours);

    _optimizationTimer = Timer.periodic(interval, (timer) async {
      await _performAutoOptimization();
    });

    debugPrint('Auto optimization scheduled every ${_settings.intervalHours} hours');
  }

  /// تنفيذ التحسين التلقائي
  Future<void> _performAutoOptimization() async {
    if (_isOptimizationRunning) {
      debugPrint('Auto optimization already running, skipping...');
      return;
    }

    _isOptimizationRunning = true;
    debugPrint('Starting auto optimization...');

    try {
      final optimizationResult = AutoOptimizationResult();
      final startTime = DateTime.now();

      // 1. تحسين الأداء
      if (_settings.optimizePerformance) {
        await _optimizePerformance(optimizationResult);
      }

      // 2. تنظيف التخزين المؤقت
      if (_settings.cleanCache) {
        await _cleanCache(optimizationResult);
      }

      // 3. تحسين الصور
      if (_settings.optimizeImages) {
        await _optimizeImages(optimizationResult);
      }

      // 4. تنظيف الملفات المؤقتة
      if (_settings.cleanTempFiles) {
        await _cleanTempFiles(optimizationResult);
      }

      // 5. تحسين قاعدة البيانات
      if (_settings.optimizeDatabase) {
        await _optimizeDatabase(optimizationResult);
      }

      optimizationResult.duration = DateTime.now().difference(startTime);

      // حفظ نتائج التحسين
      await _saveOptimizationResult(optimizationResult);

      // تحديث تاريخ آخر تحسين
      await _updateLastOptimizationDate();

      debugPrint('Auto optimization completed in ${optimizationResult.duration.inSeconds} seconds');
      debugPrint('Total space freed: ${_formatBytes(optimizationResult.totalSpaceFreed)}');

    } catch (e) {
      debugPrint('Error during auto optimization: $e');
    } finally {
      _isOptimizationRunning = false;
    }
  }

  /// تحسين الأداء
  Future<void> _optimizePerformance(AutoOptimizationResult result) async {
    try {
      final memoryBefore = PerformanceService.getMemoryStats()['memoryUsage'] as int;

      PerformanceService.optimizeMemory();
      PerformanceService.optimizeForSize();

      final memoryAfter = PerformanceService.getMemoryStats()['memoryUsage'] as int;
      final memoryFreed = memoryBefore - memoryAfter;

      result.addOptimization('Performance', memoryFreed);
      debugPrint('Performance optimization: freed ${_formatBytes(memoryFreed)}');
    } catch (e) {
      debugPrint('Error optimizing performance: $e');
    }
  }

  /// تنظيف التخزين المؤقت
  Future<void> _cleanCache(AutoOptimizationResult result) async {
    try {
      // تنظيف cache المرفقات
      final attachmentService = AttachmentCacheService.instance;
      await attachmentService.initialize();

      final cacheInfoBefore = await attachmentService.getCacheInfo();
      final sizeBefore = cacheInfoBefore.totalSize;

      // تنظيف cache الصور
      final imageStatsBefore = AssetOptimizationService.getCacheStats();
      final imageCacheBefore = imageStatsBefore['currentSize'] as int;

      AssetOptimizationService.clearCache();

      final totalFreed = sizeBefore + imageCacheBefore;
      result.addOptimization('Cache Cleanup', totalFreed);

      debugPrint('Cache cleanup: freed ${_formatBytes(totalFreed)}');
    } catch (e) {
      debugPrint('Error cleaning cache: $e');
    }
  }

  /// تحسين الصور
  Future<void> _optimizeImages(AutoOptimizationResult result) async {
    try {
      // تنظيف cache الصور القديمة
      AssetOptimizationService.cleanupOldMemoryCache();
      await AssetOptimizationService.cleanupOldCache(); // File cache cleanup

      // تقدير المساحة المحررة (تقريبي)
      final estimatedFreed = 5 * 1024 * 1024; // 5MB تقدير
      result.addOptimization('Image Optimization', estimatedFreed);

      debugPrint('Image optimization completed');
    } catch (e) {
      debugPrint('Error optimizing images: $e');
    }
  }

  /// تنظيف الملفات المؤقتة
  Future<void> _cleanTempFiles(AutoOptimizationResult result) async {
    try {
      final cleanupService = AppCleanupService.instance;
      final cleanupResult = await cleanupService.performQuickCleanup();

      result.addOptimization('Temp Files Cleanup', cleanupResult.totalFreedBytes);

      debugPrint('Temp files cleanup: freed ${cleanupResult.formattedTotalFreed}');
    } catch (e) {
      debugPrint('Error cleaning temp files: $e');
    }
  }

  /// تحسين قاعدة البيانات
  Future<void> _optimizeDatabase(AutoOptimizationResult result) async {
    try {
      await PerformanceService.optimizeDatabase();

      // تقدير المساحة المحررة (تقريبي)
      final estimatedFreed = 2 * 1024 * 1024; // 2MB تقدير
      result.addOptimization('Database Optimization', estimatedFreed);

      debugPrint('Database optimization completed');
    } catch (e) {
      debugPrint('Error optimizing database: $e');
    }
  }

  /// حفظ نتيجة التحسين
  Future<void> _saveOptimizationResult(AutoOptimizationResult result) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final resultData = {
        'timestamp': DateTime.now().toIso8601String(),
        'duration': result.duration.inSeconds,
        'totalSpaceFreed': result.totalSpaceFreed,
        'optimizations': result.optimizations.map((opt) => {
          'type': opt.type,
          'spaceFreed': opt.spaceFreed,
        }).toList(),
      };

      await prefs.setString(_optimizationStatsKey, jsonEncode(resultData));
    } catch (e) {
      debugPrint('Error saving optimization result: $e');
    }
  }

  /// تحديث تاريخ آخر تحسين
  Future<void> _updateLastOptimizationDate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastOptimizationKey, DateTime.now().toIso8601String());
    } catch (e) {
      debugPrint('Error updating last optimization date: $e');
    }
  }

  /// تحديث إعدادات التحسين التلقائي
  Future<void> updateSettings(AutoOptimizationSettings newSettings) async {
    _settings = newSettings;
    await _saveSettings();

    if (_settings.isEnabled) {
      await _scheduleOptimization();
    } else {
      _optimizationTimer?.cancel();
    }

    debugPrint('Auto optimization settings updated');
  }

  /// الحصول على الإعدادات الحالية
  AutoOptimizationSettings get settings => _settings;

  /// الحصول على آخر نتيجة تحسين
  Future<Map<String, dynamic>?> getLastOptimizationResult() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final resultJson = prefs.getString(_optimizationStatsKey);

      if (resultJson != null) {
        return jsonDecode(resultJson) as Map<String, dynamic>;
      }
    } catch (e) {
      debugPrint('Error getting last optimization result: $e');
    }

    return null;
  }

  /// التحقق من الحاجة للتحسين
  Future<bool> needsOptimization() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastOptimizationStr = prefs.getString(_lastOptimizationKey);

      if (lastOptimizationStr == null) return true;

      final lastOptimization = DateTime.parse(lastOptimizationStr);
      final hoursSinceOptimization = DateTime.now().difference(lastOptimization).inHours;

      return hoursSinceOptimization >= _settings.intervalHours;
    } catch (e) {
      debugPrint('Error checking optimization need: $e');
      return true;
    }
  }

  /// تشغيل تحسين فوري
  Future<AutoOptimizationResult> runImmediateOptimization() async {
    final result = AutoOptimizationResult();
    final startTime = DateTime.now();

    try {
      await _optimizePerformance(result);
      await _cleanCache(result);
      await _optimizeImages(result);
      await _cleanTempFiles(result);
      await _optimizeDatabase(result);

      result.duration = DateTime.now().difference(startTime);
      await _saveOptimizationResult(result);
      await _updateLastOptimizationDate();

    } catch (e) {
      debugPrint('Error during immediate optimization: $e');
    }

    return result;
  }

  /// إيقاف الخدمة
  void dispose() {
    _optimizationTimer?.cancel();
    _optimizationTimer = null;
  }

  /// تنسيق البايتات
  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}

/// إعدادات التحسين التلقائي
class AutoOptimizationSettings {
  final bool isEnabled;
  final int intervalHours;
  final bool optimizePerformance;
  final bool cleanCache;
  final bool optimizeImages;
  final bool cleanTempFiles;
  final bool optimizeDatabase;

  const AutoOptimizationSettings({
    this.isEnabled = false,
    this.intervalHours = 24, // كل 24 ساعة افتراضياً
    this.optimizePerformance = true,
    this.cleanCache = true,
    this.optimizeImages = true,
    this.cleanTempFiles = true,
    this.optimizeDatabase = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'isEnabled': isEnabled,
      'intervalHours': intervalHours,
      'optimizePerformance': optimizePerformance,
      'cleanCache': cleanCache,
      'optimizeImages': optimizeImages,
      'cleanTempFiles': cleanTempFiles,
      'optimizeDatabase': optimizeDatabase,
    };
  }

  factory AutoOptimizationSettings.fromMap(Map<String, dynamic> map) {
    return AutoOptimizationSettings(
      isEnabled: map['isEnabled'] ?? false,
      intervalHours: map['intervalHours'] ?? 24,
      optimizePerformance: map['optimizePerformance'] ?? true,
      cleanCache: map['cleanCache'] ?? true,
      optimizeImages: map['optimizeImages'] ?? true,
      cleanTempFiles: map['cleanTempFiles'] ?? true,
      optimizeDatabase: map['optimizeDatabase'] ?? false,
    );
  }

  AutoOptimizationSettings copyWith({
    bool? isEnabled,
    int? intervalHours,
    bool? optimizePerformance,
    bool? cleanCache,
    bool? optimizeImages,
    bool? cleanTempFiles,
    bool? optimizeDatabase,
  }) {
    return AutoOptimizationSettings(
      isEnabled: isEnabled ?? this.isEnabled,
      intervalHours: intervalHours ?? this.intervalHours,
      optimizePerformance: optimizePerformance ?? this.optimizePerformance,
      cleanCache: cleanCache ?? this.cleanCache,
      optimizeImages: optimizeImages ?? this.optimizeImages,
      cleanTempFiles: cleanTempFiles ?? this.cleanTempFiles,
      optimizeDatabase: optimizeDatabase ?? this.optimizeDatabase,
    );
  }
}

/// نتيجة التحسين التلقائي
class AutoOptimizationResult {
  final List<OptimizationItem> optimizations = [];
  Duration duration = Duration.zero;

  void addOptimization(String type, int spaceFreed) {
    optimizations.add(OptimizationItem(type, spaceFreed));
  }

  int get totalSpaceFreed => optimizations.fold(0, (sum, opt) => sum + opt.spaceFreed);

  String get formattedTotalFreed {
    final bytes = totalSpaceFreed;
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}

/// عنصر التحسين
class OptimizationItem {
  final String type;
  final int spaceFreed;

  OptimizationItem(this.type, this.spaceFreed);
}
