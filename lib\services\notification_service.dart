import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../models/device_error_model.dart';
import '../utils/platform_utils.dart';

class NotificationService {
  static final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  static bool _isInitialized = false;

  /// Initialize notification service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Request permission for notifications
      await _requestPermission();

      // Initialize local notifications
      await _initializeLocalNotifications();

      // Configure Firebase Messaging
      await _configureFCM();

      _isInitialized = true;
      debugPrint('Notification service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing notification service: $e');
    }
  }

  /// Request notification permissions
  static Future<void> _requestPermission() async {
    final settings = await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    debugPrint('Notification permission status: ${settings.authorizationStatus}');
  }

  /// Initialize local notifications
  static Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
  }

  /// Configure Firebase Cloud Messaging
  static Future<void> _configureFCM() async {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // Check for initial message when app is opened from terminated state
    final initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      _handleNotificationTap(initialMessage);
    }
  }

  /// Handle foreground messages
  static Future<void> _handleForegroundMessage(RemoteMessage message) async {
    debugPrint('Received foreground message: ${message.messageId}');

    // Show local notification when app is in foreground
    await _showLocalNotification(message);
  }

  /// Handle background messages
  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    debugPrint('Received background message: ${message.messageId}');
    // Background messages are automatically displayed by the system
  }

  /// Handle notification tap
  static void _handleNotificationTap(RemoteMessage message) {
    debugPrint('Notification tapped: ${message.messageId}');

    // Navigate to appropriate screen based on notification data
    final data = message.data;
    if (data.containsKey('error_id')) {
      // Navigate to error details
      // This will be handled by the main app navigation
    }
  }

  /// Handle local notification tap
  static void _onNotificationTapped(NotificationResponse response) {
    debugPrint('Local notification tapped: ${response.payload}');

    if (response.payload != null) {
      try {
        final data = jsonDecode(response.payload!);
        debugPrint('Notification payload data: $data');
        // Handle navigation based on payload data
        // This will be implemented by the main app navigation system
      } catch (e) {
        debugPrint('Error parsing notification payload: $e');
      }
    }
  }

  /// Show local notification
  static Future<void> _showLocalNotification(RemoteMessage message) async {
    const androidDetails = AndroidNotificationDetails(
      'hm_device_errors_channel',
      'HM Device Errors',
      channelDescription: 'Notifications for new device errors',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      message.hashCode,
      message.notification?.title ?? 'HM Device Errors',
      message.notification?.body ?? 'New notification',
      notificationDetails,
      payload: jsonEncode(message.data),
    );
  }

  /// Get FCM token for current device with retry logic
  static Future<String?> getFCMToken() async {
    try {
      // Check if we're on a supported platform
      if (PlatformUtils.isWeb) {
        // For web, FCM tokens might take longer to initialize
        await Future.delayed(const Duration(seconds: 1));
      }

      // Try to get token with timeout
      final token = await _firebaseMessaging.getToken().timeout(
        const Duration(seconds: 15), // Increased timeout for web
        onTimeout: () {
          // Don't print error for timeout, it's expected sometimes
          return null;
        },
      );

      if (token != null && token.isNotEmpty) {
        debugPrint('FCM Token: ${token.substring(0, 20)}...');
        return token;
      } else {
        // Retry once after a short delay
        await Future.delayed(const Duration(seconds: 3));
        final retryToken = await _firebaseMessaging.getToken().timeout(
          const Duration(seconds: 10),
          onTimeout: () => null,
        );

        if (retryToken != null && retryToken.isNotEmpty) {
          debugPrint('FCM Token (retry): ${retryToken.substring(0, 20)}...');
          return retryToken;
        } else {
          // This is normal on some platforms, don't treat as error
          return null;
        }
      }
    } catch (e) {
      // Only log actual errors, not normal platform limitations
      if (!e.toString().contains('not supported') &&
          !e.toString().contains('permission')) {
        debugPrint('Error getting FCM token: $e');
      }
      return null;
    }
  }

  /// Subscribe user to notifications
  static Future<void> subscribeUser(String userId) async {
    try {
      final token = await getFCMToken();
      if (token == null) return;

      // Store user's FCM token in Firestore
      await _firestore.collection('user_tokens').doc(userId).set({
        'fcm_token': token,
        'updated_at': FieldValue.serverTimestamp(),
        'platform': defaultTargetPlatform.name,
      }, SetOptions(merge: true));

      // Subscribe to general topic (not supported on web)
      if (!PlatformUtils.isWeb) {
        await _firebaseMessaging.subscribeToTopic('all_users');
      } else {
        debugPrint('Topic subscription not supported on web - using token-based messaging');
      }

      debugPrint('User subscribed to notifications');
    } catch (e) {
      debugPrint('Error subscribing user to notifications: $e');
    }
  }

  /// Unsubscribe user from notifications
  static Future<void> unsubscribeUser(String userId) async {
    try {
      // Remove user's FCM token from Firestore
      await _firestore.collection('user_tokens').doc(userId).delete();

      // Unsubscribe from general topic (not supported on web)
      if (!PlatformUtils.isWeb) {
        await _firebaseMessaging.unsubscribeFromTopic('all_users');
      }

      debugPrint('User unsubscribed from notifications');
    } catch (e) {
      debugPrint('Error unsubscribing user from notifications: $e');
    }
  }

  /// Send notification to all users about new error
  static Future<void> sendNewErrorNotification(DeviceError error) async {
    try {
      // Create notification document in Firestore
      // This will trigger a Cloud Function to send FCM messages
      await _firestore.collection('notifications').add({
        'type': 'new_error',
        'title_ar': 'عطل جديد',
        'title_en': 'New Error',
        'body_ar': 'تم إضافة عطل جديد: ${error.manufacturer} ${error.model}',
        'body_en': 'New error added: ${error.manufacturer} ${error.model}',
        'error_id': error.id,
        'error_code': error.errorCode,
        'manufacturer': error.manufacturer,
        'model': error.model,
        'created_at': FieldValue.serverTimestamp(),
        'target_audience': 'all_users',
      });

      debugPrint('New error notification sent for: ${error.manufacturer} ${error.model}');
    } catch (e) {
      debugPrint('Error sending new error notification: $e');
    }
  }

  /// Send notification to specific user
  static Future<void> sendUserNotification({
    required String userId,
    required String titleAr,
    required String titleEn,
    required String bodyAr,
    required String bodyEn,
    Map<String, dynamic>? data,
  }) async {
    try {
      await _firestore.collection('notifications').add({
        'type': 'user_specific',
        'title_ar': titleAr,
        'title_en': titleEn,
        'body_ar': bodyAr,
        'body_en': bodyEn,
        'target_user_id': userId,
        'data': data ?? {},
        'created_at': FieldValue.serverTimestamp(),
      });

      debugPrint('User-specific notification sent to: $userId');
    } catch (e) {
      debugPrint('Error sending user notification: $e');
    }
  }

  /// Clear all notifications
  static Future<void> clearAllNotifications() async {
    await _localNotifications.cancelAll();
  }

  /// Clear specific notification
  static Future<void> clearNotification(int id) async {
    await _localNotifications.cancel(id);
  }
}

/// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> _handleBackgroundMessage(RemoteMessage message) async {
  debugPrint('Handling background message: ${message.messageId}');
}
