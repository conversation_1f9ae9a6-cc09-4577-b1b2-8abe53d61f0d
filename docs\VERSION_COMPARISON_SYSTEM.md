# نظام مقارنة الإصدارات في التطبيق

## 📱 **تحديث الإصدار الحالي**

تم تحديث إصدار التطبيق إلى **1.0.4+5** في الملفات التالية:
- `pubspec.yaml`: `version: 1.0.4+5`
- `RemoteConfigService`: القيم الافتراضية محدثة

## 🔍 **كيف يعمل نظام مقارنة الإصدارات**

### **1. مصادر الإصدارات**

#### **أ) الإصدار الحالي للتطبيق**
```dart
// في AppUpdateService.checkForUpdates()
final packageInfo = await PackageInfo.fromPlatform();
final currentVersion = packageInfo.version; // مثال: "1.0.4"
```

**المصدر**: 
- `pubspec.yaml` → `version: 1.0.4+5`
- يتم قراءته باستخدام `PackageInfo.fromPlatform()`

#### **ب) أحدث إصدار متاح**
```dart
// من Firebase Remote Config
final latestVersion = RemoteConfigService.getString('latest_version');
```

**المصدر**: 
- Firebase Remote Config → `latest_version`
- يحدده المطور في Firebase Console

#### **ج) الحد الأدنى للإصدار المدعوم**
```dart
// من Firebase Remote Config
final minimumVersion = RemoteConfigService.getString('minimum_version');
```

**المصدر**: 
- Firebase Remote Config → `minimum_version`
- يحدده المطور في Firebase Console

### **2. خوارزمية المقارنة**

#### **الدالة الأساسية: `_isVersionLower()`**
```dart
static bool _isVersionLower(String current, String target) {
  // 1. تقسيم الإصدارات إلى أجزاء
  final currentParts = current.split('.').map(int.parse).toList();
  final targetParts = target.split('.').map(int.parse).toList();
  
  // 2. توحيد طول القوائم
  while (currentParts.length < targetParts.length) {
    currentParts.add(0);
  }
  while (targetParts.length < currentParts.length) {
    targetParts.add(0);
  }
  
  // 3. مقارنة كل جزء
  for (int i = 0; i < currentParts.length; i++) {
    if (currentParts[i] < targetParts[i]) {
      return true;  // الحالي أقل
    } else if (currentParts[i] > targetParts[i]) {
      return false; // الحالي أكبر
    }
  }
  return false; // متساويان
}
```

#### **أمثلة على المقارنة للإصدار الحالي 1.0.4:**

| الإصدار المستهدف | النتيجة | التفسير |
|------------------|---------|----------|
| `1.0.3` | `false` | 1.0.4 > 1.0.3 (الحالي أكبر) |
| `1.0.4` | `false` | 1.0.4 = 1.0.4 (متساويان) |
| `1.0.5` | `true` | 1.0.4 < 1.0.5 (الحالي أقل) |
| `1.1.0` | `true` | 1.0.4 < 1.1.0 (الحالي أقل) |
| `2.0.0` | `true` | 1.0.4 < 2.0.0 (الحالي أقل) |
| `1.0.4.1` | `true` | 1.0.4.0 < 1.0.4.1 (الحالي أقل) |
| `1.0` | `false` | 1.0.4 > 1.0.0 (الحالي أكبر) |

### **3. منطق اتخاذ القرار**

#### **في `AppUpdateService.checkForUpdates()`:**

```dart
// 1. فحص التحديث الإجباري
if (_isVersionLower(currentVersion, minimumVersion)) {
  return UpdateInfo(type: UpdateType.required); // تحديث إجباري
}

// 2. فحص التحديث الاختياري
if (_isVersionLower(currentVersion, latestVersion)) {
  // فحص إذا تم تخطي هذا الإصدار
  final skippedVersion = prefs.getString(_skipVersionKey);
  if (!updateRequired && skippedVersion == latestVersion) {
    return null; // تم تخطي هذا الإصدار
  }
  
  return UpdateInfo(
    type: updateRequired ? UpdateType.required : UpdateType.optional
  );
}

// 3. لا يوجد تحديث مطلوب
return null;
```

### **4. سيناريوهات التحديث**

#### **السيناريو 1: تحديث إجباري**
```
الإصدار الحالي: 1.0.4
الحد الأدنى: 1.0.5
أحدث إصدار: 1.0.6

النتيجة: تحديث إجباري (1.0.4 < 1.0.5)
```

#### **السيناريو 2: تحديث اختياري**
```
الإصدار الحالي: 1.0.4
الحد الأدنى: 1.0.3
أحدث إصدار: 1.0.5

النتيجة: تحديث اختياري (1.0.3 ≤ 1.0.4 < 1.0.5)
```

#### **السيناريو 3: لا يوجد تحديث**
```
الإصدار الحالي: 1.0.4
الحد الأدنى: 1.0.3
أحدث إصدار: 1.0.4

النتيجة: لا يوجد تحديث (1.0.4 = 1.0.4)
```

### **5. أماكن عرض الإصدارات**

#### **أ) صفحة إدارة التحديث**
```dart
// في _loadCurrentVersion()
final packageInfo = await PackageInfo.fromPlatform();
setState(() {
  _currentVersion = packageInfo.version; // 1.0.4
});

// في _buildCurrentStatusCard()
_buildStatusRow(
  'الإصدار الحالي',
  _currentVersion, // 1.0.4
  Colors.blue,
  isRTL,
),
```

#### **ب) Remote Config**
```dart
// في _buildCurrentStatusCard()
final latestVersion = provider.getString('latest_version');
final minimumVersion = provider.getString('minimum_version');
```

### **6. تدفق العمل الكامل**

```
1. بدء التطبيق
   ↓
2. تهيئة UpdateProvider
   ↓
3. استدعاء checkForUpdates()
   ↓
4. الحصول على الإصدار الحالي (PackageInfo)
   ↓
5. جلب الإصدارات من Remote Config
   ↓
6. مقارنة الإصدارات باستخدام _isVersionLower()
   ↓
7. اتخاذ قرار التحديث
   ↓
8. عرض الحوار المناسب (إن وجد)
```

### **7. إدارة الإصدارات عبر Remote Config**

#### **المفاتيح المطلوبة:**
```json
{
  "latest_version": "1.0.5",      // أحدث إصدار متاح
  "minimum_version": "1.0.3",     // الحد الأدنى المدعوم
  "update_required": false,       // إجباري أم اختياري
  "update_enabled": true          // تفعيل فحص التحديثات
}
```

#### **أمثلة على التكوين:**

**إجبار جميع المستخدمين على التحديث:**
```json
{
  "latest_version": "1.0.5",
  "minimum_version": "1.0.5",
  "update_required": true
}
```

**تحديث اختياري:**
```json
{
  "latest_version": "1.0.5",
  "minimum_version": "1.0.3",
  "update_required": false
}
```

### **8. معالجة الأخطاء**

#### **الإصدارات غير الصحيحة:**
```dart
try {
  final currentParts = current.split('.').map(int.parse).toList();
  // ...
} catch (e) {
  debugPrint('Error comparing versions: $e');
  return false; // افتراض عدم الحاجة للتحديث
}
```

#### **الحالات الاستثنائية:**
- إصدارات فارغة → `false`
- إصدارات غير رقمية → `false`
- أخطاء في الشبكة → لا يتم عرض التحديث

### **9. اختبار النظام**

تم إنشاء اختبارات شاملة في `test/version_comparison_test.dart` تغطي:
- ✅ مقارنات الإصدارات المختلفة
- ✅ الحالات الاستثنائية
- ✅ السيناريوهات الواقعية
- ✅ معالجة الأخطاء

### **10. الخلاصة**

نظام مقارنة الإصدارات يعمل بالشكل التالي:

1. **يقرأ الإصدار الحالي** من `PackageInfo` (1.0.4)
2. **يجلب الإصدارات المستهدفة** من Remote Config
3. **يقارن الإصدارات** باستخدام خوارزمية semantic versioning
4. **يتخذ قرار التحديث** بناءً على النتائج
5. **يعرض الحوار المناسب** للمستخدم

هذا النظام يوفر **مرونة كاملة** في إدارة تحديثات التطبيق مع **أمان** في التشغيل! 🚀
