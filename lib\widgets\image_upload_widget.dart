import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:provider/provider.dart';
import '../services/storage_service.dart';
import '../providers/locale_provider.dart';
// Removed flutter_spinkit import for size optimization

class ImageUploadWidget extends StatefulWidget {
  final List<String> imageUrls;
  final Function(List<String>) onImagesChanged;
  final Function(bool)? onUploadStateChanged;
  final int maxImages;
  final String? preferredService;

  const ImageUploadWidget({
    super.key,
    required this.imageUrls,
    required this.onImagesChanged,
    this.onUploadStateChanged,
    this.maxImages = 5,
    this.preferredService,
  });

  @override
  State<ImageUploadWidget> createState() => _ImageUploadWidgetState();
}

class _ImageUploadWidgetState extends State<ImageUploadWidget> {
  final ImagePicker _picker = ImagePicker();
  final StorageService _storageService = StorageService();
  bool _isUploading = false;
  double _uploadProgress = 0.0;
  bool _uploadCancelled = false;

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localeProvider.isRTL ? 'صور الجهاز والعطل' : 'Device and Error Images',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),

        // Upload progress indicator
        if (_isUploading)
          Column(
            children: [
              LinearProgressIndicator(value: _uploadProgress),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        localeProvider.isRTL ? 'جاري رفع الصورة...' : 'Uploading image...',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  TextButton(
                    onPressed: _cancelUpload,
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.orange,
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    ),
                    child: Text(
                      localeProvider.isRTL ? 'إلغاء' : 'Cancel',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],
          ),

        // Image grid
        if (widget.imageUrls.isNotEmpty)
          Container(
            height: 120,
            margin: const EdgeInsets.only(bottom: 16),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: widget.imageUrls.length,
              itemBuilder: (context, index) {
                return Container(
                  width: 120,
                  margin: const EdgeInsets.only(right: 8),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          widget.imageUrls[index],
                          width: 120,
                          height: 120,
                          fit: BoxFit.cover,
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Container(
                              width: 120,
                              height: 120,
                              decoration: BoxDecoration(
                                color: Colors.grey[300],
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Center(
                                child: CircularProgressIndicator(),
                              ),
                            );
                          },
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 120,
                              height: 120,
                              decoration: BoxDecoration(
                                color: Colors.grey[300],
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Icon(
                                Icons.error,
                                color: Colors.red,
                              ),
                            );
                          },
                        ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: GestureDetector(
                          onTap: () => _removeImage(index),
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),

        // Upload button with options
        if (widget.imageUrls.length < widget.maxImages && !_isUploading)
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _showUploadOptions(context, localeProvider),
              icon: const Icon(Icons.add_photo_alternate),
              label: Text(
                localeProvider.isRTL ? 'إضافة صورة' : 'Add Image',
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),

        // Image count info
        Padding(
          padding: const EdgeInsets.only(top: 8),
          child: Text(
            localeProvider.isRTL
                ? 'عدد الصور: ${widget.imageUrls.length}/${widget.maxImages}'
                : 'Images: ${widget.imageUrls.length}/${widget.maxImages}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ),
      ],
    );
  }

  void _showUploadOptions(BuildContext context, LocaleProvider localeProvider) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              localeProvider.isRTL ? 'اختر طريقة الرفع' : 'Choose Upload Method',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: _buildUploadOption(
                    icon: Icons.camera_alt,
                    title: localeProvider.isRTL ? 'التقاط صورة' : 'Take Photo',
                    onTap: () {
                      Navigator.pop(context);
                      _pickImage(ImageSource.camera);
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildUploadOption(
                    icon: Icons.photo_library,
                    title: localeProvider.isRTL ? 'تحميل صورة' : 'Upload Image',
                    onTap: () {
                      Navigator.pop(context);
                      _pickImage(ImageSource.gallery);
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: _buildUploadOption(
                icon: Icons.insert_drive_file,
                title: localeProvider.isRTL ? 'تحميل مستند' : 'Upload Document',
                onTap: () {
                  Navigator.pop(context);
                  _pickDocument();
                },
              ),
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  Widget _buildUploadOption({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withAlpha(128),
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickDocument() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx', 'txt'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        await _uploadFile(File(result.files.single.path!), result.files.single.name);
      }
    } catch (e) {
      _showErrorSnackBar('Error picking document: $e');
    }
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: source,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        await _uploadFile(File(image.path), 'image_${DateTime.now().millisecondsSinceEpoch}.jpg');
      }
    } catch (e) {
      _showErrorSnackBar('Error picking image: $e');
    }
  }

  void _cancelUpload() {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localeProvider.isRTL ? 'إلغاء الرفع' : 'Cancel Upload'),
        content: Text(localeProvider.isRTL ? 'هل تريد إلغاء رفع الملف؟' : 'Do you want to cancel the file upload?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(localeProvider.isRTL ? 'لا' : 'No'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _uploadCancelled = true;
                _isUploading = false;
                _uploadProgress = 0.0;
              });
              // Notify parent about upload state change
              widget.onUploadStateChanged?.call(false);
              final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
              _showSuccessSnackBar(
                isRTL ? 'تم إلغاء رفع الملف' : 'File upload cancelled',
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.orange),
            child: Text(localeProvider.isRTL ? 'نعم، إلغاء' : 'Yes, Cancel'),
          ),
        ],
      ),
    );
  }

  Future<void> _uploadFile(File file, String fileName) async {
    setState(() {
      _isUploading = true;
      _uploadProgress = 0.0;
      _uploadCancelled = false;
    });

    // Notify parent about upload state change
    widget.onUploadStateChanged?.call(true);

    try {
      // Generate unique filename with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = fileName.split('.').last;
      final uniqueFileName = 'device_error_$timestamp.$extension';

      // Simulate upload progress with cancellation check
      for (int i = 0; i <= 100; i += 10) {
        if (_uploadCancelled) {
          return; // Exit early if cancelled
        }
        await Future.delayed(const Duration(milliseconds: 100));
        if (mounted && !_uploadCancelled) {
          setState(() {
            _uploadProgress = i / 100;
          });
        }
      }

      // Check if cancelled before actual upload
      if (_uploadCancelled) {
        return;
      }

      // Upload to Google Drive
      final fileUrl = await _storageService.uploadImage(
        file,
        uniqueFileName,
      );

      // Check if cancelled after upload
      if (_uploadCancelled) {
        return;
      }

      if (fileUrl != null) {
        final updatedUrls = List<String>.from(widget.imageUrls)..add(fileUrl);
        widget.onImagesChanged(updatedUrls);

        if (mounted && !_uploadCancelled) {
          final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
          final isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension.toLowerCase());
          _showSuccessSnackBar(
            isRTL
                ? (isImage ? 'تم رفع الصورة بنجاح' : 'تم رفع المستند بنجاح')
                : (isImage ? 'Image uploaded successfully' : 'Document uploaded successfully'),
          );
        }
      } else {
        if (mounted && !_uploadCancelled) {
          final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
          _showErrorSnackBar(
            isRTL ? 'فشل في رفع الملف' : 'Failed to upload file',
          );
        }
      }
    } catch (e) {
      if (!_uploadCancelled) {
        _showErrorSnackBar('Upload error: $e');
      }
    } finally {
      if (mounted && !_uploadCancelled) {
        setState(() {
          _isUploading = false;
          _uploadProgress = 0.0;
        });
        // Notify parent about upload state change
        widget.onUploadStateChanged?.call(false);
      }
    }
  }

  void _removeImage(int index) {
    final updatedUrls = List<String>.from(widget.imageUrls)..removeAt(index);
    widget.onImagesChanged(updatedUrls);
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}