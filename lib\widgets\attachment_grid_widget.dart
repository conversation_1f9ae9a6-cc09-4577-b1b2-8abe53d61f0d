import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/attachment_model.dart';
import 'cached_attachment_widget.dart';
import 'attachment_viewer_dialog.dart';

/// Widget مبسط لعرض الملفات المرفقة في شكل شبكة
class AttachmentGridWidget extends StatelessWidget {
  final List<AttachmentModel> attachments;
  final bool showActions;
  final Function(AttachmentModel)? onDelete;
  final Function(AttachmentModel)? onDownload;

  const AttachmentGridWidget({
    super.key,
    required this.attachments,
    this.showActions = false,
    this.onDelete,
    this.onDownload,
  });

  @override
  Widget build(BuildContext context) {
    if (attachments.isEmpty) {
      return const SizedBox.shrink();
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate optimal grid layout based on available width
        final double availableWidth = constraints.maxWidth;
        final int crossAxisCount = availableWidth > 600 ? 4 : (availableWidth > 400 ? 3 : 2);
        final double itemWidth = (availableWidth - (crossAxisCount - 1) * 8) / crossAxisCount;
        final double childAspectRatio = itemWidth / (itemWidth + 40); // Add space for text

        return GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: childAspectRatio,
          ),
          itemCount: attachments.length,
          itemBuilder: (context, index) {
            final attachment = attachments[index];
            return _buildAttachmentTile(context, attachment);
          },
        );
      },
    );
  }

  Widget _buildAttachmentTile(BuildContext context, AttachmentModel attachment) {
    return GestureDetector(
      onTap: () => _openAttachmentViewer(context, attachment),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(10),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            // Main content area
            Expanded(
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    _buildAttachmentContent(attachment),

                    // نوع الملف
                    Positioned(
                      top: 6,
                      left: 6,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: _getTypeColor(attachment.type),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          _getTypeLabel(attachment.type),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 9,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),

                    // أيقونة التشغيل للفيديوهات
                    if (attachment.isVideo)
                      const Center(
                        child: Icon(
                          Icons.play_circle_outline,
                          color: Colors.white,
                          size: 28,
                        ),
                      ),

                    // قائمة الإجراءات
                    if (showActions)
                      Positioned(
                        top: 6,
                        right: 6,
                        child: _buildActionMenu(context, attachment),
                      ),
                  ],
                ),
              ),
            ),

            // File name and info
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: const BorderRadius.vertical(bottom: Radius.circular(12)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    attachment.originalFileName,
                    style: const TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    _formatFileSize(attachment.fileSize),
                    style: TextStyle(
                      fontSize: 8,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttachmentContent(AttachmentModel attachment) {
    // استخدام النظام الجديد للـ cache
    return CachedAttachmentWidget(
      attachment: attachment,
      fit: BoxFit.cover,
      enableThumbnail: true,
      showLoadingProgress: false,
      placeholder: Container(
        color: Colors.grey.shade200,
        child: const Center(
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      ),
      errorWidget: Container(
        color: Colors.grey.shade200,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.broken_image, color: Colors.red, size: 32),
            const SizedBox(height: 4),
            Text(
              'فشل تحميل الملف',
              style: TextStyle(
                color: Colors.red.shade700,
                fontSize: 10,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              'اضغط للمحاولة مرة أخرى',
              style: TextStyle(
                color: Colors.blue.shade600,
                fontSize: 8,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildActionMenu(BuildContext context, AttachmentModel attachment) {
    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(150),
        borderRadius: BorderRadius.circular(12),
      ),
      child: PopupMenuButton<String>(
        padding: EdgeInsets.zero,
        icon: const Icon(Icons.more_vert, size: 14, color: Colors.white),
        iconSize: 14,
        onSelected: (value) {
          switch (value) {
            case 'download':
              onDownload?.call(attachment);
              break;
            case 'delete':
              onDelete?.call(attachment);
              break;
          }
        },
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        itemBuilder: (context) => [
          PopupMenuItem(
            value: 'download',
            height: 36,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.download, size: 14, color: Colors.blue.shade600),
                const SizedBox(width: 6),
                const Text('تحميل', style: TextStyle(fontSize: 12)),
              ],
            ),
          ),
          if (showActions)
            PopupMenuItem(
              value: 'delete',
              height: 36,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.delete, size: 14, color: Colors.red),
                  const SizedBox(width: 6),
                  const Text('حذف', style: TextStyle(color: Colors.red, fontSize: 12)),
                ],
              ),
            ),
        ],
      ),
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  Color _getTypeColor(AttachmentType type) {
    switch (type) {
      case AttachmentType.image:
        return Colors.green;
      case AttachmentType.video:
        return Colors.blue;
      case AttachmentType.document:
        return Colors.orange;
    }
  }

  String _getTypeLabel(AttachmentType type) {
    switch (type) {
      case AttachmentType.image:
        return 'صورة';
      case AttachmentType.video:
        return 'فيديو';
      case AttachmentType.document:
        return 'مستند';
    }
  }

  void _openAttachmentViewer(BuildContext context, AttachmentModel attachment) {
    // Find the index of current attachment
    final currentIndex = attachments.indexOf(attachment);

    showDialog(
      context: context,
      barrierColor: Colors.black,
      builder: (context) => AttachmentViewerDialog(
        attachments: attachments,
        initialIndex: currentIndex,
        onDelete: showActions ? onDelete : null,
        onDownload: onDownload,
        onShare: (attachment) => _shareAttachment(attachment),
        showActions: showActions,
      ),
    );
  }

  void _shareAttachment(AttachmentModel attachment) {
    // يمكن إضافة منطق المشاركة هنا
    _openUrl(attachment.url);
  }

  void _openUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }
}
