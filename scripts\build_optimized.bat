@echo off
echo ========================================
echo    HM Device Errors v1.0.6 - Maximum Size Optimization Build
echo    Target: Reduce from 51MB to under 35MB
echo ========================================

REM Clean previous builds
echo Cleaning previous builds...
flutter clean
flutter pub get

REM Analyze code for issues
echo Analyzing code...
flutter analyze

REM Run tests
echo Running tests...
flutter test

REM Build optimized release APK with MAXIMUM size reduction
echo Building ultra-optimized release APK...
flutter build apk --release ^
  --shrink ^
  --obfuscate ^
  --split-debug-info=build/debug-info ^
  --target-platform android-arm64,android-arm ^
  --tree-shake-icons ^
  --dart-define=FLUTTER_WEB_USE_SKIA=true ^
  --dart-define=DART_OBFUSCATION=true ^
  --dart-define=ENABLE_LOGGING=false ^
  --dart-define=FLUTTER_WEB_AUTO_DETECT=false ^
  --dart-define=FLUTTER_WEB_CANVASKIT_URL=false ^
  --split-per-abi ^
  --no-pub ^
  --no-track-widget-creation

REM Build separate APKs for each architecture (smaller individual APKs)
echo Building architecture-specific APKs...
flutter build apk --release ^
  --shrink ^
  --obfuscate ^
  --split-debug-info=build/debug-info ^
  --target-platform android-arm64 ^
  --tree-shake-icons ^
  --dart-define=FLUTTER_WEB_USE_SKIA=true ^
  --dart-define=DART_OBFUSCATION=true ^
  --dart-define=ENABLE_LOGGING=false

flutter build apk --release ^
  --shrink ^
  --obfuscate ^
  --split-debug-info=build/debug-info ^
  --target-platform android-arm ^
  --tree-shake-icons ^
  --dart-define=FLUTTER_WEB_USE_SKIA=true ^
  --dart-define=DART_OBFUSCATION=true ^
  --dart-define=ENABLE_LOGGING=false

REM Build App Bundle for Play Store
echo Building App Bundle...
flutter build appbundle --release ^
  --shrink ^
  --obfuscate ^
  --split-debug-info=build/debug-info ^
  --tree-shake-icons ^
  --dart-define=DART_OBFUSCATION=true ^
  --dart-define=ENABLE_LOGGING=false

REM Analyze APK size
echo Analyzing APK size...
flutter build apk --analyze-size --target-platform android-arm64

REM Generate size report
echo Generating size report...
flutter build apk --analyze-size --target-platform android-arm64 > build_size_report.txt

echo Build completed successfully!
echo APK location: build\app\outputs\flutter-apk\app-release.apk
echo App Bundle location: build\app\outputs\bundle\release\app-release.aab
echo Size report: build_size_report.txt

pause
