class Manufacturer {
  final String id;
  final String name;
  final String? description;
  final String? logoUrl;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Manufacturer({
    required this.id,
    required this.name,
    this.description,
    this.logoUrl,
    required this.createdAt,
    this.updatedAt,
  });

  factory Manufacturer.fromMap(Map<String, dynamic> map, String id) {
    return Manufacturer(
      id: id,
      name: map['name'] ?? '',
      description: map['description'],
      logoUrl: map['logoUrl'],
      createdAt: map['createdAt']?.toDate() ?? DateTime.now(),
      updatedAt: map['updatedAt']?.toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'description': description,
      'logoUrl': logoUrl,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  Manufacturer copyWith({
    String? id,
    String? name,
    String? description,
    String? logoUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Manufacturer(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      logoUrl: logoUrl ?? this.logoUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Manufacturer && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Manufacturer(id: $id, name: $name)';
  }
}
