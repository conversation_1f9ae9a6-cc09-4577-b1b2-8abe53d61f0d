import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'attachment_cache_service.dart';
import 'asset_optimization_service.dart';
import '../utils/app_size_analyzer.dart';

/// خدمة تنظيف وتحسين حجم التطبيق
class AppCleanupService {
  static AppCleanupService? _instance;
  static AppCleanupService get instance => _instance ??= AppCleanupService._();

  AppCleanupService._();

  static const String _lastCleanupKey = 'last_cleanup_date';
  static const String _cleanupStatsKey = 'cleanup_stats';
  static const int _cleanupIntervalDays = 7; // تنظيف كل أسبوع

  /// تنظيف شامل للتطبيق
  Future<CleanupResult> performFullCleanup() async {
    debugPrint('Starting full app cleanup...');
    
    final result = CleanupResult();
    final startTime = DateTime.now();

    try {
      // 1. تنظيف cache المرفقات
      final attachmentCleanup = await _cleanupAttachmentCache();
      result.addCleanupItem('Attachment Cache', attachmentCleanup);

      // 2. تنظيف cache الصور
      final imageCleanup = await _cleanupImageCache();
      result.addCleanupItem('Image Cache', imageCleanup);

      // 3. تنظيف الملفات المؤقتة
      final tempCleanup = await _cleanupTempFiles();
      result.addCleanupItem('Temporary Files', tempCleanup);

      // 4. تنظيف logs القديمة
      final logCleanup = await _cleanupOldLogs();
      result.addCleanupItem('Old Logs', logCleanup);

      // 5. تنظيف SharedPreferences
      final prefsCleanup = await _cleanupSharedPreferences();
      result.addCleanupItem('Shared Preferences', prefsCleanup);

      // 6. ضغط قاعدة البيانات (إذا كانت موجودة)
      final dbCleanup = await _optimizeDatabase();
      result.addCleanupItem('Database Optimization', dbCleanup);

      // حفظ إحصائيات التنظيف
      await _saveCleanupStats(result);
      
      // تحديث تاريخ آخر تنظيف
      await _updateLastCleanupDate();

      result.duration = DateTime.now().difference(startTime);
      debugPrint('Full cleanup completed in ${result.duration.inSeconds} seconds');
      debugPrint('Total space freed: ${result.formattedTotalFreed}');

    } catch (e) {
      debugPrint('Error during full cleanup: $e');
      result.hasErrors = true;
      result.errorMessage = e.toString();
    }

    return result;
  }

  /// تنظيف سريع (العناصر الأساسية فقط)
  Future<CleanupResult> performQuickCleanup() async {
    debugPrint('Starting quick cleanup...');
    
    final result = CleanupResult();
    final startTime = DateTime.now();

    try {
      // تنظيف cache المرفقات القديمة فقط
      final attachmentCleanup = await _cleanupOldAttachmentCache();
      result.addCleanupItem('Old Attachment Cache', attachmentCleanup);

      // تنظيف الملفات المؤقتة
      final tempCleanup = await _cleanupTempFiles();
      result.addCleanupItem('Temporary Files', tempCleanup);

      result.duration = DateTime.now().difference(startTime);
      debugPrint('Quick cleanup completed in ${result.duration.inSeconds} seconds');

    } catch (e) {
      debugPrint('Error during quick cleanup: $e');
      result.hasErrors = true;
      result.errorMessage = e.toString();
    }

    return result;
  }

  /// تنظيف cache المرفقات
  Future<int> _cleanupAttachmentCache() async {
    try {
      final cacheService = AttachmentCacheService.instance;
      await cacheService.initialize();
      
      final cacheInfo = await cacheService.getCacheInfo();
      final sizeBefore = cacheInfo.totalSize;
      
      // مسح cache إذا كان كبيراً
      if (sizeBefore > 100 * 1024 * 1024) { // أكبر من 100MB
        await cacheService.clearAllCache();
        return sizeBefore;
      }
      
      return 0;
    } catch (e) {
      debugPrint('Error cleaning attachment cache: $e');
      return 0;
    }
  }

  /// تنظيف cache المرفقات القديمة فقط
  Future<int> _cleanupOldAttachmentCache() async {
    try {
      // هذا سيتم تنفيذه تلقائياً بواسطة AttachmentCacheService
      // عند تهيئته (تنظيف الملفات القديمة)
      final cacheService = AttachmentCacheService.instance;
      await cacheService.initialize();
      return 0; // سيتم حساب الحجم المحرر لاحقاً
    } catch (e) {
      debugPrint('Error cleaning old attachment cache: $e');
      return 0;
    }
  }

  /// تنظيف cache الصور
  Future<int> _cleanupImageCache() async {
    try {
      final cacheStats = AssetOptimizationService.getCacheStats();
      final sizeBefore = cacheStats['currentSize'] as int;
      
      // مسح cache الصور
      AssetOptimizationService.clearCache();
      
      return sizeBefore;
    } catch (e) {
      debugPrint('Error cleaning image cache: $e');
      return 0;
    }
  }

  /// تنظيف الملفات المؤقتة
  Future<int> _cleanupTempFiles() async {
    try {
      final tempDir = await getTemporaryDirectory();
      int totalFreed = 0;
      
      if (await tempDir.exists()) {
        final files = tempDir.listSync(recursive: true);
        
        for (final file in files) {
          if (file is File) {
            try {
              final stat = await file.stat();
              totalFreed += stat.size;
              await file.delete();
            } catch (e) {
              // تجاهل الأخطاء في حذف الملفات الفردية
            }
          }
        }
      }
      
      return totalFreed;
    } catch (e) {
      debugPrint('Error cleaning temp files: $e');
      return 0;
    }
  }

  /// تنظيف logs القديمة
  Future<int> _cleanupOldLogs() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final logsDir = Directory('${appDir.path}/logs');
      int totalFreed = 0;
      
      if (await logsDir.exists()) {
        final files = logsDir.listSync();
        final now = DateTime.now();
        
        for (final file in files) {
          if (file is File) {
            try {
              final stat = await file.stat();
              final age = now.difference(stat.modified).inDays;
              
              // حذف logs أقدم من 30 يوم
              if (age > 30) {
                totalFreed += stat.size;
                await file.delete();
              }
            } catch (e) {
              // تجاهل الأخطاء
            }
          }
        }
      }
      
      return totalFreed;
    } catch (e) {
      debugPrint('Error cleaning old logs: $e');
      return 0;
    }
  }

  /// تنظيف SharedPreferences
  Future<int> _cleanupSharedPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      int cleanedItems = 0;
      
      // إزالة المفاتيح القديمة أو غير المستخدمة
      final keysToRemove = keys.where((key) => 
        key.startsWith('temp_') || 
        key.startsWith('cache_') ||
        key.contains('_old_') ||
        key.endsWith('_deprecated')
      ).toList();
      
      for (final key in keysToRemove) {
        await prefs.remove(key);
        cleanedItems++;
      }
      
      // تقدير الحجم المحرر (تقريبي)
      return cleanedItems * 100; // تقدير 100 بايت لكل مفتاح
    } catch (e) {
      debugPrint('Error cleaning shared preferences: $e');
      return 0;
    }
  }

  /// تحسين قاعدة البيانات
  Future<int> _optimizeDatabase() async {
    try {
      // هذا سيتم تنفيذه لاحقاً عند إضافة قاعدة بيانات محلية
      // يمكن إضافة VACUUM للـ SQLite مثلاً
      return 0;
    } catch (e) {
      debugPrint('Error optimizing database: $e');
      return 0;
    }
  }

  /// حفظ إحصائيات التنظيف
  Future<void> _saveCleanupStats(CleanupResult result) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final stats = {
        'lastCleanup': DateTime.now().toIso8601String(),
        'totalFreed': result.totalFreedBytes,
        'duration': result.duration.inSeconds,
        'itemsCleaned': result.cleanupItems.length,
      };
      
      await prefs.setString(_cleanupStatsKey, jsonEncode(stats));
    } catch (e) {
      debugPrint('Error saving cleanup stats: $e');
    }
  }

  /// تحديث تاريخ آخر تنظيف
  Future<void> _updateLastCleanupDate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastCleanupKey, DateTime.now().toIso8601String());
    } catch (e) {
      debugPrint('Error updating last cleanup date: $e');
    }
  }

  /// التحقق من الحاجة للتنظيف
  Future<bool> needsCleanup() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastCleanupStr = prefs.getString(_lastCleanupKey);
      
      if (lastCleanupStr == null) return true;
      
      final lastCleanup = DateTime.parse(lastCleanupStr);
      final daysSinceCleanup = DateTime.now().difference(lastCleanup).inDays;
      
      return daysSinceCleanup >= _cleanupIntervalDays;
    } catch (e) {
      debugPrint('Error checking cleanup need: $e');
      return true;
    }
  }

  /// الحصول على إحصائيات التنظيف الأخيرة
  Future<Map<String, dynamic>?> getLastCleanupStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsStr = prefs.getString(_cleanupStatsKey);
      
      if (statsStr != null) {
        return jsonDecode(statsStr) as Map<String, dynamic>;
      }
    } catch (e) {
      debugPrint('Error getting cleanup stats: $e');
    }
    
    return null;
  }

  /// تحليل حجم التطبيق الحالي
  Future<Map<String, dynamic>> analyzeAppSize() async {
    return await AppSizeAnalyzer.analyzeAppSize();
  }
}

/// نتيجة عملية التنظيف
class CleanupResult {
  final List<CleanupItem> cleanupItems = [];
  Duration duration = Duration.zero;
  bool hasErrors = false;
  String? errorMessage;

  void addCleanupItem(String name, int bytesFreed) {
    cleanupItems.add(CleanupItem(name, bytesFreed));
  }

  int get totalFreedBytes => cleanupItems.fold(0, (sum, item) => sum + item.bytesFreed);

  String get formattedTotalFreed {
    final bytes = totalFreedBytes;
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}

/// عنصر تنظيف
class CleanupItem {
  final String name;
  final int bytesFreed;

  CleanupItem(this.name, this.bytesFreed);

  String get formattedSize {
    if (bytesFreed < 1024) return '$bytesFreed B';
    if (bytesFreed < 1024 * 1024) return '${(bytesFreed / 1024).toStringAsFixed(1)} KB';
    return '${(bytesFreed / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}
