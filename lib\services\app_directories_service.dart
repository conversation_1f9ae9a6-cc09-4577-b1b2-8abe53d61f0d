import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

/// Service for managing app-specific directories and file storage
class AppDirectoriesService {
  static const String _appFolderName = 'HMDeviceErrors';
  static const String _downloadsFolderName = 'Downloads';
  static const String _dataFolderName = 'Data';
  static const String _cacheFolderName = 'Cache';
  static const String _exportsFolderName = 'Exports';

  static Directory? _appDataDirectory;
  static Directory? _appDownloadsDirectory;
  static Directory? _appCacheDirectory;
  static Directory? _appExportsDirectory;

  /// Initialize app directories
  static Future<void> initialize() async {
    try {
      await _createAppDirectories();
      debugPrint('App directories initialized successfully');
    } catch (e) {
      debugPrint('Error initializing app directories: $e');
      rethrow;
    }
  }

  /// Create all required app directories
  static Future<void> _createAppDirectories() async {
    if (Platform.isAndroid) {
      await _createAndroidDirectories();
    } else if (Platform.isIOS) {
      await _createIOSDirectories();
    } else {
      await _createDesktopDirectories();
    }
  }

  /// Create Android-specific directories
  static Future<void> _createAndroidDirectories() async {
    // Request storage permissions
    await _requestStoragePermissions();

    // Create app data directory in android/data
    final externalDir = await getExternalStorageDirectory();
    if (externalDir != null) {
      _appDataDirectory = Directory('${externalDir.path}/$_dataFolderName');
      await _appDataDirectory!.create(recursive: true);
    }

    // Create downloads directory in public Downloads folder
    try {
      final downloadsDir = Directory('/storage/emulated/0/Download/$_appFolderName');
      _appDownloadsDirectory = downloadsDir;
      await _appDownloadsDirectory!.create(recursive: true);
    } catch (e) {
      // Fallback to app-specific external directory
      final fallbackExternalDir = await getExternalStorageDirectory();
      if (fallbackExternalDir != null) {
        _appDownloadsDirectory = Directory('${fallbackExternalDir.path}/$_downloadsFolderName');
        await _appDownloadsDirectory!.create(recursive: true);
      }
    }

    // Create cache directory
    final cacheDir = await getTemporaryDirectory();
    _appCacheDirectory = Directory('${cacheDir.path}/$_cacheFolderName');
    await _appCacheDirectory!.create(recursive: true);

    // Create exports directory
    if (externalDir != null) {
      _appExportsDirectory = Directory('${externalDir.path}/$_exportsFolderName');
      await _appExportsDirectory!.create(recursive: true);
    }
  }

  /// Create iOS-specific directories
  static Future<void> _createIOSDirectories() async {
    // App data directory
    final documentsDir = await getApplicationDocumentsDirectory();
    _appDataDirectory = Directory('${documentsDir.path}/$_dataFolderName');
    await _appDataDirectory!.create(recursive: true);

    // Downloads directory
    _appDownloadsDirectory = Directory('${documentsDir.path}/$_downloadsFolderName');
    await _appDownloadsDirectory!.create(recursive: true);

    // Cache directory
    final cacheDir = await getTemporaryDirectory();
    _appCacheDirectory = Directory('${cacheDir.path}/$_cacheFolderName');
    await _appCacheDirectory!.create(recursive: true);

    // Exports directory
    _appExportsDirectory = Directory('${documentsDir.path}/$_exportsFolderName');
    await _appExportsDirectory!.create(recursive: true);
  }

  /// Create desktop-specific directories
  static Future<void> _createDesktopDirectories() async {
    final documentsDir = await getApplicationDocumentsDirectory();
    final appDir = Directory('${documentsDir.path}/$_appFolderName');
    await appDir.create(recursive: true);

    _appDataDirectory = Directory('${appDir.path}/$_dataFolderName');
    await _appDataDirectory!.create(recursive: true);

    _appDownloadsDirectory = Directory('${appDir.path}/$_downloadsFolderName');
    await _appDownloadsDirectory!.create(recursive: true);

    _appCacheDirectory = Directory('${appDir.path}/$_cacheFolderName');
    await _appCacheDirectory!.create(recursive: true);

    _appExportsDirectory = Directory('${appDir.path}/$_exportsFolderName');
    await _appExportsDirectory!.create(recursive: true);
  }

  /// Request storage permissions for Android
  static Future<void> _requestStoragePermissions() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.request();
      if (status.isDenied) {
        debugPrint('Storage permission denied');
      }

      // For Android 11+ (API 30+), request manage external storage permission
      if (Platform.isAndroid) {
        final manageStatus = await Permission.manageExternalStorage.request();
        if (manageStatus.isDenied) {
          debugPrint('Manage external storage permission denied');
        }
      }
    }
  }

  /// Get app data directory
  static Directory? get appDataDirectory => _appDataDirectory;

  /// Get app downloads directory
  static Directory? get appDownloadsDirectory => _appDownloadsDirectory;

  /// Get app cache directory
  static Directory? get appCacheDirectory => _appCacheDirectory;

  /// Get app exports directory
  static Directory? get appExportsDirectory => _appExportsDirectory;

  /// Save file to downloads directory
  static Future<File> saveToDownloads(String fileName, List<int> bytes) async {
    if (_appDownloadsDirectory == null) {
      throw Exception('Downloads directory not initialized');
    }

    final file = File('${_appDownloadsDirectory!.path}/$fileName');
    await file.writeAsBytes(bytes);
    debugPrint('File saved to downloads: ${file.path}');
    return file;
  }

  /// Save file to exports directory
  static Future<File> saveToExports(String fileName, List<int> bytes) async {
    if (_appExportsDirectory == null) {
      throw Exception('Exports directory not initialized');
    }

    final file = File('${_appExportsDirectory!.path}/$fileName');
    await file.writeAsBytes(bytes);
    debugPrint('File saved to exports: ${file.path}');
    return file;
  }

  /// Save file to data directory
  static Future<File> saveToData(String fileName, List<int> bytes) async {
    if (_appDataDirectory == null) {
      throw Exception('Data directory not initialized');
    }

    final file = File('${_appDataDirectory!.path}/$fileName');
    await file.writeAsBytes(bytes);
    debugPrint('File saved to data: ${file.path}');
    return file;
  }

  /// Clear cache directory
  static Future<void> clearCache() async {
    if (_appCacheDirectory != null && await _appCacheDirectory!.exists()) {
      await _appCacheDirectory!.delete(recursive: true);
      await _appCacheDirectory!.create(recursive: true);
      debugPrint('Cache directory cleared');
    }
  }

  /// Get directory size in bytes
  static Future<int> getDirectorySize(Directory directory) async {
    int size = 0;
    if (await directory.exists()) {
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          size += await entity.length();
        }
      }
    }
    return size;
  }

  /// Format bytes to human readable string
  static String formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Get storage info
  static Future<Map<String, dynamic>> getStorageInfo() async {
    final dataSize = _appDataDirectory != null ? await getDirectorySize(_appDataDirectory!) : 0;
    final downloadsSize = _appDownloadsDirectory != null ? await getDirectorySize(_appDownloadsDirectory!) : 0;
    final cacheSize = _appCacheDirectory != null ? await getDirectorySize(_appCacheDirectory!) : 0;
    final exportsSize = _appExportsDirectory != null ? await getDirectorySize(_appExportsDirectory!) : 0;

    return {
      'dataSize': dataSize,
      'downloadsSize': downloadsSize,
      'cacheSize': cacheSize,
      'exportsSize': exportsSize,
      'totalSize': dataSize + downloadsSize + cacheSize + exportsSize,
      'dataSizeFormatted': formatBytes(dataSize),
      'downloadsSizeFormatted': formatBytes(downloadsSize),
      'cacheSizeFormatted': formatBytes(cacheSize),
      'exportsSizeFormatted': formatBytes(exportsSize),
      'totalSizeFormatted': formatBytes(dataSize + downloadsSize + cacheSize + exportsSize),
    };
  }
}
