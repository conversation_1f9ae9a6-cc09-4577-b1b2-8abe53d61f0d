import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import '../models/device_error_model.dart';
import '../services/firestore_service.dart';
import '../services/notification_service.dart';
import '../services/storage_service.dart';

class ErrorProvider with ChangeNotifier {
  final FirestoreService _firestoreService = FirestoreService();
  final StorageService _storageService = StorageService();

  List<DeviceError> _errors = [];
  List<DeviceError> _filteredErrors = [];
  List<DeviceError> _favoriteErrors = [];
  bool _isLoading = false;
  String? _error;
  String? _currentUserId;

  List<DeviceError> get errors => _errors;
  List<DeviceError> get filteredErrors => _filteredErrors;
  List<DeviceError> get favoriteErrors => _favoriteErrors;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Fetch all errors
  Future<void> fetchErrors({
    String? categoryId,
    String? manufacturer,
    String? model,
    String? errorCode,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final errors = await _firestoreService.getDeviceErrors(
        categoryId: categoryId,
        manufacturer: manufacturer,
        model: model,
        errorCode: errorCode,
      );

      // Update favorite status for each error based on current user
      if (_currentUserId != null) {
        _errors = errors.map((error) {
          final isFavorite = error.favoriteUsers.contains(_currentUserId);
          return error.copyWith(isFavorite: isFavorite);
        }).toList();
      } else {
        _errors = errors;
      }

      _filteredErrors = List.from(_errors);

      // Update favorites list
      _updateFavoritesList();

      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    } catch (e) {
      debugPrint('Error getting errors: $e');
      // If we have permission issues, use some sample errors
      if (e.toString().contains('permission-denied') || e.toString().contains('collection-not-found')) {
        // Create sample errors if a category is specified
        if (categoryId != null) {
          _errors = [
            DeviceError(
              id: '1',
              categoryId: categoryId,
              manufacturer: 'Samsung',
              model: 'WF45T6000AW',
              errorCode: 'E1',
              description: 'مشكلة في إمدادات المياه',
              solution: 'تحقق من صنبور الماء وتأكد من أنه مفتوح بالكامل',
              createdAt: DateTime.now(),
              createdBy: 'system',
            ),
            DeviceError(
              id: '2',
              categoryId: categoryId,
              manufacturer: 'LG',
              model: 'WT7300CW',
              errorCode: 'LE',
              description: 'المحرك مقفل',
              solution: 'تحقق من عدم وجود أي عوائق في حلة الغسالة',
              createdAt: DateTime.now(),
              createdBy: 'system',
            ),
          ];
        } else {
          _errors = [];
        }
        _filteredErrors = List.from(_errors);
        SchedulerBinding.instance.addPostFrameCallback((_) {
          notifyListeners();
        });
      } else {
        _setError(e.toString());
      }
    } finally {
      _setLoading(false);
    }
  }

  // Fetch errors by category
  Future<void> fetchErrorsByCategory(String categoryId) async {
    return fetchErrors(categoryId: categoryId);
  }

  // Get error by ID
  Future<DeviceError?> getErrorById(String id) async {
    _setLoading(true);
    _clearError();

    try {
      final error = await _firestoreService.getDeviceError(id);
      if (error != null && _currentUserId != null) {
        final isFavorite = error.favoriteUsers.contains(_currentUserId);
        return error.copyWith(isFavorite: isFavorite);
      }
      return error;
    } catch (e) {
      debugPrint('Error getting error by ID: $e');
      // If we have permission issues, return a sample error
      if (e.toString().contains('permission-denied') || e.toString().contains('collection-not-found')) {
        // Check if we have this error in our local list
        final localError = _errors.firstWhere(
          (error) => error.id == id,
          orElse: () => DeviceError(
            id: id,
            categoryId: 'sample',
            manufacturer: 'Sample Manufacturer',
            model: 'Sample Model',
            errorCode: 'S1',
            description: 'هذا مثال لعطل. لا يمكن الوصول إلى قاعدة البيانات حاليًا.',
            solution: 'يرجى التحقق من اتصالك بالإنترنت وإعدادات Firebase الخاصة بك.',
            createdAt: DateTime.now(),
            createdBy: 'system',
          ),
        );
        return localError;
      }
      _setError(e.toString());
      return null;
    } finally {
      _setLoading(false);
    }
  }

  // Add new error
  Future<bool> addError(DeviceError error) async {
    _setLoading(true);
    _clearError();

    try {
      final id = await _firestoreService.addDeviceError(error);
      final newError = error.copyWith(id: id);
      _errors.add(newError);
      _filteredErrors = List.from(_errors);

      // Send notification to all users about the new error
      try {
        await NotificationService.sendNewErrorNotification(newError);
      } catch (notificationError) {
        debugPrint('Error sending notification: $notificationError');
        // Don't fail the error creation if notification fails
      }

      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update error
  Future<bool> updateError(DeviceError error, [String? oldCategoryId]) async {
    _setLoading(true);
    _clearError();

    try {
      // If oldCategoryId is not provided, use the current categoryId
      final effectiveOldCategoryId = oldCategoryId ?? error.categoryId;
      await _firestoreService.updateDeviceError(error, effectiveOldCategoryId);
      final index = _errors.indexWhere((e) => e.id == error.id);
      if (index != -1) {
        _errors[index] = error;
        _filteredErrors = List.from(_errors);
        SchedulerBinding.instance.addPostFrameCallback((_) {
          notifyListeners();
        });
      }
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete error
  Future<bool> deleteError(dynamic errorOrId) async {
    _setLoading(true);
    _clearError();

    try {
      String errorId;
      DeviceError? errorToDelete;

      if (errorOrId is String) {
        errorId = errorOrId;
        // Find the error in our list
        errorToDelete = _errors.firstWhere((e) => e.id == errorId);
      } else if (errorOrId is DeviceError) {
        errorId = errorOrId.id;
        errorToDelete = errorOrId;
      } else {
        throw ArgumentError('Invalid argument type. Expected String or DeviceError');
      }

      // Delete attachments from cloud storage first
      await _deleteErrorAttachments(errorToDelete);

      // Delete error from Firestore
      await _firestoreService.deleteDeviceError(errorToDelete);

      _errors.removeWhere((e) => e.id == errorId);
      _filteredErrors = List.from(_errors);
      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete all attachments for an error from cloud storage
  Future<void> _deleteErrorAttachments(DeviceError error) async {
    try {
      // Initialize storage service if needed
      if (!_storageService.isInitialized) {
        await _storageService.initialize();
      }

      // Delete legacy image URLs
      for (final imageUrl in error.imageUrls) {
        try {
          await _storageService.deleteFile(imageUrl);
          debugPrint('Deleted legacy image: $imageUrl');
        } catch (e) {
          debugPrint('Error deleting legacy image $imageUrl: $e');
          // Continue with other files even if one fails
        }
      }

      // Delete new attachment files
      for (final attachment in error.attachments) {
        try {
          // Delete main file
          await _storageService.deleteFile(attachment.url);
          debugPrint('Deleted attachment: ${attachment.fileName}');

          // Delete thumbnail if exists
          if (attachment.thumbnailUrl != null) {
            await _storageService.deleteFile(attachment.thumbnailUrl!);
            debugPrint('Deleted thumbnail: ${attachment.fileName}');
          }
        } catch (e) {
          debugPrint('Error deleting attachment ${attachment.fileName}: $e');
          // Continue with other files even if one fails
        }
      }
    } catch (e) {
      debugPrint('Error deleting error attachments: $e');
      // Don't throw error here as we still want to delete the error record
    }
  }

  // Delete all errors in a category
  Future<bool> deleteAllErrorsInCategory(String categoryId) async {
    _setLoading(true);
    _clearError();

    try {
      // Get all errors in the category
      final errorsToDelete = _errors.where((error) => error.categoryId == categoryId).toList();

      // Delete each error from Firestore
      for (final error in errorsToDelete) {
        await _firestoreService.deleteDeviceError(error);
      }

      // Remove from local lists
      _errors.removeWhere((error) => error.categoryId == categoryId);
      _filteredErrors.removeWhere((error) => error.categoryId == categoryId);
      _favoriteErrors.removeWhere((error) => error.categoryId == categoryId);

      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Search errors
  Future<void> searchErrors(String query) async {
    _setLoading(true);
    _clearError();

    try {
      if (query.isEmpty) {
        _filteredErrors = List.from(_errors);
      } else {
        final searchResults = await _firestoreService.searchDeviceErrors(query);

        // Update favorite status for search results
        if (_currentUserId != null) {
          _filteredErrors = searchResults.map((error) {
            final isFavorite = error.favoriteUsers.contains(_currentUserId);
            return error.copyWith(isFavorite: isFavorite);
          }).toList();
        } else {
          _filteredErrors = searchResults;
        }
      }
      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Advanced search with multiple filters
  Future<void> searchErrorsAdvanced({
    String? query,
    String? categoryId,
    String? manufacturer,
    String? model,
    String searchType = 'all',
  }) async {
    _setLoading(true);
    _clearError();

    try {
      List<DeviceError> results = [];

      if (query != null && query.isNotEmpty) {
        // Use the existing search method as base
        results = await _firestoreService.searchDeviceErrors(query);
      } else {
        // If no query, get all errors and filter locally
        results = await _firestoreService.getDeviceErrors(
          categoryId: categoryId,
          manufacturer: manufacturer,
          model: model,
        );
      }

      // Apply additional filters
      if (categoryId != null) {
        results = results.where((error) => error.categoryId == categoryId).toList();
      }

      if (manufacturer != null) {
        results = results.where((error) =>
          error.manufacturer.toLowerCase().contains(manufacturer.toLowerCase())
        ).toList();
      }

      if (model != null) {
        results = results.where((error) =>
          error.model.toLowerCase().contains(model.toLowerCase())
        ).toList();
      }

      // Apply search type filter if query is provided
      if (query != null && query.isNotEmpty && searchType != 'all') {
        switch (searchType) {
          case 'manufacturer':
            results = results.where((error) =>
              error.manufacturer.toLowerCase().contains(query.toLowerCase())
            ).toList();
            break;
          case 'model':
            results = results.where((error) =>
              error.model.toLowerCase().contains(query.toLowerCase())
            ).toList();
            break;
          case 'errorCode':
            results = results.where((error) =>
              error.errorCode.toLowerCase().contains(query.toLowerCase())
            ).toList();
            break;
        }
      }

      // Update favorite status for results
      if (_currentUserId != null) {
        _filteredErrors = results.map((error) {
          final isFavorite = error.favoriteUsers.contains(_currentUserId);
          return error.copyWith(isFavorite: isFavorite);
        }).toList();
      } else {
        _filteredErrors = results;
      }

      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    } catch (e) {
      debugPrint('Error in advanced search: $e');
      // Fallback to sample data if there's an error
      if (e.toString().contains('permission-denied') || e.toString().contains('collection-not-found')) {
        _filteredErrors = _createSampleSearchResults(query, manufacturer, model);
        SchedulerBinding.instance.addPostFrameCallback((_) {
          notifyListeners();
        });
      } else {
        _setError(e.toString());
      }
    } finally {
      _setLoading(false);
    }
  }

  // Create sample search results for demo purposes
  List<DeviceError> _createSampleSearchResults(String? query, String? manufacturer, String? model) {
    List<DeviceError> sampleErrors = [
      DeviceError(
        id: 'search_1',
        categoryId: 'washing_machines',
        manufacturer: 'Samsung',
        model: 'WF45T6000AW',
        errorCode: 'E1',
        description: 'مشكلة في إمدادات المياه - لا يدخل الماء إلى الغسالة',
        solution: 'تحقق من صنبور الماء وتأكد من أنه مفتوح بالكامل. افحص خرطوم المياه للتأكد من عدم وجود انسداد.',
        imageUrls: [],
        createdAt: DateTime.now(),
        createdBy: 'system',
      ),
      DeviceError(
        id: 'search_2',
        categoryId: 'washing_machines',
        manufacturer: 'LG',
        model: 'WT7300CW',
        errorCode: 'LE',
        description: 'المحرك مقفل - الغسالة لا تدور',
        solution: 'تحقق من عدم وجود أي عوائق في حلة الغسالة. قم بإعادة توزيع الملابس بشكل متوازن.',
        imageUrls: [],
        createdAt: DateTime.now(),
        createdBy: 'system',
      ),
      DeviceError(
        id: 'search_3',
        categoryId: 'refrigerators',
        manufacturer: 'Whirlpool',
        model: 'WRF535SWHZ',
        errorCode: 'F1',
        description: 'مشكلة في التبريد - الثلاجة لا تبرد بشكل صحيح',
        solution: 'تحقق من إعدادات درجة الحرارة. نظف ملفات المكثف في الجزء الخلفي من الثلاجة.',
        imageUrls: [],
        createdAt: DateTime.now(),
        createdBy: 'system',
      ),
    ];

    // Filter based on search criteria
    if (query != null && query.isNotEmpty) {
      sampleErrors = sampleErrors.where((error) =>
        error.manufacturer.toLowerCase().contains(query.toLowerCase()) ||
        error.model.toLowerCase().contains(query.toLowerCase()) ||
        error.errorCode.toLowerCase().contains(query.toLowerCase()) ||
        error.description.toLowerCase().contains(query.toLowerCase())
      ).toList();
    }

    if (manufacturer != null) {
      sampleErrors = sampleErrors.where((error) =>
        error.manufacturer.toLowerCase().contains(manufacturer.toLowerCase())
      ).toList();
    }

    if (model != null) {
      sampleErrors = sampleErrors.where((error) =>
        error.model.toLowerCase().contains(model.toLowerCase())
      ).toList();
    }

    return sampleErrors;
  }

  // Filter errors
  void filterErrors({
    String? manufacturer,
    String? model,
    String? errorCode,
  }) {
    _filteredErrors = _errors.where((error) {
      bool matchesManufacturer = manufacturer == null ||
          error.manufacturer.toLowerCase().contains(manufacturer.toLowerCase());
      bool matchesModel = model == null ||
          error.model.toLowerCase().contains(model.toLowerCase());
      bool matchesErrorCode = errorCode == null ||
          error.errorCode.toLowerCase().contains(errorCode.toLowerCase());

      return matchesManufacturer && matchesModel && matchesErrorCode;
    }).toList();

    SchedulerBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  // Initialize with current user
  Future<void> initializeWithUser(String userId) async {
    _currentUserId = userId;
    _updateFavoriteStatus();
  }

  // Load all errors (wrapper for fetchErrors)
  Future<void> loadErrors() async {
    await fetchErrors();
  }

  // Toggle favorite status
  Future<bool> toggleFavorite(DeviceError error) async {
    if (_currentUserId == null) return false;

    _setLoading(true);
    _clearError();

    try {
      // Create a new list to avoid modifying the original
      List<String> updatedFavoriteUsers = List<String>.from(error.favoriteUsers);
      bool newIsFavorite = !error.isFavorite;

      if (error.isFavorite) {
        // Remove from favorites
        updatedFavoriteUsers.remove(_currentUserId);
      } else {
        // Add to favorites
        if (!updatedFavoriteUsers.contains(_currentUserId)) {
          updatedFavoriteUsers.add(_currentUserId!);
        }
      }

      // Update in Firestore first
      await _firestoreService.updateDeviceErrorFavorites(error.id, updatedFavoriteUsers);

      // Create updated error object
      final updatedError = error.copyWith(
        favoriteUsers: updatedFavoriteUsers,
        isFavorite: newIsFavorite,
      );

      // Update all instances in local lists
      // Update in _errors list
      final index = _errors.indexWhere((e) => e.id == error.id);
      if (index != -1) {
        _errors[index] = updatedError;
      }

      // Update in _filteredErrors list
      final filteredIndex = _filteredErrors.indexWhere((e) => e.id == error.id);
      if (filteredIndex != -1) {
        _filteredErrors[filteredIndex] = updatedError;
      }

      // Update in _favoriteErrors list
      if (newIsFavorite) {
        // Add to favorites if not already there
        if (!_favoriteErrors.any((e) => e.id == error.id)) {
          _favoriteErrors.add(updatedError);
        }
      } else {
        // Remove from favorites
        _favoriteErrors.removeWhere((e) => e.id == error.id);
      }

      // Notify listeners once after all updates
      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });

      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get favorite errors
  Future<void> fetchFavoriteErrors() async {
    if (_currentUserId == null) return;

    _setLoading(true);
    _clearError();

    try {
      final favoriteErrors = await _firestoreService.getFavoriteErrors(_currentUserId!);

      // Mark all as favorites and ensure they have the current user in favoriteUsers
      List<DeviceError> updatedFavorites = favoriteErrors.map((error) {
        List<String> favoriteUsers = List<String>.from(error.favoriteUsers);
        if (!favoriteUsers.contains(_currentUserId)) {
          favoriteUsers.add(_currentUserId!);
        }
        return error.copyWith(
          isFavorite: true,
          favoriteUsers: favoriteUsers
        );
      }).toList();

      // Check if the list has actually changed before updating
      bool hasChanges = _favoriteErrors.length != updatedFavorites.length;

      if (!hasChanges) {
        // Check if any items are different
        for (int i = 0; i < updatedFavorites.length; i++) {
          if (!_favoriteErrors.any((e) => e.id == updatedFavorites[i].id)) {
            hasChanges = true;
            break;
          }
        }
      }

      if (hasChanges) {
        _favoriteErrors = updatedFavorites;
        SchedulerBinding.instance.addPostFrameCallback((_) {
          notifyListeners();
        });
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Update favorite status for all errors
  void _updateFavoriteStatus() {
    if (_currentUserId == null) return;

    bool hasChanges = false;

    // Update _errors list
    List<DeviceError> updatedErrors = _errors.map((error) {
      final isFavorite = error.favoriteUsers.contains(_currentUserId);
      if (error.isFavorite != isFavorite) {
        hasChanges = true;
        return error.copyWith(isFavorite: isFavorite);
      }
      return error;
    }).toList();

    if (hasChanges) {
      _errors = updatedErrors;
    }

    // Update _filteredErrors list
    hasChanges = false;
    List<DeviceError> updatedFilteredErrors = _filteredErrors.map((error) {
      final isFavorite = error.favoriteUsers.contains(_currentUserId);
      if (error.isFavorite != isFavorite) {
        hasChanges = true;
        return error.copyWith(isFavorite: isFavorite);
      }
      return error;
    }).toList();

    if (hasChanges) {
      _filteredErrors = updatedFilteredErrors;
    }

    // Update favorites list
    _updateFavoritesList();

    // Only notify if there were changes
    if (hasChanges) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }

  // Update favorites list
  void _updateFavoritesList() {
    if (_currentUserId == null) return;

    // Get all errors that should be in favorites
    final newFavorites = _errors.where((error) =>
      error.favoriteUsers.contains(_currentUserId)
    ).toList();

    // Check if the favorites list has changed
    bool hasChanges = false;

    // Check if the lists have different lengths
    if (_favoriteErrors.length != newFavorites.length) {
      hasChanges = true;
    } else {
      // Check if any items are different
      for (int i = 0; i < newFavorites.length; i++) {
        if (!_favoriteErrors.any((e) => e.id == newFavorites[i].id)) {
          hasChanges = true;
          break;
        }
      }
    }

    // Only update if there are changes
    if (hasChanges) {
      _favoriteErrors = newFavorites.map((error) =>
        error.copyWith(isFavorite: true)
      ).toList();
    }
  }

  // Helper methods
  void _setLoading(bool value) {
    if (_isLoading != value) {
      _isLoading = value;
      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }

  void _setError(String error) {
    if (_error != error) {
      _error = error;
      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }

  void _clearError() {
    if (_error != null) {
      _error = null;
      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }
}
