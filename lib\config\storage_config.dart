/// Legacy storage configuration class
///
/// This class is deprecated and will be removed in future versions.
/// Storage configurations are now managed through the database using StorageConfigService.
///
/// For app-wide storage settings that don't change frequently, use AppStorageSettings
/// from the StorageConfigModel.
@Deprecated('Use StorageConfigService and AppStorageSettings instead')
class StorageConfig {
  // NOTE: These constants are deprecated and should not be used.
  // Storage configurations are now managed through the database.

  @Deprecated('Use StorageConfigService.googleDriveConfig instead')
  static const String googleDriveClientId = '';

  @Deprecated('Use StorageConfigService.googleDriveConfig instead')
  static const String googleDriveClientSecret = '';

  @Deprecated('Use StorageConfigService.oneDriveConfig instead')
  static const String oneDriveClientId = '';

  @Deprecated('Use StorageConfigService.oneDriveConfig instead')
  static const String oneDriveClientSecret = '';

  @Deprecated('Use StorageConfigService.oneDriveConfig instead')
  static const String oneDriveRedirectUri = 'https://login.microsoftonline.com/common/oauth2/nativeclient';

  @Deprecated('Use StorageConfigService.defaultConfig instead')
  static const String defaultStorageService = 'google_drive';

  // These settings are now part of AppStorageSettings in the database
  @Deprecated('Use AppStorageSettings.maxImageSize instead')
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB

  @Deprecated('Use AppStorageSettings.maxImagesPerError instead')
  static const int maxImagesPerError = 5;

  @Deprecated('Use AppStorageSettings.supportedImageFormats instead')
  static const List<String> supportedImageFormats = [
    'jpg',
    'jpeg',
    'png',
    'gif',
    'webp',
  ];

  @Deprecated('Use AppStorageSettings.imageQuality instead')
  static const int imageQuality = 85; // 0-100

  @Deprecated('Use AppStorageSettings.maxImageWidth instead')
  static const int maxImageWidth = 1920;

  @Deprecated('Use AppStorageSettings.maxImageHeight instead')
  static const int maxImageHeight = 1080;

  @Deprecated('Use AppStorageSettings.deviceErrorsFolder instead')
  static const String deviceErrorsFolder = 'HM_Device_Errors';

  @Deprecated('Use AppStorageSettings.imagesSubfolder instead')
  static const String imagesSubfolder = 'Images';

  @Deprecated('Use AppStorageSettings.imageCacheDuration instead')
  static const Duration imageCacheDuration = Duration(days: 7);

  @Deprecated('Use AppStorageSettings.maxCacheSize instead')
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
}