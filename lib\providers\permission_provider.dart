import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/permission_model.dart' as perm;
import '../models/user_model.dart';

class PermissionProvider extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  List<perm.UserRole> _roles = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<perm.UserRole> get roles => _roles;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Initialize with system roles
  Future<void> initializeSystemRoles() async {
    _setLoading(true);
    _clearError();

    try {
      // Check if system roles exist in Firestore
      final rolesSnapshot = await _firestore.collection('roles').get();

      if (rolesSnapshot.docs.isEmpty) {
        // Create system roles if they don't exist
        await _createSystemRoles();
      }

      // Load all roles
      await loadRoles();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Create system roles in Firestore
  Future<void> _createSystemRoles() async {
    final batch = _firestore.batch();

    for (final role in perm.SystemRoles.allSystemRoles) {
      final docRef = _firestore.collection('roles').doc(role.id);
      batch.set(docRef, role.toFirestore());
    }

    await batch.commit();
  }

  // Load all roles from Firestore
  Future<void> loadRoles() async {
    _setLoading(true);
    _clearError();

    try {
      final snapshot = await _firestore.collection('roles').get();
      _roles = snapshot.docs.map((doc) => perm.UserRole.fromFirestore(doc)).toList();

      // Sort roles by system roles first, then by name
      _roles.sort((a, b) {
        if (a.isSystemRole && !b.isSystemRole) return -1;
        if (!a.isSystemRole && b.isSystemRole) return 1;
        return a.name.compareTo(b.name);
      });

      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Get role by ID
  perm.UserRole? getRoleById(String roleId) {
    try {
      return _roles.firstWhere((role) => role.id == roleId);
    } catch (e) {
      return null;
    }
  }

  // Create custom role
  Future<bool> createRole(perm.UserRole role) async {
    _setLoading(true);
    _clearError();

    try {
      await _firestore.collection('roles').add(role.toFirestore());
      await loadRoles(); // Reload to get the new role with ID
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update role
  Future<bool> updateRole(perm.UserRole role) async {
    _setLoading(true);
    _clearError();

    try {
      // Don't allow updating system roles
      if (role.isSystemRole) {
        _setError('Cannot update system roles');
        return false;
      }

      await _firestore.collection('roles').doc(role.id).update(
        role.copyWith(updatedAt: DateTime.now()).toFirestore(),
      );

      await loadRoles(); // Reload to get updated data
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete role
  Future<bool> deleteRole(String roleId) async {
    _setLoading(true);
    _clearError();

    try {
      final role = getRoleById(roleId);
      if (role == null) {
        _setError('Role not found');
        return false;
      }

      // Don't allow deleting system roles
      if (role.isSystemRole) {
        _setError('Cannot delete system roles');
        return false;
      }

      // Check if any users are using this role
      final usersWithRole = await _firestore
          .collection('users')
          .where('roleId', isEqualTo: roleId)
          .get();

      if (usersWithRole.docs.isNotEmpty) {
        _setError('Cannot delete role: ${usersWithRole.docs.length} users are assigned to this role');
        return false;
      }

      await _firestore.collection('roles').doc(roleId).delete();
      await loadRoles(); // Reload to update the list
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Assign role to user
  Future<bool> assignRoleToUser(String userId, String roleId) async {
    _setLoading(true);
    _clearError();

    try {
      await _firestore.collection('users').doc(userId).update({
        'roleId': roleId,
      });
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Add custom permission to user
  Future<bool> addCustomPermissionToUser(String userId, perm.PermissionType permission) async {
    _setLoading(true);
    _clearError();

    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        _setError('User not found');
        return false;
      }

      final userData = userDoc.data() as Map<String, dynamic>;
      final currentPermissions = (userData['customPermissions'] as List<dynamic>?)
          ?.map((p) => p.toString())
          .toList() ?? [];

      if (!currentPermissions.contains(permission.toString())) {
        currentPermissions.add(permission.toString());

        await _firestore.collection('users').doc(userId).update({
          'customPermissions': currentPermissions,
        });
      }

      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Remove custom permission from user
  Future<bool> removeCustomPermissionFromUser(String userId, perm.PermissionType permission) async {
    _setLoading(true);
    _clearError();

    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        _setError('User not found');
        return false;
      }

      final userData = userDoc.data() as Map<String, dynamic>;
      final currentPermissions = (userData['customPermissions'] as List<dynamic>?)
          ?.map((p) => p.toString())
          .toList() ?? [];

      currentPermissions.remove(permission.toString());

      await _firestore.collection('users').doc(userId).update({
        'customPermissions': currentPermissions,
      });

      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Check if user has permission
  Future<bool> userHasPermission(String userId, perm.PermissionType permission) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) return false;

      final user = UserModel.fromFirestore(userDoc);
      return user.hasPermission(permission);
    } catch (e) {
      return false;
    }
  }

  // Get users by role
  Future<List<UserModel>> getUsersByRole(String roleId) async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .where('roleId', isEqualTo: roleId)
          .get();

      return snapshot.docs.map((doc) => UserModel.fromFirestore(doc)).toList();
    } catch (e) {
      return [];
    }
  }

  // Get role usage statistics
  Future<Map<String, int>> getRoleUsageStats() async {
    try {
      final Map<String, int> stats = {};

      for (final role in _roles) {
        final users = await getUsersByRole(role.id);
        stats[role.id] = users.length;
      }

      return stats;
    } catch (e) {
      return {};
    }
  }

  // Helper methods
  void _setLoading(bool value) {
    if (_isLoading != value) {
      _isLoading = value;
      notifyListeners();
    }
  }

  void _setError(String error) {
    if (_error != error) {
      _error = error;
      notifyListeners();
    }
  }

  void _clearError() {
    if (_error != null) {
      _error = null;
      notifyListeners();
    }
  }

  // Clear all data (for logout)
  void clear() {
    _roles.clear();
    _error = null;
    _isLoading = false;
    notifyListeners();
  }
}
