import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import '../models/device_category_model.dart';
import '../models/user_model.dart';

/// Utility class to initialize Firebase with sample data
class FirebaseInitializer {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Initialize Firebase with sample data
  static Future<void> initializeWithSampleData() async {
    try {
      // Check if data already exists
      try {
        final categoriesSnapshot = await _firestore.collection('categories').get();
        // Only initialize if no data exists
        if (categoriesSnapshot.docs.isEmpty) {
          await _initializeCategories();
        }
      } catch (e) {
        debugPrint('Error checking categories: $e');
        // Continue with other initialization
      }

      try {
        final usersSnapshot = await _firestore.collection('users').get();
        // Create admin user if no users exist
        if (usersSnapshot.docs.isEmpty) {
          await _createAdminUser();
        }
      } catch (e) {
        debugPrint('Error checking users: $e');
        // Continue with other initialization
      }

      debugPrint('Firebase initialization completed');
    } catch (e) {
      debugPrint('Error initializing Firebase with sample data: $e');
    }
  }

  /// Initialize device categories
  static Future<void> _initializeCategories() async {
    try {
      // Define sample categories
      final categories = [
        DeviceCategory(
          id: 'washing_machines',
          name: 'غسالات',
          description: 'أعطال وإصلاحات الغسالات',
          deviceCount: 0,
        ),
        DeviceCategory(
          id: 'refrigerators',
          name: 'ثلاجات',
          description: 'أعطال وإصلاحات الثلاجات',
          deviceCount: 0,
        ),
        DeviceCategory(
          id: 'air_conditioners',
          name: 'مكيفات',
          description: 'أعطال وإصلاحات المكيفات',
          deviceCount: 0,
        ),
        DeviceCategory(
          id: 'ovens',
          name: 'أفران',
          description: 'أعطال وإصلاحات الأفران',
          deviceCount: 0,
        ),
        DeviceCategory(
          id: 'dishwashers',
          name: 'غسالات أطباق',
          description: 'أعطال وإصلاحات غسالات الأطباق',
          deviceCount: 0,
        ),
      ];

      // Add categories to Firestore
      for (final category in categories) {
        await _firestore
            .collection('categories')
            .doc(category.id)
            .set(category.toMap());
      }

      debugPrint('Sample categories initialized successfully');
    } catch (e) {
      debugPrint('Error initializing categories: $e');
    }
  }

  /// Create admin user
  static Future<void> _createAdminUser() async {
    try {
      // Create admin user in Firebase Auth
      const email = '<EMAIL>';
      const password = 'Admin@123456'; // Match the password in AuthService

      // Check if user already exists
      try {
        await _auth.signInWithEmailAndPassword(
          email: email,
          password: password,
        );
        // User exists, sign out
        await _auth.signOut();
        return;
      } catch (e) {
        // User doesn't exist, continue with creation
      }

      // Create user
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Update display name
      await userCredential.user?.updateDisplayName('Admin');

      // Create user in Firestore
      final userModel = UserModel(
        id: userCredential.user!.uid,
        email: email,
        displayName: 'Admin',
        role: UserRole.admin,
        createdAt: DateTime.now(),
        isActive: true,
      );

      await _firestore
          .collection('users')
          .doc(userCredential.user!.uid)
          .set(userModel.toMap());

      debugPrint('Admin user created successfully');

      // Sign out after creation
      await _auth.signOut();
    } catch (e) {
      debugPrint('Error creating admin user: $e');
    }
  }
}
