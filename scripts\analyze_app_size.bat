@echo off
echo ========================================
echo    HM Device Errors v1.0.4 - App Size Analysis
echo ========================================
echo.

REM Set variables
set BUILD_DIR=build
set REPORTS_DIR=reports
set APK_DIR=%BUILD_DIR%\app\outputs\flutter-apk
set BUNDLE_DIR=%BUILD_DIR%\app\outputs\bundle\release

REM Create reports directory
if not exist %REPORTS_DIR% mkdir %REPORTS_DIR%

echo [1/6] Cleaning previous builds...
flutter clean
flutter pub get

echo.
echo [2/6] Building release APK for analysis...
flutter build apk --release ^
  --shrink ^
  --obfuscate ^
  --split-debug-info=%BUILD_DIR%/debug-info ^
  --target-platform android-arm64,android-arm ^
  --tree-shake-icons ^
  --dart-define=DART_OBFUSCATION=true ^
  --dart-define=ENABLE_LOGGING=false ^
  --analyze-size

echo.
echo [3/6] Building App Bundle for analysis...
flutter build appbundle --release ^
  --shrink ^
  --obfuscate ^
  --split-debug-info=%BUILD_DIR%/debug-info ^
  --tree-shake-icons ^
  --dart-define=DART_OBFUSCATION=true ^
  --dart-define=ENABLE_LOGGING=false ^
  --analyze-size

echo.
echo [4/6] Analyzing APK size breakdown...
if exist "%APK_DIR%\app-release.apk" (
    echo APK Analysis:
    echo -------------
    for %%f in ("%APK_DIR%\app-release.apk") do echo APK Size: %%~zf bytes

    REM Generate detailed APK analysis
    flutter build apk --analyze-size --target-platform android-arm64 > %REPORTS_DIR%\apk_analysis.txt 2>&1

    REM Extract APK contents for analysis
    if exist "%JAVA_HOME%\bin\jar.exe" (
        echo Extracting APK contents...
        "%JAVA_HOME%\bin\jar.exe" -tf "%APK_DIR%\app-release.apk" > %REPORTS_DIR%\apk_contents.txt
    ) else (
        echo Warning: JAVA_HOME not set, skipping APK content extraction
    )
) else (
    echo Error: APK file not found!
)

echo.
echo [5/6] Analyzing App Bundle size...
if exist "%BUNDLE_DIR%\app-release.aab" (
    echo App Bundle Analysis:
    echo -------------------
    for %%f in ("%BUNDLE_DIR%\app-release.aab") do echo Bundle Size: %%~zf bytes

    REM Generate detailed Bundle analysis
    flutter build appbundle --analyze-size > %REPORTS_DIR%\bundle_analysis.txt 2>&1
) else (
    echo Error: App Bundle file not found!
)

echo.
echo [6/6] Generating comprehensive size report...

REM Create comprehensive report
echo ======================================== > %REPORTS_DIR%\size_report.txt
echo    HM Device Errors - Size Analysis Report >> %REPORTS_DIR%\size_report.txt
echo ======================================== >> %REPORTS_DIR%\size_report.txt
echo Generated on: %date% %time% >> %REPORTS_DIR%\size_report.txt
echo. >> %REPORTS_DIR%\size_report.txt

REM APK Information
echo APK ANALYSIS: >> %REPORTS_DIR%\size_report.txt
echo ------------- >> %REPORTS_DIR%\size_report.txt
if exist "%APK_DIR%\app-release.apk" (
    for %%f in ("%APK_DIR%\app-release.apk") do (
        set /a apk_size_mb=%%~zf/1024/1024
        echo APK File: %%~nxf >> %REPORTS_DIR%\size_report.txt
        echo APK Size: %%~zf bytes ^(~!apk_size_mb! MB^) >> %REPORTS_DIR%\size_report.txt
    )
) else (
    echo APK file not found >> %REPORTS_DIR%\size_report.txt
)
echo. >> %REPORTS_DIR%\size_report.txt

REM App Bundle Information
echo APP BUNDLE ANALYSIS: >> %REPORTS_DIR%\size_report.txt
echo -------------------- >> %REPORTS_DIR%\size_report.txt
if exist "%BUNDLE_DIR%\app-release.aab" (
    for %%f in ("%BUNDLE_DIR%\app-release.aab") do (
        set /a bundle_size_mb=%%~zf/1024/1024
        echo Bundle File: %%~nxf >> %REPORTS_DIR%\size_report.txt
        echo Bundle Size: %%~zf bytes ^(~!bundle_size_mb! MB^) >> %REPORTS_DIR%\size_report.txt
    )
) else (
    echo App Bundle file not found >> %REPORTS_DIR%\size_report.txt
)
echo. >> %REPORTS_DIR%\size_report.txt

REM Architecture-specific APKs
echo ARCHITECTURE-SPECIFIC APKS: >> %REPORTS_DIR%\size_report.txt
echo --------------------------- >> %REPORTS_DIR%\size_report.txt
for %%f in ("%APK_DIR%\app-*-release.apk") do (
    set /a arch_size_mb=%%~zf/1024/1024
    echo %%~nxf: %%~zf bytes ^(~!arch_size_mb! MB^) >> %REPORTS_DIR%\size_report.txt
)
echo. >> %REPORTS_DIR%\size_report.txt

REM Dependencies Analysis
echo DEPENDENCIES ANALYSIS: >> %REPORTS_DIR%\size_report.txt
echo ---------------------- >> %REPORTS_DIR%\size_report.txt
echo Analyzing pubspec.yaml dependencies... >> %REPORTS_DIR%\size_report.txt

REM Count dependencies
findstr /C:":" pubspec.yaml | findstr /V /C:"#" | findstr /V /C:"sdk:" | findstr /V /C:"flutter:" > temp_deps.txt
for /f %%i in ('type temp_deps.txt ^| find /c /v ""') do set dep_count=%%i
echo Total dependencies: %dep_count% >> %REPORTS_DIR%\size_report.txt
del temp_deps.txt
echo. >> %REPORTS_DIR%\size_report.txt

REM Major dependencies and estimated sizes
echo Major Dependencies ^(Estimated Sizes^): >> %REPORTS_DIR%\size_report.txt
echo - firebase_core: ~3MB >> %REPORTS_DIR%\size_report.txt
echo - cloud_firestore: ~6MB >> %REPORTS_DIR%\size_report.txt
echo - firebase_storage: ~3MB >> %REPORTS_DIR%\size_report.txt
echo - firebase_auth: ~4MB >> %REPORTS_DIR%\size_report.txt
echo - googleapis: ~8MB >> %REPORTS_DIR%\size_report.txt
echo - cached_network_image: ~2MB >> %REPORTS_DIR%\size_report.txt
echo - image_picker: ~3MB >> %REPORTS_DIR%\size_report.txt
echo - flutter_image_compress: ~2MB >> %REPORTS_DIR%\size_report.txt
echo. >> %REPORTS_DIR%\size_report.txt

REM Optimization Suggestions
echo OPTIMIZATION SUGGESTIONS: >> %REPORTS_DIR%\size_report.txt
echo ------------------------- >> %REPORTS_DIR%\size_report.txt
echo 1. Enable ProGuard/R8 shrinking ^(Already enabled^) >> %REPORTS_DIR%\size_report.txt
echo 2. Use architecture-specific APKs ^(Implemented^) >> %REPORTS_DIR%\size_report.txt
echo 3. Remove unused dependencies >> %REPORTS_DIR%\size_report.txt
echo 4. Optimize image assets ^(Convert to WebP^) >> %REPORTS_DIR%\size_report.txt
echo 5. Use font subsetting for custom fonts >> %REPORTS_DIR%\size_report.txt
echo 6. Enable resource shrinking ^(Already enabled^) >> %REPORTS_DIR%\size_report.txt
echo 7. Consider removing rarely used features >> %REPORTS_DIR%\size_report.txt
echo 8. Implement dynamic feature delivery >> %REPORTS_DIR%\size_report.txt
echo. >> %REPORTS_DIR%\size_report.txt

REM Build Configuration
echo BUILD CONFIGURATION: >> %REPORTS_DIR%\size_report.txt
echo -------------------- >> %REPORTS_DIR%\size_report.txt
echo - Shrinking: Enabled >> %REPORTS_DIR%\size_report.txt
echo - Obfuscation: Enabled >> %REPORTS_DIR%\size_report.txt
echo - Tree Shaking: Enabled >> %REPORTS_DIR%\size_report.txt
echo - Debug Info: Split >> %REPORTS_DIR%\size_report.txt
echo - Target Platforms: ARM64, ARM >> %REPORTS_DIR%\size_report.txt
echo. >> %REPORTS_DIR%\size_report.txt

REM Asset Analysis
echo ASSET ANALYSIS: >> %REPORTS_DIR%\size_report.txt
echo --------------- >> %REPORTS_DIR%\size_report.txt
if exist "assets" (
    echo Analyzing assets folder... >> %REPORTS_DIR%\size_report.txt

    REM Count files in assets
    for /f %%i in ('dir /s /b assets\* 2^>nul ^| find /c /v ""') do set asset_count=%%i
    echo Total asset files: %asset_count% >> %REPORTS_DIR%\size_report.txt

    REM Analyze by type
    echo. >> %REPORTS_DIR%\size_report.txt
    echo Asset breakdown: >> %REPORTS_DIR%\size_report.txt

    if exist "assets\images" (
        for /f %%i in ('dir /s /b assets\images\* 2^>nul ^| find /c /v ""') do echo - Images: %%i files >> %REPORTS_DIR%\size_report.txt
    )

    if exist "assets\fonts" (
        for /f %%i in ('dir /s /b assets\fonts\* 2^>nul ^| find /c /v ""') do echo - Fonts: %%i files >> %REPORTS_DIR%\size_report.txt
    )

    if exist "assets\icons" (
        for /f %%i in ('dir /s /b assets\icons\* 2^>nul ^| find /c /v ""') do echo - Icons: %%i files >> %REPORTS_DIR%\size_report.txt
    )
) else (
    echo Assets folder not found >> %REPORTS_DIR%\size_report.txt
)
echo. >> %REPORTS_DIR%\size_report.txt

REM Performance Metrics
echo PERFORMANCE METRICS: >> %REPORTS_DIR%\size_report.txt
echo -------------------- >> %REPORTS_DIR%\size_report.txt
echo Build time: Measured during build process >> %REPORTS_DIR%\size_report.txt
echo Compression ratio: Varies by content type >> %REPORTS_DIR%\size_report.txt
echo Tree shaking effectiveness: Enabled for unused code removal >> %REPORTS_DIR%\size_report.txt
echo. >> %REPORTS_DIR%\size_report.txt

echo Analysis completed successfully!
echo.
echo ========================================
echo              RESULTS SUMMARY
echo ========================================

REM Display summary
if exist "%APK_DIR%\app-release.apk" (
    for %%f in ("%APK_DIR%\app-release.apk") do (
        set /a apk_mb=%%~zf/1024/1024
        echo APK Size: %%~zf bytes ^(~!apk_mb! MB^)
    )
)

if exist "%BUNDLE_DIR%\app-release.aab" (
    for %%f in ("%BUNDLE_DIR%\app-release.aab") do (
        set /a bundle_mb=%%~zf/1024/1024
        echo Bundle Size: %%~zf bytes ^(~!bundle_mb! MB^)
    )
)

echo.
echo Reports generated in '%REPORTS_DIR%' folder:
echo - size_report.txt ^(Comprehensive analysis^)
echo - apk_analysis.txt ^(Flutter APK analysis^)
echo - bundle_analysis.txt ^(Flutter Bundle analysis^)
echo - apk_contents.txt ^(APK file listing^)
echo.
echo ========================================

REM Open reports folder
if exist %REPORTS_DIR% (
    echo Opening reports folder...
    start "" "%CD%\%REPORTS_DIR%"
)

pause
