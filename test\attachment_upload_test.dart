import 'package:flutter_test/flutter_test.dart';
import 'package:hm_device_errors/services/cloud_upload_service.dart';
import 'package:hm_device_errors/services/enhanced_attachment_service.dart';
import 'package:hm_device_errors/models/attachment_model.dart';
import 'dart:typed_data';

void main() {
  group('Attachment Upload Tests', () {
    test('CloudUploadService should validate file data', () {
      // Test file validation
      final testAttachment = EnhancedAttachmentModel(
        id: 'test_id',
        fileName: 'test.jpg',
        originalFileName: 'test.jpg',
        type: AttachmentType.image,
        url: '',
        fileSize: 1024,
        mimeType: 'image/jpeg',
        uploadedAt: DateTime.now(),
        uploadedBy: 'test_user',
        fileData: Uint8List.fromList([1, 2, 3, 4]), // Small test data
      );

      final isValid = CloudUploadService.validateFileForUpload(
        testAttachment,
        testAttachment.fileData!,
      );

      expect(isValid, isTrue);
    });

    test('CloudUploadService should reject oversized files', () {
      // Test oversized file rejection
      final oversizedData = Uint8List(15 * 1024 * 1024); // 15MB for image (max 10MB)
      
      final testAttachment = EnhancedAttachmentModel(
        id: 'test_id',
        fileName: 'large_test.jpg',
        originalFileName: 'large_test.jpg',
        type: AttachmentType.image,
        url: '',
        fileSize: oversizedData.length,
        mimeType: 'image/jpeg',
        uploadedAt: DateTime.now(),
        uploadedBy: 'test_user',
        fileData: oversizedData,
      );

      final isValid = CloudUploadService.validateFileForUpload(
        testAttachment,
        testAttachment.fileData!,
      );

      expect(isValid, isFalse);
    });

    test('CloudUploadService should format file sizes correctly', () {
      expect(CloudUploadService.formatFileSize(512), equals('512 B'));
      expect(CloudUploadService.formatFileSize(1536), equals('1.5 KB'));
      expect(CloudUploadService.formatFileSize(2097152), equals('2.0 MB'));
      expect(CloudUploadService.formatFileSize(1073741824), equals('1.0 GB'));
    });

    test('CloudUploadService should provide correct progress status', () {
      expect(CloudUploadService.getProgressStatus(0.1), equals('جاري التحضير...'));
      expect(CloudUploadService.getProgressStatus(0.3), equals('جاري الرفع...'));
      expect(CloudUploadService.getProgressStatus(0.7), equals('جاري المعالجة...'));
      expect(CloudUploadService.getProgressStatus(0.95), equals('جاري الإنهاء...'));
      expect(CloudUploadService.getProgressStatus(1.0), equals('تم الرفع بنجاح'));
    });

    test('EnhancedAttachmentModel should preserve file data', () {
      final testData = Uint8List.fromList([1, 2, 3, 4, 5]);
      
      final attachment = EnhancedAttachmentModel(
        id: 'test_id',
        fileName: 'test.pdf',
        originalFileName: 'test.pdf',
        type: AttachmentType.document,
        url: '',
        fileSize: testData.length,
        mimeType: 'application/pdf',
        uploadedAt: DateTime.now(),
        uploadedBy: 'test_user',
        fileData: testData,
      );

      expect(attachment.fileData, equals(testData));
      expect(attachment.fileData!.length, equals(5));
    });

    test('EnhancedAttachmentModel copyWith should preserve file data', () {
      final testData = Uint8List.fromList([1, 2, 3, 4, 5]);
      
      final originalAttachment = EnhancedAttachmentModel(
        id: 'test_id',
        fileName: 'test.pdf',
        originalFileName: 'test.pdf',
        type: AttachmentType.document,
        url: '',
        fileSize: testData.length,
        mimeType: 'application/pdf',
        uploadedAt: DateTime.now(),
        uploadedBy: 'test_user',
        fileData: testData,
      );

      final copiedAttachment = originalAttachment.copyWith(
        url: 'https://example.com/test.pdf',
      );

      expect(copiedAttachment.fileData, equals(testData));
      expect(copiedAttachment.url, equals('https://example.com/test.pdf'));
      expect(copiedAttachment.fileName, equals(originalAttachment.fileName));
    });

    test('AttachmentModel should determine correct types from extensions', () {
      expect(AttachmentModel.getTypeFromExtension('jpg'), equals(AttachmentType.image));
      expect(AttachmentModel.getTypeFromExtension('mp4'), equals(AttachmentType.video));
      expect(AttachmentModel.getTypeFromExtension('pdf'), equals(AttachmentType.document));
      expect(AttachmentModel.getTypeFromExtension('unknown'), equals(AttachmentType.document));
    });

    test('AttachmentModel should determine correct types from MIME types', () {
      expect(AttachmentModel.getTypeFromMimeType('image/jpeg'), equals(AttachmentType.image));
      expect(AttachmentModel.getTypeFromMimeType('video/mp4'), equals(AttachmentType.video));
      expect(AttachmentModel.getTypeFromMimeType('application/pdf'), equals(AttachmentType.document));
      expect(AttachmentModel.getTypeFromMimeType('unknown/type'), equals(AttachmentType.document));
    });

    test('AttachmentModel should provide correct supported extensions', () {
      final imageExtensions = AttachmentModel.getSupportedExtensions(AttachmentType.image);
      expect(imageExtensions, contains('jpg'));
      expect(imageExtensions, contains('png'));
      expect(imageExtensions, contains('gif'));

      final videoExtensions = AttachmentModel.getSupportedExtensions(AttachmentType.video);
      expect(videoExtensions, contains('mp4'));
      expect(videoExtensions, contains('mov'));

      final documentExtensions = AttachmentModel.getSupportedExtensions(AttachmentType.document);
      expect(documentExtensions, contains('pdf'));
      expect(documentExtensions, contains('doc'));
      expect(documentExtensions, contains('docx'));
    });
  });

  group('File Size Validation Tests', () {
    test('Should accept files within size limits', () {
      // Image: 5MB (under 10MB limit)
      final imageData = Uint8List(5 * 1024 * 1024);
      final imageAttachment = EnhancedAttachmentModel(
        id: 'test',
        fileName: 'test.jpg',
        originalFileName: 'test.jpg',
        type: AttachmentType.image,
        url: '',
        fileSize: imageData.length,
        mimeType: 'image/jpeg',
        uploadedAt: DateTime.now(),
        uploadedBy: 'test',
        fileData: imageData,
      );
      expect(CloudUploadService.validateFileForUpload(imageAttachment, imageData), isTrue);

      // Video: 30MB (under 50MB limit)
      final videoData = Uint8List(30 * 1024 * 1024);
      final videoAttachment = EnhancedAttachmentModel(
        id: 'test',
        fileName: 'test.mp4',
        originalFileName: 'test.mp4',
        type: AttachmentType.video,
        url: '',
        fileSize: videoData.length,
        mimeType: 'video/mp4',
        uploadedAt: DateTime.now(),
        uploadedBy: 'test',
        fileData: videoData,
      );
      expect(CloudUploadService.validateFileForUpload(videoAttachment, videoData), isTrue);

      // Document: 15MB (under 25MB limit)
      final docData = Uint8List(15 * 1024 * 1024);
      final docAttachment = EnhancedAttachmentModel(
        id: 'test',
        fileName: 'test.pdf',
        originalFileName: 'test.pdf',
        type: AttachmentType.document,
        url: '',
        fileSize: docData.length,
        mimeType: 'application/pdf',
        uploadedAt: DateTime.now(),
        uploadedBy: 'test',
        fileData: docData,
      );
      expect(CloudUploadService.validateFileForUpload(docAttachment, docData), isTrue);
    });

    test('Should reject files exceeding size limits', () {
      // Image: 15MB (over 10MB limit)
      final imageData = Uint8List(15 * 1024 * 1024);
      final imageAttachment = EnhancedAttachmentModel(
        id: 'test',
        fileName: 'test.jpg',
        originalFileName: 'test.jpg',
        type: AttachmentType.image,
        url: '',
        fileSize: imageData.length,
        mimeType: 'image/jpeg',
        uploadedAt: DateTime.now(),
        uploadedBy: 'test',
        fileData: imageData,
      );
      expect(CloudUploadService.validateFileForUpload(imageAttachment, imageData), isFalse);

      // Video: 60MB (over 50MB limit)
      final videoData = Uint8List(60 * 1024 * 1024);
      final videoAttachment = EnhancedAttachmentModel(
        id: 'test',
        fileName: 'test.mp4',
        originalFileName: 'test.mp4',
        type: AttachmentType.video,
        url: '',
        fileSize: videoData.length,
        mimeType: 'video/mp4',
        uploadedAt: DateTime.now(),
        uploadedBy: 'test',
        fileData: videoData,
      );
      expect(CloudUploadService.validateFileForUpload(videoAttachment, videoData), isFalse);

      // Document: 30MB (over 25MB limit)
      final docData = Uint8List(30 * 1024 * 1024);
      final docAttachment = EnhancedAttachmentModel(
        id: 'test',
        fileName: 'test.pdf',
        originalFileName: 'test.pdf',
        type: AttachmentType.document,
        url: '',
        fileSize: docData.length,
        mimeType: 'application/pdf',
        uploadedAt: DateTime.now(),
        uploadedBy: 'test',
        fileData: docData,
      );
      expect(CloudUploadService.validateFileForUpload(docAttachment, docData), isFalse);
    });
  });
}
