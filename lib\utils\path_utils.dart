import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart' as path_provider;
import 'platform_utils.dart';

class PathUtils {
  static Directory? _webTempDirectory;
  static Directory? _webDocumentsDirectory;
  static Directory? _webSupportDirectory;

  /// Get temporary directory with web fallback
  static Future<Directory> getTemporaryDirectory() async {
    if (PlatformUtils.isWeb) {
      // For web, use a virtual directory concept
      _webTempDirectory ??= _createWebDirectory('temp');
      return _webTempDirectory!;
    }

    try {
      return await path_provider.getTemporaryDirectory();
    } catch (e) {
      debugPrint('Error getting temporary directory: $e');
      // Fallback to current directory
      return Directory.current;
    }
  }

  /// Get application documents directory with web fallback
  static Future<Directory> getApplicationDocumentsDirectory() async {
    if (PlatformUtils.isWeb) {
      _webDocumentsDirectory ??= _createWebDirectory('documents');
      return _webDocumentsDirectory!;
    }

    try {
      return await path_provider.getApplicationDocumentsDirectory();
    } catch (e) {
      debugPrint('Error getting documents directory: $e');
      return Directory.current;
    }
  }

  /// Get application support directory with web fallback
  static Future<Directory> getApplicationSupportDirectory() async {
    if (PlatformUtils.isWeb) {
      _webSupportDirectory ??= _createWebDirectory('support');
      return _webSupportDirectory!;
    }

    try {
      return await path_provider.getApplicationSupportDirectory();
    } catch (e) {
      debugPrint('Error getting support directory: $e');
      return Directory.current;
    }
  }

  /// Create a virtual directory for web
  static Directory _createWebDirectory(String name) {
    // For web, we'll use a virtual path that doesn't actually exist
    // This is mainly for compatibility - actual file operations won't work
    return Directory('/virtual/$name');
  }

  /// Check if directory operations are supported
  static bool get isDirectoryOperationSupported => !PlatformUtils.isWeb;

  /// Safe directory size calculation
  static Future<int> calculateDirectorySize(Directory directory) async {
    if (PlatformUtils.isWeb) {
      return 0; // Can't calculate size on web
    }

    try {
      int size = 0;
      if (await directory.exists()) {
        await for (final entity in directory.list(recursive: true)) {
          if (entity is File) {
            size += await entity.length();
          }
        }
      }
      return size;
    } catch (e) {
      debugPrint('Error calculating directory size: $e');
      return 0;
    }
  }

  /// Safe file operations
  static Future<bool> writeFile(String path, List<int> bytes) async {
    if (PlatformUtils.isWeb) {
      debugPrint('File write not supported on web: $path');
      return false;
    }

    try {
      final file = File(path);
      await file.writeAsBytes(bytes);
      return true;
    } catch (e) {
      debugPrint('Error writing file: $e');
      return false;
    }
  }

  /// Safe file read
  static Future<List<int>?> readFile(String path) async {
    if (PlatformUtils.isWeb) {
      debugPrint('File read not supported on web: $path');
      return null;
    }

    try {
      final file = File(path);
      if (await file.exists()) {
        return await file.readAsBytes();
      }
      return null;
    } catch (e) {
      debugPrint('Error reading file: $e');
      return null;
    }
  }

  /// Safe file deletion
  static Future<bool> deleteFile(String path) async {
    if (PlatformUtils.isWeb) {
      debugPrint('File deletion not supported on web: $path');
      return false;
    }

    try {
      final file = File(path);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error deleting file: $e');
      return false;
    }
  }

  /// Safe directory creation
  static Future<bool> createDirectory(String path) async {
    if (PlatformUtils.isWeb) {
      debugPrint('Directory creation not supported on web: $path');
      return false;
    }

    try {
      final directory = Directory(path);
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }
      return true;
    } catch (e) {
      debugPrint('Error creating directory: $e');
      return false;
    }
  }

  /// Clean temporary files
  static Future<void> cleanTemporaryFiles() async {
    if (PlatformUtils.isWeb) {
      debugPrint('Temporary file cleanup not supported on web');
      return;
    }

    try {
      final tempDir = await getTemporaryDirectory();
      if (await tempDir.exists()) {
        await for (final entity in tempDir.list()) {
          if (entity is File) {
            final stat = await entity.stat();
            // Delete files older than 1 day
            if (DateTime.now().difference(stat.modified).inDays > 1) {
              await entity.delete();
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error cleaning temporary files: $e');
    }
  }
}
