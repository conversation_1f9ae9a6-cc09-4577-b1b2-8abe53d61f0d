import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/theme_provider.dart';
import '../../services/app_cleanup_service.dart';
import '../../services/asset_optimization_service.dart';
import '../../services/dependency_optimizer_service.dart';
import '../../services/font_asset_optimizer_service.dart';


class AppSizeManagementScreen extends StatefulWidget {
  const AppSizeManagementScreen({super.key});

  @override
  State<AppSizeManagementScreen> createState() => _AppSizeManagementScreenState();
}

class _AppSizeManagementScreenState extends State<AppSizeManagementScreen> {
  bool _isLoading = false;
  Map<String, dynamic>? _sizeAnalysis;
  Map<String, dynamic>? _lastCleanupStats;
  bool _needsCleanup = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final cleanupService = AppCleanupService.instance;

      // تحليل حجم التطبيق
      final analysis = await cleanupService.analyzeAppSize();

      // إحصائيات التنظيف الأخيرة
      final lastStats = await cleanupService.getLastCleanupStats();

      // التحقق من الحاجة للتنظيف
      final needsCleanup = await cleanupService.needsCleanup();

      setState(() {
        _sizeAnalysis = analysis;
        _lastCleanupStats = lastStats;
        _needsCleanup = needsCleanup;
      });
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDark = themeProvider.isDarkMode;

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'إدارة حجم التطبيق',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _loadData,
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: isDark
                ? [Colors.grey[900]!, Colors.grey[800]!]
                : [Colors.blue[50]!, Colors.white],
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : RefreshIndicator(
                onRefresh: _loadData,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSizeOverviewCard(isDark),
                      const SizedBox(height: 16),
                      _buildCleanupActionsCard(isDark),
                      const SizedBox(height: 16),
                      _buildOptimizationSuggestionsCard(isDark),
                      const SizedBox(height: 16),
                      _buildLastCleanupCard(isDark),
                      const SizedBox(height: 16),
                      _buildCacheDetailsCard(isDark),
                      const SizedBox(height: 16),
                      _buildAdvancedAnalysisCard(isDark),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildSizeOverviewCard(bool isDark) {
    if (_sizeAnalysis == null) return const SizedBox.shrink();

    final appSize = _sizeAnalysis!['app_size'] as Map<String, dynamic>?;
    final formatted = appSize?['formatted'] as Map<String, dynamic>?;

    return Card(
      elevation: 4,
      color: isDark ? Colors.grey[800] : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.storage,
                  color: isDark ? Colors.blue[300] : Colors.blue[600],
                ),
                const SizedBox(width: 8),
                Text(
                  'نظرة عامة على الحجم',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildSizeItem('إجمالي الحجم', formatted?['total'] ?? 'غير متاح', isDark),
            _buildSizeItem('المستندات', formatted?['documents'] ?? 'غير متاح', isDark),
            _buildSizeItem('التخزين المؤقت', formatted?['cache'] ?? 'غير متاح', isDark),
            _buildSizeItem('الدعم', formatted?['support'] ?? 'غير متاح', isDark),
          ],
        ),
      ),
    );
  }

  Widget _buildSizeItem(String label, String value, bool isDark) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: isDark ? Colors.grey[300] : Colors.grey[700],
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCleanupActionsCard(bool isDark) {
    return Card(
      elevation: 4,
      color: isDark ? Colors.grey[800] : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.cleaning_services,
                  color: isDark ? Colors.green[300] : Colors.green[600],
                ),
                const SizedBox(width: 8),
                Text(
                  'إجراءات التنظيف',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_needsCleanup)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange[300]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange[700]),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'يُنصح بإجراء تنظيف للتطبيق',
                        style: TextStyle(color: Colors.orange[700]),
                      ),
                    ),
                  ],
                ),
              ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _performQuickCleanup,
                    icon: const Icon(Icons.flash_on),
                    label: const Text('تنظيف سريع'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isDark ? Colors.blue[700] : Colors.blue[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _performFullCleanup,
                    icon: const Icon(Icons.cleaning_services),
                    label: const Text('تنظيف شامل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isDark ? Colors.green[700] : Colors.green[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptimizationSuggestionsCard(bool isDark) {
    if (_sizeAnalysis == null) return const SizedBox.shrink();

    final suggestions = _sizeAnalysis!['optimization_suggestions'] as List<dynamic>? ?? [];

    return Card(
      elevation: 4,
      color: isDark ? Colors.grey[800] : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.lightbulb,
                  color: isDark ? Colors.yellow[300] : Colors.yellow[700],
                ),
                const SizedBox(width: 8),
                Text(
                  'اقتراحات التحسين',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...suggestions.take(3).map((suggestion) => _buildSuggestionItem(suggestion, isDark)),
            if (suggestions.length > 3)
              TextButton(
                onPressed: () => _showAllSuggestions(suggestions, isDark),
                child: Text('عرض جميع الاقتراحات (${suggestions.length})'),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSuggestionItem(dynamic suggestion, bool isDark) {
    final title = suggestion['title'] ?? '';
    final description = suggestion['description'] ?? '';
    final priority = suggestion['priority'] ?? 'low';
    final estimatedSaving = suggestion['estimatedSaving'] ?? '';

    Color priorityColor;
    switch (priority) {
      case 'high':
        priorityColor = Colors.red;
        break;
      case 'medium':
        priorityColor = Colors.orange;
        break;
      default:
        priorityColor = Colors.green;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[700] : Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: priorityColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: priorityColor,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : Colors.black87,
                  ),
                ),
              ),
              if (estimatedSaving.isNotEmpty)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.green[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    estimatedSaving,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.green[700],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(
              fontSize: 12,
              color: isDark ? Colors.grey[300] : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLastCleanupCard(bool isDark) {
    return Card(
      elevation: 4,
      color: isDark ? Colors.grey[800] : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.history,
                  color: isDark ? Colors.purple[300] : Colors.purple[600],
                ),
                const SizedBox(width: 8),
                Text(
                  'آخر تنظيف',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_lastCleanupStats != null) ...[
              _buildSizeItem('المساحة المحررة', _formatBytes(_lastCleanupStats!['totalFreed'] ?? 0), isDark),
              _buildSizeItem('المدة', '${_lastCleanupStats!['duration'] ?? 0} ثانية', isDark),
              _buildSizeItem('العناصر المنظفة', '${_lastCleanupStats!['itemsCleaned'] ?? 0}', isDark),
            ] else
              Text(
                'لم يتم إجراء تنظيف من قبل',
                style: TextStyle(
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCacheDetailsCard(bool isDark) {
    return Card(
      elevation: 4,
      color: isDark ? Colors.grey[800] : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.cached,
                  color: isDark ? Colors.cyan[300] : Colors.cyan[600],
                ),
                const SizedBox(width: 8),
                Text(
                  'تفاصيل التخزين المؤقت',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            FutureBuilder<Map<String, dynamic>>(
              future: Future.value(AssetOptimizationService.getCacheStats()),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  final stats = snapshot.data!;
                  return Column(
                    children: [
                      _buildSizeItem('حجم cache الصور', stats['formattedCurrentSize'] ?? '0 B', isDark),
                      _buildSizeItem('عدد العناصر', '${stats['itemCount'] ?? 0}', isDark),
                      _buildSizeItem('نسبة الاستخدام', '${stats['usagePercentage'] ?? 0}%', isDark),
                    ],
                  );
                }
                return const CircularProgressIndicator();
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _performQuickCleanup() async {
    setState(() => _isLoading = true);

    try {
      final cleanupService = AppCleanupService.instance;
      final result = await cleanupService.performQuickCleanup();

      if (result.hasErrors) {
        _showErrorSnackBar('خطأ في التنظيف: ${result.errorMessage}');
      } else {
        _showSuccessSnackBar('تم التنظيف السريع بنجاح! تم تحرير ${result.formattedTotalFreed}');
        await _loadData();
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في التنظيف السريع: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _performFullCleanup() async {
    final confirmed = await _showConfirmationDialog(
      'تنظيف شامل',
      'هل أنت متأكد من إجراء تنظيف شامل؟ سيتم حذف جميع الملفات المؤقتة والـ cache.',
    );

    if (!confirmed) return;

    setState(() => _isLoading = true);

    try {
      final cleanupService = AppCleanupService.instance;
      final result = await cleanupService.performFullCleanup();

      if (result.hasErrors) {
        _showErrorSnackBar('خطأ في التنظيف: ${result.errorMessage}');
      } else {
        _showSuccessSnackBar('تم التنظيف الشامل بنجاح! تم تحرير ${result.formattedTotalFreed}');
        await _loadData();
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في التنظيف الشامل: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showAllSuggestions(List<dynamic> suggestions, bool isDark) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('جميع اقتراحات التحسين'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: suggestions.length,
            itemBuilder: (context, index) => _buildSuggestionItem(suggestions[index], isDark),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Future<bool> _showConfirmationDialog(String title, String message) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Widget _buildAdvancedAnalysisCard(bool isDark) {
    return Card(
      elevation: 4,
      color: isDark ? Colors.grey[800] : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: isDark ? Colors.orange[300] : Colors.orange[600],
                ),
                const SizedBox(width: 8),
                Text(
                  'تحليل متقدم',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _analyzeDependencies,
                    icon: const Icon(Icons.extension),
                    label: const Text('تحليل التبعيات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isDark ? Colors.orange[700] : Colors.orange[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _analyzeFontsAndAssets,
                    icon: const Icon(Icons.font_download),
                    label: const Text('تحليل الخطوط'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isDark ? Colors.purple[700] : Colors.purple[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _generateDetailedReport,
                icon: const Icon(Icons.assessment),
                label: const Text('تقرير مفصل'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: isDark ? Colors.teal[700] : Colors.teal[600],
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _analyzeDependencies() async {
    setState(() => _isLoading = true);

    try {
      final dependencyService = DependencyOptimizerService.instance;
      final analysis = await dependencyService.analyzeDependencies();

      _showAnalysisDialog(
        'تحليل التبعيات',
        'إجمالي التبعيات: ${analysis.totalDependencies}\n'
        'الحجم المقدر: ${analysis.formattedTotalSize}\n'
        'التوفير المحتمل: ${analysis.formattedPotentialSaving}\n'
        'اقتراحات التحسين: ${analysis.optimizationSuggestions.length}',
      );
    } catch (e) {
      _showErrorSnackBar('خطأ في تحليل التبعيات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _analyzeFontsAndAssets() async {
    setState(() => _isLoading = true);

    try {
      final fontAssetService = FontAssetOptimizerService.instance;
      final fontAnalysis = await fontAssetService.analyzeFonts();
      final assetAnalysis = await fontAssetService.analyzeAssets();

      _showAnalysisDialog(
        'تحليل الخطوط والأصول',
        'الخطوط:\n'
        '- إجمالي الخطوط: ${fontAnalysis.totalFonts}\n'
        '- الحجم الإجمالي: ${_formatBytes(fontAnalysis.totalSize)}\n\n'
        'الأصول:\n'
        '- إجمالي الأصول: ${assetAnalysis.totalAssets}\n'
        '- الحجم الإجمالي: ${_formatBytes(assetAnalysis.totalSize)}\n\n'
        'اقتراحات التحسين: ${fontAnalysis.optimizationSuggestions.length + assetAnalysis.optimizationSuggestions.length}',
      );
    } catch (e) {
      _showErrorSnackBar('خطأ في تحليل الخطوط والأصول: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _generateDetailedReport() async {
    setState(() => _isLoading = true);

    try {
      // إنشاء تقرير شامل
      final report = StringBuffer();
      report.writeln('=== تقرير تحليل حجم التطبيق ===\n');
      report.writeln('التاريخ: ${DateTime.now().toString().split('.')[0]}\n');

      // معلومات الحجم العامة
      if (_sizeAnalysis != null) {
        final appSize = _sizeAnalysis!['app_size'] as Map<String, dynamic>?;
        final formatted = appSize?['formatted'] as Map<String, dynamic>?;

        report.writeln('الحجم العام:');
        report.writeln('- إجمالي الحجم: ${formatted?['total'] ?? 'غير متاح'}');
        report.writeln('- المستندات: ${formatted?['documents'] ?? 'غير متاح'}');
        report.writeln('- التخزين المؤقت: ${formatted?['cache'] ?? 'غير متاح'}');
        report.writeln('- الدعم: ${formatted?['support'] ?? 'غير متاح'}\n');
      }

      // إحصائيات التنظيف
      if (_lastCleanupStats != null) {
        report.writeln('آخر تنظيف:');
        report.writeln('- المساحة المحررة: ${_formatBytes(_lastCleanupStats!['totalFreed'] ?? 0)}');
        report.writeln('- المدة: ${_lastCleanupStats!['duration'] ?? 0} ثانية');
        report.writeln('- العناصر المنظفة: ${_lastCleanupStats!['itemsCleaned'] ?? 0}\n');
      }

      // اقتراحات التحسين
      if (_sizeAnalysis != null) {
        final suggestions = _sizeAnalysis!['optimization_suggestions'] as List<dynamic>? ?? [];
        report.writeln('اقتراحات التحسين (${suggestions.length}):');
        for (int i = 0; i < suggestions.length && i < 5; i++) {
          final suggestion = suggestions[i];
          report.writeln('${i + 1}. ${suggestion['title']}');
          report.writeln('   ${suggestion['description']}');
          if (suggestion['estimatedSaving'] != null) {
            report.writeln('   التوفير المقدر: ${suggestion['estimatedSaving']}');
          }
          report.writeln('');
        }
      }

      _showReportDialog('تقرير مفصل', report.toString());
    } catch (e) {
      _showErrorSnackBar('خطأ في إنشاء التقرير: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showAnalysisDialog(String title, String content) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: SingleChildScrollView(
          child: Text(content),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showReportDialog(String title, String content) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: SingleChildScrollView(
            child: Text(
              content,
              style: const TextStyle(fontFamily: 'monospace'),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              // يمكن إضافة وظيفة حفظ التقرير هنا
              Navigator.pop(context);
              _showSuccessSnackBar('تم إنشاء التقرير بنجاح');
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
