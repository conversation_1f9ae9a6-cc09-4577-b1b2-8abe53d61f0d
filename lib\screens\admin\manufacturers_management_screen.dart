import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/manufacturer_provider.dart';
import '../../providers/locale_provider.dart';

class ManufacturersManagementScreen extends StatefulWidget {
  const ManufacturersManagementScreen({super.key});

  @override
  State<ManufacturersManagementScreen> createState() => _ManufacturersManagementScreenState();
}

class _ManufacturersManagementScreenState extends State<ManufacturersManagementScreen> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadManufacturers();
  }

  Future<void> _loadManufacturers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final manufacturerProvider = Provider.of<ManufacturerProvider>(context, listen: false);
      await manufacturerProvider.fetchManufacturers();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading manufacturers: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final manufacturerProvider = Provider.of<ManufacturerProvider>(context);
    final localeProvider = Provider.of<LocaleProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          localeProvider.isRTL ? 'إدارة الشركات المصنعة' : 'Manufacturers Management',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: localeProvider.isRTL ? 'تحديث' : 'Refresh',
            onPressed: _loadManufacturers,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : manufacturerProvider.manufacturers.isEmpty
              ? Center(
                  child: Text(
                    localeProvider.isRTL
                        ? 'لا توجد شركات مصنعة'
                        : 'No manufacturers found',
                    style: const TextStyle(fontSize: 18),
                  ),
                )
              : ListView.builder(
                  itemCount: manufacturerProvider.manufacturers.length,
                  itemBuilder: (context, index) {
                    final manufacturer = manufacturerProvider.manufacturers[index];
                    return Card(
                      margin: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: ListTile(
                        title: Text(manufacturer),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.edit),
                              tooltip: localeProvider.isRTL ? 'تعديل' : 'Edit',
                              onPressed: () => _showEditManufacturerDialog(context, manufacturer),
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete),
                              tooltip: localeProvider.isRTL ? 'حذف' : 'Delete',
                              onPressed: () => _showDeleteManufacturerDialog(context, manufacturer),
                            ),
                          ],
                        ),
                        onTap: () {
                          // Navigate to models for this manufacturer
                          Navigator.pushNamed(
                            context,
                            '/models-management',
                            arguments: manufacturer,
                          );
                        },
                      ),
                    );
                  },
                ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddManufacturerDialog(context),
        tooltip: localeProvider.isRTL ? 'إضافة شركة مصنعة' : 'Add Manufacturer',
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showAddManufacturerDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final manufacturerController = TextEditingController();
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final manufacturerProvider = Provider.of<ManufacturerProvider>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            localeProvider.isRTL ? 'إضافة شركة مصنعة جديدة' : 'Add New Manufacturer',
          ),
          content: Form(
            key: formKey,
            child: TextFormField(
              controller: manufacturerController,
              decoration: InputDecoration(
                labelText: localeProvider.isRTL ? 'اسم الشركة المصنعة' : 'Manufacturer Name',
                border: const OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return localeProvider.isRTL
                      ? 'الرجاء إدخال اسم الشركة المصنعة'
                      : 'Please enter a manufacturer name';
                }
                if (manufacturerProvider.manufacturers.contains(value.trim())) {
                  return localeProvider.isRTL
                      ? 'هذه الشركة المصنعة موجودة بالفعل'
                      : 'This manufacturer already exists';
                }
                return null;
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(localeProvider.isRTL ? 'إلغاء' : 'Cancel'),
            ),
            TextButton(
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  final name = manufacturerController.text.trim();
                  try {
                    await manufacturerProvider.addManufacturer(name);
                    if (context.mounted) {
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            localeProvider.isRTL
                                ? 'تمت إضافة الشركة المصنعة بنجاح'
                                : 'Manufacturer added successfully',
                          ),
                        ),
                      );
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Error: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                }
              },
              child: Text(localeProvider.isRTL ? 'إضافة' : 'Add'),
            ),
          ],
        );
      },
    );
  }

  void _showEditManufacturerDialog(BuildContext context, String manufacturer) {
    final formKey = GlobalKey<FormState>();
    final manufacturerController = TextEditingController(text: manufacturer);
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final manufacturerProvider = Provider.of<ManufacturerProvider>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            localeProvider.isRTL ? 'تعديل الشركة المصنعة' : 'Edit Manufacturer',
          ),
          content: Form(
            key: formKey,
            child: TextFormField(
              controller: manufacturerController,
              decoration: InputDecoration(
                labelText: localeProvider.isRTL ? 'اسم الشركة المصنعة' : 'Manufacturer Name',
                border: const OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return localeProvider.isRTL
                      ? 'الرجاء إدخال اسم الشركة المصنعة'
                      : 'Please enter a manufacturer name';
                }
                if (value.trim() != manufacturer &&
                    manufacturerProvider.manufacturers.contains(value.trim())) {
                  return localeProvider.isRTL
                      ? 'هذه الشركة المصنعة موجودة بالفعل'
                      : 'This manufacturer already exists';
                }
                return null;
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(localeProvider.isRTL ? 'إلغاء' : 'Cancel'),
            ),
            TextButton(
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  final newName = manufacturerController.text.trim();
                  try {
                    await manufacturerProvider.updateManufacturer(manufacturer, newName);
                    if (context.mounted) {
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            localeProvider.isRTL
                                ? 'تم تحديث الشركة المصنعة بنجاح'
                                : 'Manufacturer updated successfully',
                          ),
                        ),
                      );
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Error: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                }
              },
              child: Text(localeProvider.isRTL ? 'حفظ' : 'Save'),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteManufacturerDialog(BuildContext context, String manufacturer) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final manufacturerProvider = Provider.of<ManufacturerProvider>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            localeProvider.isRTL ? 'حذف الشركة المصنعة' : 'Delete Manufacturer',
          ),
          content: Text(
            localeProvider.isRTL
                ? 'هل أنت متأكد من حذف الشركة المصنعة "$manufacturer"؟ سيتم حذف جميع الموديلات المرتبطة بها أيضًا.'
                : 'Are you sure you want to delete the manufacturer "$manufacturer"? All associated models will also be deleted.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(localeProvider.isRTL ? 'إلغاء' : 'Cancel'),
            ),
            TextButton(
              onPressed: () async {
                try {
                  await manufacturerProvider.deleteManufacturer(manufacturer);
                  if (context.mounted) {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          localeProvider.isRTL
                              ? 'تم حذف الشركة المصنعة بنجاح'
                              : 'Manufacturer deleted successfully',
                        ),
                      ),
                    );
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Error: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: Text(localeProvider.isRTL ? 'حذف' : 'Delete'),
            ),
          ],
        );
      },
    );
  }
}
