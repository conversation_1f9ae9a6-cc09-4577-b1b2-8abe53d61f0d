# Using Your Custom Logo Files from Downloads

This guide explains how to use the logo files from `C:\Users\<USER>\Downloads\ic_launcher` in your HM Device Errors app.

## 🚀 Quick Setup (Automated)

### Option 1: Run the Batch Script
1. Double-click `scripts\copy_logo_files.bat`
2. The script will automatically copy all files to the correct locations

### Option 2: Run the PowerShell Script
1. Right-click on `scripts\copy_logo_files.ps1`
2. Select "Run with PowerShell"
3. Follow the on-screen instructions

## 📁 Manual Setup

If you prefer to copy files manually, here's the structure:

### Your Downloads Folder Structure
```
C:\Users\<USER>\Downloads\ic_launcher\
├── mipmap-mdpi\
│   └── ic_launcher.png (48x48)
├── mipmap-hdpi\
│   └── ic_launcher.png (72x72)
├── mipmap-xhdpi\
│   └── ic_launcher.png (96x96)
├── mipmap-xxhdpi\
│   └── ic_launcher.png (144x144)
├── mipmap-xxxhdpi\
│   └── ic_launcher.png (192x192)
├── drawable\
│   └── app_logo.xml
├── ios\
│   └── [iOS icon files]
├── web\
│   └── [Web icon files]
└── macos\
    └── [macOS icon files]
```

### Copy to Project Locations

#### Android Icons
Copy from `Downloads\ic_launcher\` to your project:

```
mipmap-mdpi\ic_launcher.png     → android\app\src\main\res\mipmap-mdpi\ic_launcher.png
mipmap-hdpi\ic_launcher.png     → android\app\src\main\res\mipmap-hdpi\ic_launcher.png
mipmap-xhdpi\ic_launcher.png    → android\app\src\main\res\mipmap-xhdpi\ic_launcher.png
mipmap-xxhdpi\ic_launcher.png   → android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png
mipmap-xxxhdpi\ic_launcher.png  → android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png
```

#### Drawable Files
```
drawable\app_logo.xml → android\app\src\main\res\drawable\app_logo.xml
```

#### iOS Icons
```
ios\* → ios\Runner\Assets.xcassets\AppIcon.appiconset\
```

#### Web Icons
```
web\* → web\icons\
favicon.png → web\favicon.png
```

#### macOS Icons
```
macos\* → macos\Runner\Assets.xcassets\AppIcon.appiconset\
```

## 🔧 Alternative: Direct File Replacement

If your Downloads folder has a different structure, you can:

1. **Find your logo files** in the Downloads folder
2. **Identify the sizes** (usually in the filename or folder name)
3. **Copy to the correct locations** based on size:

### Android Mipmap Sizes
- **48px** → `mipmap-mdpi`
- **72px** → `mipmap-hdpi`
- **96px** → `mipmap-xhdpi`
- **144px** → `mipmap-xxhdpi`
- **192px** → `mipmap-xxxhdpi`

### File Naming
All Android launcher icons should be named `ic_launcher.png`

## 🛠️ After Copying Files

1. **Clean the project**:
   ```bash
   flutter clean
   ```

2. **Get dependencies**:
   ```bash
   flutter pub get
   ```

3. **Build and test**:
   ```bash
   flutter build apk
   ```

## 🔍 Verify Installation

Check that files exist in these locations:
- `android\app\src\main\res\mipmap-mdpi\ic_launcher.png`
- `android\app\src\main\res\mipmap-hdpi\ic_launcher.png`
- `android\app\src\main\res\mipmap-xhdpi\ic_launcher.png`
- `android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png`
- `android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png`

## 🎯 Testing

After copying the files:

1. **Uninstall** the old app from your device
2. **Build and install** the new version
3. **Check** that the new logo appears in:
   - App launcher
   - Recent apps
   - Settings > Apps
   - Notifications

## ⚠️ Troubleshooting

### Icons Not Updating
- Clean and rebuild the project
- Uninstall and reinstall the app
- Clear device cache

### File Not Found Errors
- Check file paths are correct
- Ensure files are named exactly `ic_launcher.png`
- Verify folder structure matches requirements

### Wrong Icon Sizes
- Check that each mipmap folder has the correct size icon
- Use image editing software to resize if needed

## 📞 Need Help?

If you encounter issues:
1. Run the automated scripts first
2. Check the file structure in your Downloads folder
3. Verify file names and sizes
4. Test with a clean build

The automated scripts will handle most common scenarios and provide detailed feedback about what files were found and copied.
