#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to generate all app icons from the HM logo design.
This script creates PNG icons for Android, iOS, macOS, and Web platforms.
"""

import os
import sys
from pathlib import Path

# Try to import required libraries
try:
    from PIL import Image, ImageDraw, ImageFont
    import numpy as np
except ImportError:
    print("Required libraries not found. Please install them with:")
    print("pip install Pillow numpy")
    sys.exit(1)

def create_gradient_background(width, height):
    """Create a gradient background matching the HM logo colors."""
    # Create an array for the gradient
    gradient = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Define colors (RGB)
    start_color = np.array([233, 30, 99])    # #E91E63 (Pink)
    mid_color = np.array([156, 39, 176])     # #9C27B0 (Purple)
    end_color = np.array([33, 150, 243])     # #2196F3 (Blue)
    
    # Create diagonal gradient
    for y in range(height):
        for x in range(width):
            # Calculate position along diagonal (0 to 1)
            diagonal_pos = (x + y) / (width + height - 2)
            
            if diagonal_pos <= 0.5:
                # Interpolate between start and mid
                t = diagonal_pos * 2
                color = start_color * (1 - t) + mid_color * t
            else:
                # Interpolate between mid and end
                t = (diagonal_pos - 0.5) * 2
                color = mid_color * (1 - t) + end_color * t
            
            gradient[y, x] = color.astype(np.uint8)
    
    return Image.fromarray(gradient)

def create_hm_logo(size):
    """Create the HM logo at the specified size."""
    # Create image with transparent background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Calculate dimensions
    margin = size * 0.1
    logo_size = size - 2 * margin
    
    # Create white circle background
    circle_margin = size * 0.05
    draw.ellipse([circle_margin, circle_margin, size - circle_margin, size - circle_margin], 
                 fill=(255, 255, 255, 255))
    
    # Create gradient background for letters
    gradient_bg = create_gradient_background(int(logo_size), int(logo_size))
    
    # Create mask for letters
    letter_mask = Image.new('L', (size, size), 0)
    mask_draw = ImageDraw.Draw(letter_mask)
    
    # Calculate letter dimensions - MUCH LARGER
    letter_height = logo_size * 0.8  # Much bigger letters (80% of logo area)
    letter_width = logo_size * 0.2   # Much thicker letters
    letter_y = margin + logo_size * 0.1  # Higher position
    
    # Draw H
    h_x = margin + logo_size * 0.15
    # Left vertical line of H
    mask_draw.rectangle([h_x, letter_y, h_x + letter_width, letter_y + letter_height], fill=255)
    # Right vertical line of H
    h_right_x = h_x + logo_size * 0.25
    mask_draw.rectangle([h_right_x, letter_y, h_right_x + letter_width, letter_y + letter_height], fill=255)
    # Horizontal line of H
    h_mid_y = letter_y + letter_height * 0.4
    mask_draw.rectangle([h_x, h_mid_y, h_right_x + letter_width, h_mid_y + letter_width], fill=255)
    
    # Draw M
    m_x = margin + logo_size * 0.55
    # Left vertical line of M
    mask_draw.rectangle([m_x, letter_y, m_x + letter_width, letter_y + letter_height], fill=255)
    # Right vertical line of M
    m_right_x = m_x + logo_size * 0.25
    mask_draw.rectangle([m_right_x, letter_y, m_right_x + letter_width, letter_y + letter_height], fill=255)
    # Middle lines of M (simplified as rectangles)
    m_mid_x = m_x + logo_size * 0.125
    mask_draw.rectangle([m_mid_x, letter_y, m_mid_x + letter_width * 0.7, letter_y + letter_height * 0.6], fill=255)
    
    # Apply gradient to letters
    gradient_resized = gradient_bg.resize((size, size))
    gradient_rgba = gradient_resized.convert('RGBA')
    
    # Apply mask to gradient
    gradient_rgba.putalpha(letter_mask)
    
    # Composite the gradient letters onto the white circle
    img = Image.alpha_composite(img, gradient_rgba)
    
    return img

def generate_android_icons():
    """Generate Android launcher icons."""
    android_sizes = {
        'mipmap-mdpi': 48,
        'mipmap-hdpi': 72,
        'mipmap-xhdpi': 96,
        'mipmap-xxhdpi': 144,
        'mipmap-xxxhdpi': 192
    }
    
    base_path = Path('android/app/src/main/res')
    
    for folder, size in android_sizes.items():
        folder_path = base_path / folder
        folder_path.mkdir(parents=True, exist_ok=True)
        
        icon = create_hm_logo(size)
        icon_path = folder_path / 'ic_launcher.png'
        icon.save(icon_path, 'PNG')
        print(f"Generated {icon_path}")

def generate_ios_icons():
    """Generate iOS app icons."""
    ios_sizes = [
        (20, 2), (20, 3),  # iPhone notification
        (29, 1), (29, 2), (29, 3),  # iPhone settings
        (40, 2), (40, 3),  # iPhone spotlight
        (60, 2), (60, 3),  # iPhone app
        (20, 1), (20, 2),  # iPad notification
        (29, 1), (29, 2),  # iPad settings
        (40, 1), (40, 2),  # iPad spotlight
        (76, 1), (76, 2),  # iPad app
        (83.5, 2),  # iPad Pro
        (1024, 1)  # App Store
    ]
    
    base_path = Path('ios/Runner/Assets.xcassets/AppIcon.appiconset')
    
    for base_size, scale in ios_sizes:
        size = int(base_size * scale)
        icon = create_hm_logo(size)
        
        if scale == 1:
            filename = f'Icon-App-{base_size}x{base_size}@1x.png'
        else:
            filename = f'Icon-App-{base_size}x{base_size}@{scale}x.png'
        
        icon_path = base_path / filename
        icon.save(icon_path, 'PNG')
        print(f"Generated {icon_path}")

def generate_macos_icons():
    """Generate macOS app icons."""
    macos_sizes = [16, 32, 64, 128, 256, 512, 1024]
    
    base_path = Path('macos/Runner/Assets.xcassets/AppIcon.appiconset')
    
    for size in macos_sizes:
        icon = create_hm_logo(size)
        filename = f'app_icon_{size}.png'
        icon_path = base_path / filename
        icon.save(icon_path, 'PNG')
        print(f"Generated {icon_path}")

def generate_web_icons():
    """Generate web app icons."""
    web_sizes = [192, 512]
    
    base_path = Path('web/icons')
    base_path.mkdir(parents=True, exist_ok=True)
    
    for size in web_sizes:
        # Regular icon
        icon = create_hm_logo(size)
        icon_path = base_path / f'Icon-{size}.png'
        icon.save(icon_path, 'PNG')
        print(f"Generated {icon_path}")
        
        # Maskable icon (with more padding)
        maskable_icon = create_hm_logo(int(size * 0.8))
        maskable_bg = Image.new('RGBA', (size, size), (255, 255, 255, 255))
        offset = (size - int(size * 0.8)) // 2
        maskable_bg.paste(maskable_icon, (offset, offset), maskable_icon)
        
        maskable_path = base_path / f'Icon-maskable-{size}.png'
        maskable_bg.save(maskable_path, 'PNG')
        print(f"Generated {maskable_path}")
    
    # Generate favicon
    favicon = create_hm_logo(32)
    favicon_path = Path('web/favicon.png')
    favicon.save(favicon_path, 'PNG')
    print(f"Generated {favicon_path}")

def main():
    """Main function to generate all icons."""
    print("Generating HM Device Errors app icons...")
    
    try:
        generate_android_icons()
        generate_ios_icons()
        generate_macos_icons()
        generate_web_icons()
        
        print("\n✅ All icons generated successfully!")
        print("\nNext steps:")
        print("1. Build and test the app to verify the icons appear correctly")
        print("2. Consider using flutter_launcher_icons package for automated icon generation")
        
    except Exception as e:
        print(f"❌ Error generating icons: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
