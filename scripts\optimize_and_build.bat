@echo off
echo ========================================
echo    HM Device Errors - Complete Size Optimization
echo    Target: Reduce from 51MB to under 35MB
echo ========================================

echo [1/8] Pre-build cleanup...
flutter clean
rd /s /q build 2>nul
rd /s /q .dart_tool 2>nul

echo [2/8] Getting dependencies...
flutter pub get

echo [3/8] Running code analysis...
flutter analyze --no-fatal-infos

echo [4/8] Building ultra-optimized APK for ARM64...
flutter build apk --release ^
  --shrink ^
  --obfuscate ^
  --split-debug-info=build/debug-info ^
  --target-platform android-arm64 ^
  --tree-shake-icons ^
  --dart-define=FLUTTER_WEB_USE_SKIA=false ^
  --dart-define=DART_OBFUSCATION=true ^
  --dart-define=ENABLE_LOGGING=false ^
  --dart-define=FLUTTER_WEB_AUTO_DETECT=false ^
  --no-pub ^
  --no-track-widget-creation

echo [5/8] Building ultra-optimized APK for ARM32...
flutter build apk --release ^
  --shrink ^
  --obfuscate ^
  --split-debug-info=build/debug-info ^
  --target-platform android-arm ^
  --tree-shake-icons ^
  --dart-define=FLUTTER_WEB_USE_SKIA=false ^
  --dart-define=DART_OBFUSCATION=true ^
  --dart-define=ENABLE_LOGGING=false ^
  --dart-define=FLUTTER_WEB_AUTO_DETECT=false ^
  --no-pub ^
  --no-track-widget-creation

echo [6/8] Building App Bundle...
flutter build appbundle --release ^
  --shrink ^
  --obfuscate ^
  --split-debug-info=build/debug-info ^
  --tree-shake-icons ^
  --dart-define=DART_OBFUSCATION=true ^
  --dart-define=ENABLE_LOGGING=false ^
  --no-pub ^
  --no-track-widget-creation

echo [7/8] Analyzing final sizes...
echo.
echo ========================================
echo              SIZE ANALYSIS
echo ========================================

if exist "build\app\outputs\flutter-apk\app-arm64-v8a-release.apk" (
    for %%I in (build\app\outputs\flutter-apk\app-arm64-v8a-release.apk) do (
        set /a arm64_mb=%%~zI/1048576
        echo ARM64 APK: %%~zI bytes (~!arm64_mb! MB)
    )
) else (
    echo ARM64 APK: Not found
)

if exist "build\app\outputs\flutter-apk\app-armeabi-v7a-release.apk" (
    for %%I in (build\app\outputs\flutter-apk\app-armeabi-v7a-release.apk) do (
        set /a arm32_mb=%%~zI/1048576
        echo ARM32 APK: %%~zI bytes (~!arm32_mb! MB)
    )
) else (
    echo ARM32 APK: Not found
)

if exist "build\app\outputs\bundle\release\app-release.aab" (
    for %%I in (build\app\outputs\bundle\release\app-release.aab) do (
        set /a bundle_mb=%%~zI/1048576
        echo App Bundle: %%~zI bytes (~!bundle_mb! MB)
    )
) else (
    echo App Bundle: Not found
)

echo [8/8] Generating optimization report...
echo ======================================== > optimization_report.txt
echo    SIZE OPTIMIZATION RESULTS >> optimization_report.txt
echo ======================================== >> optimization_report.txt
echo Generated: %date% %time% >> optimization_report.txt
echo. >> optimization_report.txt

echo OPTIMIZATIONS APPLIED: >> optimization_report.txt
echo - Removed flutter_spinkit package (~500KB) >> optimization_report.txt
echo - Removed flutter_staggered_grid_view package (~300KB) >> optimization_report.txt
echo - Removed shimmer package (~150KB) >> optimization_report.txt
echo - Removed unused font files (~2MB) >> optimization_report.txt
echo - Enabled aggressive ProGuard/R8 shrinking >> optimization_report.txt
echo - Enhanced resource exclusion >> optimization_report.txt
echo - Optimized build configuration >> optimization_report.txt
echo. >> optimization_report.txt

echo FINAL APK SIZES: >> optimization_report.txt
if exist "build\app\outputs\flutter-apk\app-arm64-v8a-release.apk" (
    for %%I in (build\app\outputs\flutter-apk\app-arm64-v8a-release.apk) do (
        set /a arm64_mb=%%~zI/1048576
        echo - ARM64 APK: %%~zI bytes (~!arm64_mb! MB) >> optimization_report.txt
    )
)
if exist "build\app\outputs\flutter-apk\app-armeabi-v7a-release.apk" (
    for %%I in (build\app\outputs\flutter-apk\app-armeabi-v7a-release.apk) do (
        set /a arm32_mb=%%~zI/1048576
        echo - ARM32 APK: %%~zI bytes (~!arm32_mb! MB) >> optimization_report.txt
    )
)
if exist "build\app\outputs\bundle\release\app-release.aab" (
    for %%I in (build\app\outputs\bundle\release\app-release.aab) do (
        set /a bundle_mb=%%~zI/1048576
        echo - App Bundle: %%~zI bytes (~!bundle_mb! MB) >> optimization_report.txt
    )
)

echo.
echo ========================================
echo           OPTIMIZATION COMPLETE!
echo ========================================
echo.
echo Files generated:
echo - ARM64 APK: build\app\outputs\flutter-apk\app-arm64-v8a-release.apk
echo - ARM32 APK: build\app\outputs\flutter-apk\app-armeabi-v7a-release.apk
echo - App Bundle: build\app\outputs\bundle\release\app-release.aab
echo - Report: optimization_report.txt
echo.
echo Estimated size reduction: 3-5MB
echo Original size: ~51MB
echo Optimized size: ~46-48MB (per architecture)
echo ========================================

pause
