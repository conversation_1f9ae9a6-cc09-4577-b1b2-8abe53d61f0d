import 'package:flutter/foundation.dart';
import '../models/attachment_model.dart';
import '../services/attachment_cache_service.dart';

/// Provider لإدارة المرفقات والـ cache
class AttachmentProvider with ChangeNotifier {
  final AttachmentCacheService _cacheService = AttachmentCacheService.instance;

  final Map<String, AttachmentModel> _attachments = {};
  final Map<String, bool> _downloadingAttachments = {};
  final Map<String, double> _downloadProgress = {};
  CacheInfo? _cacheInfo;
  bool _isInitialized = false;

  Map<String, AttachmentModel> get attachments => _attachments;
  Map<String, bool> get downloadingAttachments => _downloadingAttachments;
  Map<String, double> get downloadProgress => _downloadProgress;
  CacheInfo? get cacheInfo => _cacheInfo;
  bool get isInitialized => _isInitialized;

  /// تهيئة الـ provider
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _cacheService.initialize();
      await _updateCacheInfo();
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing AttachmentProvider: $e');
    }
  }

  /// تحديث معلومات الـ cache
  Future<void> _updateCacheInfo() async {
    try {
      _cacheInfo = await _cacheService.getCacheInfo();
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating cache info: $e');
    }
  }

  /// تحميل مرفق وحفظه في الـ cache
  Future<bool> cacheAttachment(AttachmentModel attachment) async {
    if (!_isInitialized) await initialize();

    final attachmentId = attachment.id;

    // تحقق من أن الملف ليس قيد التحميل بالفعل
    if (_downloadingAttachments[attachmentId] == true) {
      return false;
    }

    try {
      _downloadingAttachments[attachmentId] = true;
      _downloadProgress[attachmentId] = 0.0;
      notifyListeners();

      // محاكاة progress
      _updateDownloadProgress(attachmentId, 0.1);

      final cachedFile = await _cacheService.cacheFile(attachment);

      _updateDownloadProgress(attachmentId, 1.0);

      if (cachedFile != null) {
        _attachments[attachmentId] = attachment;
        await _updateCacheInfo();

        _downloadingAttachments[attachmentId] = false;
        _downloadProgress.remove(attachmentId);
        notifyListeners();

        return true;
      } else {
        throw Exception('فشل في تحميل الملف');
      }
    } catch (e) {
      _downloadingAttachments[attachmentId] = false;
      _downloadProgress.remove(attachmentId);
      notifyListeners();

      debugPrint('Error caching attachment: $e');
      return false;
    }
  }

  /// تحديث progress التحميل
  void _updateDownloadProgress(String attachmentId, double progress) {
    _downloadProgress[attachmentId] = progress;
    notifyListeners();
  }

  /// تحميل عدة مرفقات
  Future<void> cacheMultipleAttachments(List<AttachmentModel> attachments) async {
    for (final attachment in attachments) {
      await cacheAttachment(attachment);
    }
  }

  /// التحقق من وجود مرفق في الـ cache
  Future<bool> isAttachmentCached(AttachmentModel attachment) async {
    if (!_isInitialized) await initialize();

    final cachedFile = await _cacheService.getCachedFile(attachment);
    return cachedFile != null;
  }

  /// الحصول على حجم الـ cache المستخدم
  String get formattedCacheSize {
    return _cacheInfo?.formattedTotalSize ?? '0 B';
  }

  /// الحصول على نسبة استخدام الـ cache
  double get cacheUsagePercentage {
    return _cacheInfo?.usagePercentage ?? 0.0;
  }

  /// مسح جميع الـ cache
  Future<void> clearAllCache() async {
    if (!_isInitialized) await initialize();

    try {
      await _cacheService.clearAllCache();
      _attachments.clear();
      await _updateCacheInfo();
      notifyListeners();
    } catch (e) {
      debugPrint('Error clearing cache: $e');
    }
  }

  /// مسح الـ cache القديم
  Future<void> cleanupOldCache() async {
    if (!_isInitialized) await initialize();

    try {
      // هذه الوظيفة تتم تلقائياً في CacheService
      // لكن يمكن استدعاؤها يدوياً
      await _updateCacheInfo();
      notifyListeners();
    } catch (e) {
      debugPrint('Error cleaning up cache: $e');
    }
  }

  /// الحصول على معلومات تفصيلية عن الـ cache
  Future<Map<String, dynamic>> getCacheStatistics() async {
    if (!_isInitialized) await initialize();

    final info = await _cacheService.getCacheInfo();

    return {
      'totalSize': info.totalSize,
      'formattedTotalSize': info.formattedTotalSize,
      'fileCount': info.fileCount,
      'maxSize': info.maxSize,
      'formattedMaxSize': info.formattedMaxSize,
      'usagePercentage': info.usagePercentage,
      'availableSpace': info.maxSize - info.totalSize,
      'formattedAvailableSpace': _formatBytes(info.maxSize - info.totalSize),
    };
  }

  /// تنسيق البايتات
  String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  /// تحديد أولوية التحميل للمرفقات
  Future<void> preloadAttachments(List<AttachmentModel> attachments) async {
    if (!_isInitialized) await initialize();

    // ترتيب المرفقات حسب الأولوية (الصور أولاً، ثم الملفات الصغيرة)
    final sortedAttachments = List<AttachmentModel>.from(attachments);
    sortedAttachments.sort((a, b) {
      // الصور لها أولوية أعلى
      if (a.isImage && !b.isImage) return -1;
      if (!a.isImage && b.isImage) return 1;

      // الملفات الأصغر لها أولوية أعلى
      return a.fileSize.compareTo(b.fileSize);
    });

    // تحميل المرفقات بالتوازي (محدود)
    const maxConcurrent = 3;
    for (int i = 0; i < sortedAttachments.length; i += maxConcurrent) {
      final batch = sortedAttachments.skip(i).take(maxConcurrent);
      await Future.wait(
        batch.map((attachment) => cacheAttachment(attachment)),
      );
    }
  }

  /// التحقق من حالة التحميل لمرفق معين
  bool isAttachmentDownloading(String attachmentId) {
    return _downloadingAttachments[attachmentId] ?? false;
  }

  /// الحصول على progress التحميل لمرفق معين
  double getDownloadProgress(String attachmentId) {
    return _downloadProgress[attachmentId] ?? 0.0;
  }

  /// إلغاء تحميل مرفق
  void cancelDownload(String attachmentId) {
    _downloadingAttachments[attachmentId] = false;
    _downloadProgress.remove(attachmentId);
    notifyListeners();
  }

  /// تحديث قائمة المرفقات
  void updateAttachments(List<AttachmentModel> attachments) {
    for (final attachment in attachments) {
      _attachments[attachment.id] = attachment;
    }
    notifyListeners();
  }

  /// إزالة مرفق من القائمة
  void removeAttachment(String attachmentId) {
    _attachments.remove(attachmentId);
    _downloadingAttachments.remove(attachmentId);
    _downloadProgress.remove(attachmentId);
    notifyListeners();
  }

  /// الحصول على مرفق بالمعرف
  AttachmentModel? getAttachment(String attachmentId) {
    return _attachments[attachmentId];
  }

  /// الحصول على جميع المرفقات من نوع معين
  List<AttachmentModel> getAttachmentsByType(AttachmentType type) {
    return _attachments.values
        .where((attachment) => attachment.type == type)
        .toList();
  }

  /// الحصول على المرفقات المحملة
  List<AttachmentModel> get cachedAttachments {
    return _attachments.values.toList();
  }

  /// الحصول على عدد المرفقات قيد التحميل
  int get downloadingCount {
    return _downloadingAttachments.values.where((isDownloading) => isDownloading).length;
  }

  @override
  void dispose() {
    _attachments.clear();
    _downloadingAttachments.clear();
    _downloadProgress.clear();
    super.dispose();
  }
}
