import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocaleProvider with ChangeNotifier {
  Locale _locale = const Locale('ar', '');
  bool _isRTL = true;

  Locale get locale => _locale;
  bool get isRTL => _isRTL;

  LocaleProvider() {
    _loadLocalePreference();
  }

  Future<void> _loadLocalePreference() async {
    final prefs = await SharedPreferences.getInstance();
    final languageCode = prefs.getString('languageCode') ?? 'ar';
    _locale = Locale(languageCode, '');
    _isRTL = languageCode == 'ar';
    notifyListeners();
  }

  Future<void> setLocale(Locale locale) async {
    if (!['ar', 'en'].contains(locale.languageCode)) return;

    _locale = locale;
    _isRTL = locale.languageCode == 'ar';
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('languageCode', locale.languageCode);
    
    notifyListeners();
  }

  Future<void> toggleLocale() async {
    final newLocale = _locale.languageCode == 'ar' 
        ? const Locale('en', '') 
        : const Locale('ar', '');
    await setLocale(newLocale);
  }
}
