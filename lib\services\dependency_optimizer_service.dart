import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة تحسين وتحليل التبعيات
class DependencyOptimizerService {
  static DependencyOptimizerService? _instance;
  static DependencyOptimizerService get instance => _instance ??= DependencyOptimizerService._();

  DependencyOptimizerService._();

  static const String _dependencyStatsKey = 'dependency_stats';
  static const String _lastAnalysisKey = 'last_dependency_analysis';

  /// تحليل التبعيات المستخدمة
  Future<DependencyAnalysis> analyzeDependencies() async {
    debugPrint('Starting dependency analysis...');

    final analysis = DependencyAnalysis();

    try {
      // تحليل التبعيات الأساسية
      analysis.coreDependencies = _analyzeCorePackages();

      // تحليل تبعيات Firebase
      analysis.firebaseDependencies = _analyzeFirebasePackages();

      // تحليل تبعيات UI
      analysis.uiDependencies = _analyzeUIPackages();

      // تحليل التبعيات الأخرى
      analysis.otherDependencies = _analyzeOtherPackages();

      // حساب الأحجام المقدرة
      analysis.calculateEstimatedSizes();

      // إنشاء اقتراحات التحسين
      analysis.optimizationSuggestions = _generateDependencyOptimizations(analysis);

      // حفظ نتائج التحليل
      await _saveDependencyAnalysis(analysis);

      debugPrint('Dependency analysis completed');

    } catch (e) {
      debugPrint('Error analyzing dependencies: $e');
    }

    return analysis;
  }

  /// تحليل الحزم الأساسية
  List<DependencyInfo> _analyzeCorePackages() {
    return [
      DependencyInfo(
        name: 'flutter',
        category: 'Core',
        estimatedSize: 15 * 1024 * 1024, // 15MB
        isEssential: true,
        description: 'Flutter framework core',
      ),
      DependencyInfo(
        name: 'flutter_localizations',
        category: 'Core',
        estimatedSize: 2 * 1024 * 1024, // 2MB
        isEssential: true,
        description: 'Localization support',
      ),
      DependencyInfo(
        name: 'cupertino_icons',
        category: 'Core',
        estimatedSize: 1 * 1024 * 1024, // 1MB
        isEssential: false,
        description: 'iOS style icons',
        optimizationNote: 'يمكن استبدالها بأيقونات مخصصة أصغر',
      ),
    ];
  }

  /// تحليل حزم Firebase
  List<DependencyInfo> _analyzeFirebasePackages() {
    return [
      DependencyInfo(
        name: 'firebase_core',
        category: 'Firebase',
        estimatedSize: 3 * 1024 * 1024, // 3MB
        isEssential: true,
        description: 'Firebase core functionality',
      ),
      DependencyInfo(
        name: 'firebase_auth',
        category: 'Firebase',
        estimatedSize: 4 * 1024 * 1024, // 4MB
        isEssential: true,
        description: 'Authentication services',
      ),
      DependencyInfo(
        name: 'cloud_firestore',
        category: 'Firebase',
        estimatedSize: 6 * 1024 * 1024, // 6MB
        isEssential: true,
        description: 'Cloud database',
      ),
      DependencyInfo(
        name: 'firebase_storage',
        category: 'Firebase',
        estimatedSize: 3 * 1024 * 1024, // 3MB
        isEssential: true,
        description: 'Cloud storage',
      ),
      DependencyInfo(
        name: 'firebase_messaging',
        category: 'Firebase',
        estimatedSize: 2 * 1024 * 1024, // 2MB
        isEssential: false,
        description: 'Push notifications',
        optimizationNote: 'يمكن إزالتها إذا لم تكن الإشعارات مطلوبة',
      ),
      DependencyInfo(
        name: 'firebase_remote_config',
        category: 'Firebase',
        estimatedSize: 1 * 1024 * 1024, // 1MB
        isEssential: false,
        description: 'Remote configuration',
        optimizationNote: 'يمكن استبدالها بحل محلي',
      ),
    ];
  }

  /// تحليل حزم UI
  List<DependencyInfo> _analyzeUIPackages() {
    return [
      DependencyInfo(
        name: 'provider',
        category: 'UI',
        estimatedSize: 500 * 1024, // 500KB
        isEssential: true,
        description: 'State management',
      ),
      DependencyInfo(
        name: 'cached_network_image',
        category: 'UI',
        estimatedSize: 2 * 1024 * 1024, // 2MB
        isEssential: false,
        description: 'Network image caching',
        optimizationNote: 'يمكن استبدالها بحل مخصص أصغر',
      ),
      DependencyInfo(
        name: 'flutter_svg',
        category: 'UI',
        estimatedSize: 1 * 1024 * 1024, // 1MB
        isEssential: false,
        description: 'SVG image support',
        optimizationNote: 'يمكن تحويل SVG إلى PNG لتوفير المساحة',
      ),
      DependencyInfo(
        name: 'image_picker',
        category: 'UI',
        estimatedSize: 3 * 1024 * 1024, // 3MB
        isEssential: true,
        description: 'Image selection',
      ),
      DependencyInfo(
        name: 'file_picker',
        category: 'UI',
        estimatedSize: 2 * 1024 * 1024, // 2MB
        isEssential: true,
        description: 'File selection',
      ),
      DependencyInfo(
        name: 'flutter_staggered_grid_view',
        category: 'UI',
        estimatedSize: 300 * 1024, // 300KB
        isEssential: false,
        description: 'Advanced grid layouts',
        optimizationNote: 'يمكن استبدالها بـ GridView العادي',
      ),
      DependencyInfo(
        name: 'flutter_spinkit',
        category: 'UI',
        estimatedSize: 200 * 1024, // 200KB
        isEssential: false,
        description: 'Loading animations',
        optimizationNote: 'يمكن استبدالها بـ CircularProgressIndicator',
      ),
      DependencyInfo(
        name: 'shimmer',
        category: 'UI',
        estimatedSize: 150 * 1024, // 150KB
        isEssential: false,
        description: 'Shimmer loading effect',
        optimizationNote: 'يمكن إنشاء تأثير مشابه محلياً',
      ),
    ];
  }

  /// تحليل الحزم الأخرى
  List<DependencyInfo> _analyzeOtherPackages() {
    return [
      DependencyInfo(
        name: 'googleapis',
        category: 'Network',
        estimatedSize: 8 * 1024 * 1024, // 8MB
        isEssential: true,
        description: 'Google APIs client',
      ),
      DependencyInfo(
        name: 'googleapis_auth',
        category: 'Network',
        estimatedSize: 2 * 1024 * 1024, // 2MB
        isEssential: true,
        description: 'Google APIs authentication',
      ),
      DependencyInfo(
        name: 'http',
        category: 'Network',
        estimatedSize: 500 * 1024, // 500KB
        isEssential: true,
        description: 'HTTP client',
      ),
      DependencyInfo(
        name: 'shared_preferences',
        category: 'Storage',
        estimatedSize: 300 * 1024, // 300KB
        isEssential: true,
        description: 'Local preferences storage',
      ),
      DependencyInfo(
        name: 'path_provider',
        category: 'Storage',
        estimatedSize: 400 * 1024, // 400KB
        isEssential: true,
        description: 'File system paths',
      ),
      DependencyInfo(
        name: 'excel',
        category: 'Utility',
        estimatedSize: 1 * 1024 * 1024, // 1MB
        isEssential: false,
        description: 'Excel file handling',
        optimizationNote: 'يمكن إزالتها إذا لم تكن مطلوبة',
      ),
      DependencyInfo(
        name: 'encrypt',
        category: 'Security',
        estimatedSize: 800 * 1024, // 800KB
        isEssential: true,
        description: 'Encryption utilities',
      ),
      DependencyInfo(
        name: 'crypto',
        category: 'Security',
        estimatedSize: 600 * 1024, // 600KB
        isEssential: true,
        description: 'Cryptographic functions',
      ),
      DependencyInfo(
        name: 'flutter_image_compress',
        category: 'Image',
        estimatedSize: 2 * 1024 * 1024, // 2MB
        isEssential: true,
        description: 'Image compression',
      ),
      DependencyInfo(
        name: 'image',
        category: 'Image',
        estimatedSize: 3 * 1024 * 1024, // 3MB
        isEssential: true,
        description: 'Image processing',
      ),
    ];
  }

  /// إنشاء اقتراحات تحسين التبعيات
  List<DependencyOptimization> _generateDependencyOptimizations(DependencyAnalysis analysis) {
    final optimizations = <DependencyOptimization>[];

    // البحث عن التبعيات غير الأساسية
    final allDependencies = [
      ...analysis.coreDependencies,
      ...analysis.firebaseDependencies,
      ...analysis.uiDependencies,
      ...analysis.otherDependencies,
    ];

    for (final dep in allDependencies) {
      if (!dep.isEssential && dep.optimizationNote != null) {
        optimizations.add(DependencyOptimization(
          dependencyName: dep.name,
          currentSize: dep.estimatedSize,
          optimizationType: 'Remove or Replace',
          description: dep.optimizationNote!,
          estimatedSaving: dep.estimatedSize,
          priority: _calculateOptimizationPriority(dep),
        ));
      }
    }

    // اقتراحات عامة
    optimizations.addAll([
      DependencyOptimization(
        dependencyName: 'Firebase Bundle',
        currentSize: analysis.firebaseDependencies.fold(0, (sum, dep) => sum + dep.estimatedSize),
        optimizationType: 'Optimize Bundle',
        description: 'استخدام Firebase BoM لتحسين إدارة الإصدارات',
        estimatedSaving: 2 * 1024 * 1024, // 2MB
        priority: OptimizationPriority.medium,
      ),
      DependencyOptimization(
        dependencyName: 'UI Libraries',
        currentSize: analysis.uiDependencies.fold(0, (sum, dep) => sum + dep.estimatedSize),
        optimizationType: 'Consolidate',
        description: 'دمج مكتبات UI المتشابهة أو استبدالها بحلول مخصصة',
        estimatedSaving: 3 * 1024 * 1024, // 3MB
        priority: OptimizationPriority.low,
      ),
    ]);

    return optimizations;
  }

  /// حساب أولوية التحسين
  OptimizationPriority _calculateOptimizationPriority(DependencyInfo dep) {
    if (dep.estimatedSize > 2 * 1024 * 1024) { // أكبر من 2MB
      return OptimizationPriority.high;
    } else if (dep.estimatedSize > 500 * 1024) { // أكبر من 500KB
      return OptimizationPriority.medium;
    } else {
      return OptimizationPriority.low;
    }
  }

  /// حفظ تحليل التبعيات
  Future<void> _saveDependencyAnalysis(DependencyAnalysis analysis) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final analysisData = {
        'timestamp': DateTime.now().toIso8601String(),
        'totalDependencies': analysis.totalDependencies,
        'totalEstimatedSize': analysis.totalEstimatedSize,
        'optimizationCount': analysis.optimizationSuggestions.length,
        'potentialSaving': analysis.potentialSaving,
      };

      await prefs.setString(_dependencyStatsKey, jsonEncode(analysisData));
      await prefs.setString(_lastAnalysisKey, DateTime.now().toIso8601String());
    } catch (e) {
      debugPrint('Error saving dependency analysis: $e');
    }
  }

  /// الحصول على آخر تحليل للتبعيات
  Future<Map<String, dynamic>?> getLastAnalysisStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsStr = prefs.getString(_dependencyStatsKey);

      if (statsStr != null) {
        return jsonDecode(statsStr) as Map<String, dynamic>;
      }
    } catch (e) {
      debugPrint('Error getting last analysis stats: $e');
    }

    return null;
  }

  /// التحقق من الحاجة لتحليل جديد
  Future<bool> needsNewAnalysis() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastAnalysisStr = prefs.getString(_lastAnalysisKey);

      if (lastAnalysisStr == null) return true;

      final lastAnalysis = DateTime.parse(lastAnalysisStr);
      final daysSinceAnalysis = DateTime.now().difference(lastAnalysis).inDays;

      return daysSinceAnalysis >= 7; // تحليل جديد كل أسبوع
    } catch (e) {
      debugPrint('Error checking analysis need: $e');
      return true;
    }
  }
}

/// معلومات التبعية
class DependencyInfo {
  final String name;
  final String category;
  final int estimatedSize;
  final bool isEssential;
  final String description;
  final String? optimizationNote;

  DependencyInfo({
    required this.name,
    required this.category,
    required this.estimatedSize,
    required this.isEssential,
    required this.description,
    this.optimizationNote,
  });

  String get formattedSize {
    if (estimatedSize < 1024) return '$estimatedSize B';
    if (estimatedSize < 1024 * 1024) return '${(estimatedSize / 1024).toStringAsFixed(1)} KB';
    return '${(estimatedSize / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}

/// تحليل التبعيات
class DependencyAnalysis {
  List<DependencyInfo> coreDependencies = [];
  List<DependencyInfo> firebaseDependencies = [];
  List<DependencyInfo> uiDependencies = [];
  List<DependencyInfo> otherDependencies = [];
  List<DependencyOptimization> optimizationSuggestions = [];

  int get totalDependencies =>
    coreDependencies.length +
    firebaseDependencies.length +
    uiDependencies.length +
    otherDependencies.length;

  int get totalEstimatedSize =>
    coreDependencies.fold<int>(0, (sum, dep) => sum + dep.estimatedSize) +
    firebaseDependencies.fold<int>(0, (sum, dep) => sum + dep.estimatedSize) +
    uiDependencies.fold<int>(0, (sum, dep) => sum + dep.estimatedSize) +
    otherDependencies.fold<int>(0, (sum, dep) => sum + dep.estimatedSize);

  int get potentialSaving =>
    optimizationSuggestions.fold<int>(0, (sum, opt) => sum + opt.estimatedSaving);

  void calculateEstimatedSizes() {
    // يتم حساب الأحجام تلقائياً من خلال الخصائص أعلاه
  }

  String get formattedTotalSize {
    if (totalEstimatedSize < 1024 * 1024) {
      return '${(totalEstimatedSize / 1024).toStringAsFixed(1)} KB';
    }
    return '${(totalEstimatedSize / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  String get formattedPotentialSaving {
    if (potentialSaving < 1024 * 1024) {
      return '${(potentialSaving / 1024).toStringAsFixed(1)} KB';
    }
    return '${(potentialSaving / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}

/// تحسين التبعية
class DependencyOptimization {
  final String dependencyName;
  final int currentSize;
  final String optimizationType;
  final String description;
  final int estimatedSaving;
  final OptimizationPriority priority;

  DependencyOptimization({
    required this.dependencyName,
    required this.currentSize,
    required this.optimizationType,
    required this.description,
    required this.estimatedSaving,
    required this.priority,
  });

  String get formattedSaving {
    if (estimatedSaving < 1024 * 1024) {
      return '${(estimatedSaving / 1024).toStringAsFixed(1)} KB';
    }
    return '${(estimatedSaving / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}

/// أولوية التحسين
enum OptimizationPriority {
  high,
  medium,
  low,
}
