# إعدادات الكاش المتقدمة - المرحلة الثانية

## نظرة عامة

تم تطوير شاشة إعدادات كاش متقدمة جديدة تتضمن ثلاث تبويبات رئيسية لإدارة شاملة للكاش والمرفقات.

## الميزات الجديدة

### 1. واجهة مستخدم محسنة مع تبويبات

#### تبويب الإحصائيات
- **إحصائيات أساسية**: عدد الملفات، الحجم الإجمالي، نسبة الاستخدام
- **إحصائيات مفصلة**: تفاصيل thumbnails الصور والفيديوهات، عمر أقدم ملف
- **مخطط الاستخدام**: عرض بصري لاستخدام المساحة مع ألوان تحذيرية

#### تبويب الإعدادات
- **إعدادات الحجم**: تحكم في الحد الأقصى للكاش والتنظيف التلقائي
- **إعدادات الاحتفاظ**: مدة الاحتفاظ بالملفات
- **إعدادات الصور المصغرة**: تحكم في thumbnails الصور والفيديوهات
- **إعدادات الشبكة**: التحميل على WiFi فقط، ضغط الصور التلقائي

#### تبويب الأدوات
- **إجراءات سريعة**: مسح الكاش، تحسين الكاش
- **الصيانة التلقائية**: جدولة التنظيف مع تحكم في الفترة الزمنية
- **أدوات متقدمة**: تصدير التقارير، فتح مجلد الكاش

### 2. إعدادات جديدة

#### إعدادات الشبكة
```dart
bool wifiOnlyDownload;        // التحميل على WiFi فقط
bool autoCompressImages;      // ضغط الصور تلقائياً
```

#### إعدادات الصيانة
```dart
bool enableScheduledCleanup;  // تفعيل التنظيف المجدول
int cleanupIntervalDays;      // فترة التنظيف (1-7 أيام)
```

### 3. تحسينات الأداء

#### تحديث تلقائي للإحصائيات
- تحديث الإحصائيات كل 30 ثانية
- زر تحديث يدوي في شريط التطبيق

#### تحسين الكاش
- تنظيف thumbnails القديمة
- إزالة الملفات المكررة (قادم)
- ضغط الصور الكبيرة (قادم)

## الاستخدام

### فتح شاشة الإعدادات
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const CacheSettingsScreen(),
  ),
);
```

### الوصول للإعدادات الجديدة
```dart
final settings = CacheSettingsService.instance;

// إعدادات الشبكة
bool wifiOnly = settings.isWifiOnlyDownloadEnabled;
bool autoCompress = settings.isAutoCompressImagesEnabled;

// إعدادات الصيانة
bool scheduledCleanup = settings.isScheduledCleanupEnabled;
int cleanupInterval = settings.cleanupIntervalDays;
```

## الملفات المحدثة

### الملفات الرئيسية
- `lib/screens/settings/cache_settings_screen.dart` - الشاشة الرئيسية
- `lib/models/cache_settings_model.dart` - نموذج الإعدادات المحدث

### الميزات المضافة
1. **TabController** لإدارة التبويبات
2. **Timer** للتحديث التلقائي
3. **إحصائيات مفصلة** مع مخططات بصرية
4. **إعدادات شبكة متقدمة**
5. **أدوات صيانة ذكية**

## التحسينات المستقبلية

### المرحلة القادمة
- [ ] تنفيذ تصدير التقارير
- [ ] فتح مجلد الكاش في مدير الملفات
- [ ] إحصائيات أكثر تفصيلاً للـ thumbnails
- [ ] تحسين خوارزمية ضغط الصور
- [ ] إشعارات للصيانة التلقائية

### تحسينات الأداء
- [ ] كاش للإحصائيات
- [ ] تحميل تدريجي للبيانات الكبيرة
- [ ] تحسين استعلامات قاعدة البيانات

## الاختبار

### اختبار الواجهة
```dart
testWidgets('Cache settings tabs work correctly', (tester) async {
  await tester.pumpWidget(MyApp());
  
  // فتح شاشة الإعدادات
  await tester.tap(find.text('إعدادات الكاش'));
  await tester.pumpAndSettle();
  
  // اختبار التبويبات
  expect(find.text('الإحصائيات'), findsOneWidget);
  expect(find.text('الإعدادات'), findsOneWidget);
  expect(find.text('الأدوات'), findsOneWidget);
  
  // اختبار التنقل بين التبويبات
  await tester.tap(find.text('الإعدادات'));
  await tester.pumpAndSettle();
  
  expect(find.text('إعدادات الحجم'), findsOneWidget);
});
```

### اختبار الإعدادات
```dart
test('New cache settings work correctly', () async {
  final settings = CacheSettingsModel(
    wifiOnlyDownload: true,
    autoCompressImages: false,
    enableScheduledCleanup: true,
    cleanupIntervalDays: 5,
  );
  
  expect(settings.wifiOnlyDownload, true);
  expect(settings.cleanupIntervalDays, 5);
});
```

## الأمان والخصوصية

### حماية البيانات
- جميع الإعدادات محفوظة محلياً
- لا يتم إرسال بيانات الكاش للخادم
- تشفير أسماء الملفات في الكاش

### إدارة الأذونات
- طلب إذن الوصول للتخزين عند الحاجة
- احترام إعدادات الخصوصية للمستخدم

## الدعم والمساعدة

### المشاكل الشائعة
1. **بطء في التحديث**: تأكد من تفعيل التحديث التلقائي
2. **عدم حفظ الإعدادات**: تحقق من أذونات التطبيق
3. **استهلاك مساحة كبيرة**: استخدم أدوات التحسين

### التواصل
- للمشاكل التقنية: راجع ملف الأخطاء
- للاقتراحات: أضف issue في المشروع
