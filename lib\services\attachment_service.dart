import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;
import '../models/attachment_model.dart';
import 'storage_service.dart';
import 'video_compression_service.dart';

/// Service for handling file attachments (images, videos, documents)
class AttachmentService {
  static final StorageService _storageService = StorageService();
  static final ImagePicker _imagePicker = ImagePicker();

  /// Pick images from gallery or camera
  static Future<List<AttachmentModel>> pickImages({
    bool allowMultiple = true,
    ImageSource source = ImageSource.gallery,
  }) async {
    try {
      final List<AttachmentModel> attachments = [];

      if (allowMultiple && source == ImageSource.gallery) {
        // Use pickMultipleMedia for multiple images
        final List<XFile> images = await _imagePicker.pickMultipleMedia(
          maxWidth: 1920,
          maxHeight: 1080,
          imageQuality: 85,
        );

        for (final image in images) {
          final attachment = await _createAttachmentFromXFile(
            image,
            AttachmentType.image,
          );
          if (attachment != null) {
            attachments.add(attachment);
          }
        }
      } else {
        final XFile? image = await _imagePicker.pickImage(
          source: source,
          maxWidth: 1920,
          maxHeight: 1080,
          imageQuality: 85,
        );

        if (image != null) {
          final attachment = await _createAttachmentFromXFile(
            image,
            AttachmentType.image,
          );
          if (attachment != null) {
            attachments.add(attachment);
          }
        }
      }

      return attachments;
    } catch (e) {
      debugPrint('Error picking images: $e');
      throw Exception('Failed to pick images: $e');
    }
  }

  /// Pick videos from gallery or camera
  static Future<List<AttachmentModel>> pickVideos({
    bool allowMultiple = false,
    ImageSource source = ImageSource.gallery,
  }) async {
    try {
      final List<AttachmentModel> attachments = [];

      final XFile? video = await _imagePicker.pickVideo(
        source: source,
        maxDuration: const Duration(minutes: 5), // 5 minute limit
      );

      if (video != null) {
        final attachment = await _createAttachmentFromXFile(
          video,
          AttachmentType.video,
        );
        if (attachment != null) {
          attachments.add(attachment);
        }
      }

      return attachments;
    } catch (e) {
      debugPrint('Error picking videos: $e');
      throw Exception('Failed to pick videos: $e');
    }
  }

  /// Pick documents using file picker
  static Future<List<AttachmentModel>> pickDocuments({
    bool allowMultiple = true,
  }) async {
    try {
      final List<AttachmentModel> attachments = [];

      final FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: AttachmentModel.getSupportedExtensions(AttachmentType.document),
        allowMultiple: allowMultiple,
        withData: kIsWeb, // Load data for web
      );

      if (result != null) {
        for (final file in result.files) {
          final attachment = await _createAttachmentFromPlatformFile(
            file,
            AttachmentType.document,
          );
          if (attachment != null) {
            attachments.add(attachment);
          }
        }
      }

      return attachments;
    } catch (e) {
      debugPrint('Error picking documents: $e');
      throw Exception('Failed to pick documents: $e');
    }
  }

  /// Create attachment from XFile (images/videos)
  static Future<AttachmentModel?> _createAttachmentFromXFile(
    XFile file,
    AttachmentType type,
  ) async {
    try {
      final String fileName = path.basename(file.path);
      final String extension = path.extension(fileName).toLowerCase();
      final int fileSize = await file.length();

      // Validate file size based on type
      int maxSize;
      String sizeLabel;

      switch (type) {
        case AttachmentType.video:
          maxSize = 50 * 1024 * 1024; // 50MB for videos
          sizeLabel = '50MB';
          break;
        case AttachmentType.image:
          maxSize = 10 * 1024 * 1024; // 10MB for images
          sizeLabel = '10MB';
          break;
        case AttachmentType.document:
          maxSize = 25 * 1024 * 1024; // 25MB for documents
          sizeLabel = '25MB';
          break;
      }

      if (fileSize > maxSize) {
        throw Exception('File size too large. Maximum $sizeLabel allowed for ${type.toString().split('.').last}s.');
      }

      // Get file data
      Uint8List fileData;
      if (kIsWeb) {
        fileData = await file.readAsBytes();
      } else {
        fileData = await File(file.path).readAsBytes();
      }

      // Compress video if needed
      if (type == AttachmentType.video) {
        fileData = await VideoCompressionService.compressVideo(fileData, fileName);
      }

      // Generate unique file name
      final String uniqueFileName = _generateUniqueFileName(fileName);

      // Determine MIME type
      final String mimeType = _getMimeTypeFromExtension(extension);

      return AttachmentModel(
        id: '', // Will be set when uploaded
        fileName: uniqueFileName,
        originalFileName: fileName,
        type: type,
        url: '', // Will be set when uploaded
        fileSize: fileData.length,
        mimeType: mimeType,
        uploadedAt: DateTime.now(),
        uploadedBy: '', // Will be set when uploaded
        metadata: type == AttachmentType.video ? {'originalSize': fileSize} : null,
      );
    } catch (e) {
      debugPrint('Error creating attachment from XFile: $e');
      return null;
    }
  }

  /// Create attachment from PlatformFile (documents)
  static Future<AttachmentModel?> _createAttachmentFromPlatformFile(
    PlatformFile file,
    AttachmentType type,
  ) async {
    try {
      final String fileName = file.name;
      final String extension = path.extension(fileName).toLowerCase();
      final int fileSize = file.size;

      // Validate file size based on type
      int maxSize;
      String sizeLabel;

      switch (type) {
        case AttachmentType.video:
          maxSize = 50 * 1024 * 1024; // 50MB for videos
          sizeLabel = '50MB';
          break;
        case AttachmentType.image:
          maxSize = 10 * 1024 * 1024; // 10MB for images
          sizeLabel = '10MB';
          break;
        case AttachmentType.document:
          maxSize = 25 * 1024 * 1024; // 25MB for documents
          sizeLabel = '25MB';
          break;
      }

      if (fileSize > maxSize) {
        throw Exception('File size too large. Maximum $sizeLabel allowed for ${type.toString().split('.').last}s.');
      }

      // Get file data
      Uint8List? fileData = file.bytes;
      if (fileData == null && file.path != null) {
        fileData = await File(file.path!).readAsBytes();
      }

      if (fileData == null) {
        throw Exception('Could not read file data');
      }

      // Generate unique file name
      final String uniqueFileName = _generateUniqueFileName(fileName);

      // Determine MIME type
      final String mimeType = _getMimeTypeFromExtension(extension);

      return AttachmentModel(
        id: '', // Will be set when uploaded
        fileName: uniqueFileName,
        originalFileName: fileName,
        type: type,
        url: '', // Will be set when uploaded
        fileSize: fileData.length,
        mimeType: mimeType,
        uploadedAt: DateTime.now(),
        uploadedBy: '', // Will be set when uploaded
      );
    } catch (e) {
      debugPrint('Error creating attachment from PlatformFile: $e');
      return null;
    }
  }

  /// Upload attachment to cloud storage
  static Future<AttachmentModel> uploadAttachment(
    AttachmentModel attachment,
    Uint8List fileData,
    String userId,
  ) async {
    try {
      // Create folder structure based on type
      String folderPath;
      switch (attachment.type) {
        case AttachmentType.image:
          folderPath = 'attachments/images';
          break;
        case AttachmentType.video:
          folderPath = 'attachments/videos';
          break;
        case AttachmentType.document:
          folderPath = 'attachments/documents';
          break;
      }

      // Upload file
      final String? uploadedUrl = await _storageService.uploadFile(
        fileData,
        attachment.fileName,
        folderPath,
      );

      if (uploadedUrl == null) {
        throw Exception('Failed to upload file to cloud storage');
      }

      final String url = uploadedUrl;

      // Generate thumbnail for videos (if needed)
      String? thumbnailUrl;
      if (attachment.type == AttachmentType.video) {
        try {
          final thumbnailData = await VideoCompressionService.generateThumbnail(fileData);
          if (thumbnailData != null) {
            final thumbnailFileName = '${path.basenameWithoutExtension(attachment.fileName)}_thumb.jpg';
            final uploadedThumbnailUrl = await _storageService.uploadFile(
              thumbnailData,
              thumbnailFileName,
              'attachments/thumbnails',
            );
            thumbnailUrl = uploadedThumbnailUrl;
          }
        } catch (e) {
          debugPrint('Error generating video thumbnail: $e');
        }
      }

      return attachment.copyWith(
        id: _generateAttachmentId(),
        url: url,
        thumbnailUrl: thumbnailUrl,
        uploadedBy: userId,
      );
    } catch (e) {
      debugPrint('Error uploading attachment: $e');
      throw Exception('Failed to upload attachment: $e');
    }
  }

  /// Generate unique file name with HM prefix and date
  static String _generateUniqueFileName(String originalFileName) {
    final now = DateTime.now();
    final dateStr = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
    final timeStr = '${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}';
    final extension = path.extension(originalFileName);

    // Generate sequential number for the day
    final sequentialNumber = now.millisecondsSinceEpoch % 10000;

    return 'HM_${dateStr}_${timeStr}_${sequentialNumber.toString().padLeft(4, '0')}$extension';
  }

  /// Generate unique attachment ID
  static String _generateAttachmentId() {
    return 'att_${DateTime.now().millisecondsSinceEpoch}_${(DateTime.now().microsecond % 1000).toString().padLeft(3, '0')}';
  }

  /// Get MIME type from file extension
  static String _getMimeTypeFromExtension(String extension) {
    switch (extension.toLowerCase()) {
      // Images
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.png':
        return 'image/png';
      case '.gif':
        return 'image/gif';
      case '.webp':
        return 'image/webp';
      case '.bmp':
        return 'image/bmp';

      // Videos
      case '.mp4':
        return 'video/mp4';
      case '.mov':
        return 'video/quicktime';
      case '.avi':
        return 'video/x-msvideo';
      case '.mkv':
        return 'video/x-matroska';
      case '.webm':
        return 'video/webm';
      case '.3gp':
        return 'video/3gpp';

      // Documents
      case '.pdf':
        return 'application/pdf';
      case '.doc':
        return 'application/msword';
      case '.docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case '.txt':
        return 'text/plain';
      case '.rtf':
        return 'application/rtf';
      case '.odt':
        return 'application/vnd.oasis.opendocument.text';

      default:
        return 'application/octet-stream';
    }
  }

  /// Delete attachment from cloud storage
  static Future<void> deleteAttachment(AttachmentModel attachment) async {
    try {
      // Delete main file
      await _storageService.deleteFile(attachment.url);

      // Delete thumbnail if exists
      if (attachment.thumbnailUrl != null) {
        await _storageService.deleteFile(attachment.thumbnailUrl!);
      }
    } catch (e) {
      debugPrint('Error deleting attachment: $e');
      throw Exception('Failed to delete attachment: $e');
    }
  }
}
