// This file is a placeholder for the Firebase configuration
// In a real project, you would generate this file using the FlutterFire CLI
// Run: flutterfire configure
// For now, we'll use a placeholder to demonstrate the structure

import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  // Firebase configuration values
  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBRJaBdiPVuKWN0g9V5aXUVxaJyBufnY2I',
    appId: '1:538173267535:web:9656e47abf7cce24f6d43a',
    messagingSenderId: '538173267535',
    projectId: 'elmohtarfeen-44665',
    authDomain: 'elmohtarfeen-44665.firebaseapp.com',
    storageBucket: 'elmohtarfeen-44665.firebasestorage.app',
    measurementId: 'G-JNH98ECNVD',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBRJaBdiPVuKWN0g9V5aXUVxaJyBufnY2I',
    appId: '1:538173267535:web:9656e47abf7cce24f6d43a',
    messagingSenderId: '538173267535',
    projectId: 'elmohtarfeen-44665',
    storageBucket: 'elmohtarfeen-44665.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBRJaBdiPVuKWN0g9V5aXUVxaJyBufnY2I',
    appId: '1:538173267535:web:9656e47abf7cce24f6d43a',
    messagingSenderId: '538173267535',
    projectId: 'elmohtarfeen-44665',
    storageBucket: 'elmohtarfeen-44665.firebasestorage.app',
    iosClientId: 'YOUR_IOS_CLIENT_ID', // Update this when you have iOS client ID
    iosBundleId: 'com.mohamedrady.hmdeviceerrors',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBRJaBdiPVuKWN0g9V5aXUVxaJyBufnY2I',
    appId: '1:538173267535:web:9656e47abf7cce24f6d43a',
    messagingSenderId: '538173267535',
    projectId: 'elmohtarfeen-44665',
    storageBucket: 'elmohtarfeen-44665.firebasestorage.app',
    iosClientId: 'YOUR_IOS_CLIENT_ID', // Update this when you have macOS client ID
    iosBundleId: 'com.mohamedrady.hmdeviceerrors',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBRJaBdiPVuKWN0g9V5aXUVxaJyBufnY2I',
    appId: '1:538173267535:web:9656e47abf7cce24f6d43a',
    messagingSenderId: '538173267535',
    projectId: 'elmohtarfeen-44665',
    storageBucket: 'elmohtarfeen-44665.firebasestorage.app',
  );
}
