import java.util.Properties
import java.io.FileInputStream

plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
    // Add the Google services Gradle plugin
    id("com.google.gms.google-services")
}

// Load key.properties file
val keystorePropertiesFile = rootProject.file("key.properties")
val keystoreProperties = Properties()
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "com.mohamedrady.hmdeviceerrors"
    compileSdk = 35
    // Disable NDK requirements
    // ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
        isCoreLibraryDesugaringEnabled = true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
        freeCompilerArgs += listOf(
            "-Xjvm-default=all",
            "-opt-in=kotlin.RequiresOptIn"
        )
    }

    defaultConfig {
        // Application ID for HM Device Errors app
        applicationId = "com.mohamedrady.hmdeviceerrors"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 23
        targetSdk = 35
        versionCode = 9
        versionName = "1.0.7"

        // Enable multidex for large apps
        multiDexEnabled = true

        // Optimize for smaller APK size
        vectorDrawables.useSupportLibrary = true
    }

    signingConfigs {
        create("release") {
            if (keystorePropertiesFile.exists()) {
                keyAlias = keystoreProperties["keyAlias"] as String
                keyPassword = keystoreProperties["keyPassword"] as String
                storeFile = file(keystoreProperties["storeFile"] as String)
                storePassword = keystoreProperties["storePassword"] as String
            }
        }
    }

    buildTypes {
        release {
            // Use the release signing config if key.properties exists, otherwise use debug
            signingConfig = if (keystorePropertiesFile.exists()) {
                signingConfigs.getByName("release")
            } else {
                signingConfigs.getByName("debug")
            }

            // Enable R8 shrinking for smaller APK size
            isMinifyEnabled = true
            isShrinkResources = true
            isDebuggable = false
            isJniDebuggable = false
            isRenderscriptDebuggable = false
            isPseudoLocalesEnabled = false

            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")

            // Additional optimizations for smaller APK
            // Enable NDK for specific ABIs to reduce APK size (focus on most common architectures)
            ndk {
                abiFilters.addAll(listOf("armeabi-v7a", "arm64-v8a"))
            }

            // Enable additional optimizations for maximum size reduction
            packaging {
                resources {
                    excludes += listOf(
                        "META-INF/DEPENDENCIES",
                        "META-INF/LICENSE",
                        "META-INF/LICENSE.txt",
                        "META-INF/license.txt",
                        "META-INF/NOTICE",
                        "META-INF/NOTICE.txt",
                        "META-INF/notice.txt",
                        "META-INF/ASL2.0",
                        "META-INF/*.kotlin_module",
                        "META-INF/versions/**",
                        "kotlin/**",
                        "**/*.kotlin_metadata",
                        "**/*.kotlin_builtins",
                        "**/*.pro",
                        // Additional exclusions for smaller APK
                        "META-INF/INDEX.LIST",
                        "META-INF/MANIFEST.MF",
                        "META-INF/maven/**",
                        "META-INF/proguard/**",
                        "**/*.properties",
                        "**/*.version",
                        "**/*.txt",
                        "**/*.md",
                        "**/*.rst",
                        "DebugProbesKt.bin",
                        "kotlin-tooling-metadata.json"
                    )
                }
                // Compress native libraries
                jniLibs {
                    useLegacyPackaging = false
                }
                // Compress resources
                resources {
                    excludes += "**/*.md"
                }
            }

            // Optimize for size
            buildConfigField("boolean", "ENABLE_LOGGING", "false")
            buildConfigField("boolean", "ENABLE_ANALYTICS", "true")
        }

        debug {
            isMinifyEnabled = false
            isDebuggable = true
            applicationIdSuffix = ".debug"
            versionNameSuffix = "-debug"
        }
    }
}

flutter {
    source = "../.."
}

// Configure Java compilation to suppress obsolete warnings
tasks.withType<JavaCompile> {
    options.compilerArgs.addAll(listOf(
        "-Xlint:-options",
        "-Xlint:-deprecation"
    ))
}

dependencies {
    // Import the Firebase BoM
    implementation(platform("com.google.firebase:firebase-bom:33.14.0"))

    // Add the dependencies for Firebase products
    implementation("com.google.firebase:firebase-analytics")
    implementation("com.google.firebase:firebase-auth")
    implementation("com.google.firebase:firebase-firestore")
    implementation("com.google.firebase:firebase-storage")

    // Core library desugaring for flutter_local_notifications
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.4")
}
