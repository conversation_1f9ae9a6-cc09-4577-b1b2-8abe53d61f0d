import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:path/path.dart' as path;
import '../services/error_translation_service.dart';

/// خدمة شاملة لإدارة صور المستخدمين مع إمكانية التعديل والحذف
class UserProfileImageService {
  static final FirebaseStorage _storage = FirebaseStorage.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final ImagePicker _picker = ImagePicker();

  /// رفع صورة جديدة للمستخدم
  static Future<String> uploadProfileImage({
    required File imageFile,
    required String userId,
    Function(double)? onProgress,
  }) async {
    try {
      // التحقق من صحة الملف
      await _validateImageFile(imageFile);

      // إنشاء مسار فريد للصورة
      final String fileName = 'profile_${userId}_${DateTime.now().millisecondsSinceEpoch}${path.extension(imageFile.path)}';
      final String filePath = 'user_profiles/$userId/$fileName';

      // رفع الصورة إلى Firebase Storage
      final Reference ref = _storage.ref().child(filePath);
      final UploadTask uploadTask = ref.putFile(imageFile);

      // مراقبة تقدم الرفع
      if (onProgress != null) {
        uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
          final double progress = snapshot.bytesTransferred / snapshot.totalBytes;
          onProgress(progress);
        });
      }

      // انتظار اكتمال الرفع
      final TaskSnapshot snapshot = await uploadTask;
      final String downloadUrl = await snapshot.ref.getDownloadURL();

      // حذف الصورة القديمة إذا كانت موجودة
      await _deleteOldProfileImage(userId);

      // تحديث بيانات المستخدم في Firestore
      await _updateUserProfileImage(userId, downloadUrl);

      return downloadUrl;
    } catch (e) {
      throw Exception(ErrorTranslationService.translateError(e.toString()));
    }
  }

  /// اختيار صورة من المعرض
  static Future<File?> pickImageFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      throw Exception(ErrorTranslationService.translateError(e.toString()));
    }
  }

  /// اختيار صورة من الكاميرا
  static Future<File?> pickImageFromCamera() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      throw Exception(ErrorTranslationService.translateError(e.toString()));
    }
  }

  /// حذف صورة المستخدم
  static Future<void> deleteProfileImage(String userId) async {
    try {
      // حذف الصورة من Firebase Storage
      await _deleteOldProfileImage(userId);

      // إزالة رابط الصورة من بيانات المستخدم
      await _updateUserProfileImage(userId, null);

      // تحديث صورة المستخدم في Firebase Auth إذا كان هو المستخدم الحالي
      if (_auth.currentUser?.uid == userId) {
        await _auth.currentUser?.updatePhotoURL(null);
      }
    } catch (e) {
      throw Exception(ErrorTranslationService.translateError(e.toString()));
    }
  }

  /// عرض حوار اختيار مصدر الصورة
  static Future<File?> showImageSourceDialog(BuildContext context) async {
    final bool isRTL = Directionality.of(context) == TextDirection.rtl;

    return await showDialog<File?>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            isRTL ? 'اختر مصدر الصورة' : 'Choose Image Source',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library, color: Colors.blue),
                title: Text(isRTL ? 'المعرض' : 'Gallery'),
                onTap: () async {
                  Navigator.of(context).pop();
                  final file = await pickImageFromGallery();
                  if (context.mounted) {
                    Navigator.of(context).pop(file);
                  }
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt, color: Colors.green),
                title: Text(isRTL ? 'الكاميرا' : 'Camera'),
                onTap: () async {
                  Navigator.of(context).pop();
                  final file = await pickImageFromCamera();
                  if (context.mounted) {
                    Navigator.of(context).pop(file);
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(isRTL ? 'إلغاء' : 'Cancel'),
            ),
          ],
        );
      },
    );
  }

  /// عرض حوار تأكيد حذف الصورة
  static Future<bool> showDeleteConfirmationDialog(BuildContext context) async {
    final bool isRTL = Directionality.of(context) == TextDirection.rtl;

    return await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            isRTL ? 'حذف الصورة' : 'Delete Image',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Text(
            isRTL
              ? 'هل أنت متأكد من حذف صورة الملف الشخصي؟'
              : 'Are you sure you want to delete the profile image?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(isRTL ? 'إلغاء' : 'Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: Text(isRTL ? 'حذف' : 'Delete'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// التحقق من صحة ملف الصورة
  static Future<void> _validateImageFile(File imageFile) async {
    // التحقق من وجود الملف
    if (!await imageFile.exists()) {
      throw Exception('الملف غير موجود');
    }

    // التحقق من حجم الملف (5 ميجابايت كحد أقصى)
    final int fileSizeInBytes = await imageFile.length();
    const int maxSizeInBytes = 5 * 1024 * 1024; // 5 MB

    if (fileSizeInBytes > maxSizeInBytes) {
      throw Exception('حجم الصورة كبير جداً. الحد الأقصى 5 ميجابايت');
    }

    // التحقق من نوع الملف
    final String extension = path.extension(imageFile.path).toLowerCase();
    const List<String> allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif'];

    if (!allowedExtensions.contains(extension)) {
      throw Exception('نوع الملف غير مدعوم. الأنواع المدعومة: JPG, PNG, GIF');
    }
  }

  /// حذف الصورة القديمة من Firebase Storage
  static Future<void> _deleteOldProfileImage(String userId) async {
    try {
      // الحصول على بيانات المستخدم الحالية
      final DocumentSnapshot userDoc = await _firestore
          .collection('users')
          .doc(userId)
          .get();

      if (userDoc.exists) {
        final Map<String, dynamic>? userData = userDoc.data() as Map<String, dynamic>?;
        final String? currentImageUrl = userData?['profileImageUrl'];

        if (currentImageUrl != null && currentImageUrl.isNotEmpty) {
          // استخراج مسار الملف من الرابط
          final Reference ref = _storage.refFromURL(currentImageUrl);
          await ref.delete();
        }
      }
    } catch (e) {
      // لا نرمي خطأ هنا لأن حذف الصورة القديمة ليس ضرورياً لنجاح العملية
      debugPrint('Warning: Could not delete old profile image: $e');
    }
  }

  /// تحديث رابط صورة المستخدم في Firestore
  static Future<void> _updateUserProfileImage(String userId, String? imageUrl) async {
    await _firestore.collection('users').doc(userId).update({
      'profileImageUrl': imageUrl,
      'updatedAt': FieldValue.serverTimestamp(),
    });

    // تحديث صورة المستخدم في Firebase Auth إذا كان هو المستخدم الحالي
    if (_auth.currentUser?.uid == userId && imageUrl != null) {
      await _auth.currentUser?.updatePhotoURL(imageUrl);
    }
  }

  /// الحصول على رابط صورة المستخدم
  static Future<String?> getUserProfileImageUrl(String userId) async {
    try {
      final DocumentSnapshot userDoc = await _firestore
          .collection('users')
          .doc(userId)
          .get();

      if (userDoc.exists) {
        final Map<String, dynamic>? userData = userDoc.data() as Map<String, dynamic>?;
        return userData?['profileImageUrl'];
      }
      return null;
    } catch (e) {
      debugPrint('Error getting user profile image URL: $e');
      return null;
    }
  }

  /// تحديث صورة المستخدم مع معالجة شاملة للأخطاء
  static Future<String?> updateProfileImageWithErrorHandling({
    required BuildContext context,
    required String userId,
    required VoidCallback onSuccess,
    required Function(String) onError,
    Function(double)? onProgress,
  }) async {
    try {
      // عرض حوار اختيار مصدر الصورة
      final File? imageFile = await showImageSourceDialog(context);

      if (imageFile == null) {
        return null; // المستخدم ألغى العملية
      }

      // رفع الصورة
      final String imageUrl = await uploadProfileImage(
        imageFile: imageFile,
        userId: userId,
        onProgress: onProgress,
      );

      onSuccess();
      return imageUrl;
    } catch (e) {
      onError(e.toString());
      return null;
    }
  }
}
