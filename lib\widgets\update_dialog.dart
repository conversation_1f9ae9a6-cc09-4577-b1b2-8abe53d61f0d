import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/update_provider.dart';
import '../providers/locale_provider.dart';
import '../services/app_update_service.dart';

class UpdateDialog extends StatelessWidget {
  final UpdateInfo updateInfo;
  final bool canDismiss;

  const UpdateDialog({
    super.key,
    required this.updateInfo,
    this.canDismiss = true,
  });

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    return PopScope(
      canPop: canDismiss && !updateInfo.isRequired,
      child: AlertDialog(
        title: Row(
          children: [
            Icon(
              _getUpdateIcon(),
              color: _getUpdateColor(),
              size: 28,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                updateInfo.title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              updateInfo.message,
              style: const TextStyle(fontSize: 16),
            ),
            if (updateInfo.currentVersion != null && updateInfo.latestVersion != null) ...[
              const SizedBox(height: 16),
              _buildVersionInfo(isRTL),
            ],
            if (updateInfo.type == UpdateType.maintenance) ...[
              const SizedBox(height: 16),
              _buildMaintenanceInfo(isRTL),
            ],
          ],
        ),
        actions: _buildActions(context, isRTL),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }

  Widget _buildVersionInfo(bool isRTL) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                isRTL ? 'الإصدار الحالي:' : 'Current Version:',
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  color: Colors.grey,
                ),
              ),
              Text(
                updateInfo.currentVersion ?? '',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                isRTL ? 'الإصدار الجديد:' : 'Latest Version:',
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  color: Colors.grey,
                ),
              ),
              Text(
                updateInfo.latestVersion ?? '',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _getUpdateColor(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMaintenanceInfo(bool isRTL) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange[200]!),
      ),
      child: Row(
        children: [
          Icon(
            Icons.build,
            color: Colors.orange[600],
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              isRTL
                ? 'التطبيق تحت الصيانة. يرجى المحاولة لاحقاً.'
                : 'App is under maintenance. Please try again later.',
              style: TextStyle(
                color: Colors.orange[800],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildActions(BuildContext context, bool isRTL) {
    final updateProvider = Provider.of<UpdateProvider>(context);

    if (updateInfo.type == UpdateType.maintenance) {
      return [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(isRTL ? 'موافق' : 'OK'),
        ),
      ];
    }

    final actions = <Widget>[];

    // Skip button (only for optional updates)
    if (!updateInfo.isRequired && canDismiss) {
      actions.add(
        TextButton(
          onPressed: () {
            updateProvider.skipVersion();
            Navigator.of(context).pop();
          },
          child: Text(
            isRTL ? 'تخطي' : 'Skip',
            style: TextStyle(color: Colors.grey[600]),
          ),
        ),
      );
    }

    // Later button (only for optional updates)
    if (!updateInfo.isRequired && canDismiss) {
      actions.add(
        TextButton(
          onPressed: () {
            updateProvider.dismissUpdate();
            Navigator.of(context).pop();
          },
          child: Text(
            isRTL ? 'لاحقاً' : 'Later',
            style: TextStyle(color: Colors.grey[600]),
          ),
        ),
      );
    }

    // Update button
    actions.add(
      ElevatedButton(
        onPressed: updateProvider.isChecking
            ? null
            : () async {
                final success = await updateProvider.launchUpdate();
                if (success && context.mounted) {
                  Navigator.of(context).pop();
                }
              },
        style: ElevatedButton.styleFrom(
          backgroundColor: _getUpdateColor(),
          foregroundColor: Colors.white,
        ),
        child: updateProvider.isChecking
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(isRTL ? 'تحديث' : 'Update'),
      ),
    );

    return actions;
  }

  IconData _getUpdateIcon() {
    switch (updateInfo.type) {
      case UpdateType.required:
        return Icons.system_update_alt;
      case UpdateType.optional:
        return Icons.update;
      case UpdateType.maintenance:
        return Icons.build;
    }
  }

  Color _getUpdateColor() {
    switch (updateInfo.type) {
      case UpdateType.required:
        return Colors.red;
      case UpdateType.optional:
        return Colors.blue;
      case UpdateType.maintenance:
        return Colors.orange;
    }
  }

  /// Show update dialog
  static Future<void> show(
    BuildContext context,
    UpdateInfo updateInfo, {
    bool canDismiss = true,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: canDismiss && !updateInfo.isRequired,
      builder: (context) => UpdateDialog(
        updateInfo: updateInfo,
        canDismiss: canDismiss,
      ),
    );
  }
}

/// Update notification widget for subtle notifications
class UpdateNotificationWidget extends StatelessWidget {
  final UpdateInfo updateInfo;
  final VoidCallback? onTap;
  final VoidCallback? onDismiss;

  const UpdateNotificationWidget({
    super.key,
    required this.updateInfo,
    this.onTap,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getUpdateColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _getUpdateColor().withValues(alpha: 0.3)),
      ),
      child: ListTile(
        leading: Icon(
          _getUpdateIcon(),
          color: _getUpdateColor(),
        ),
        title: Text(
          updateInfo.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          updateInfo.latestVersion != null
              ? (isRTL
                  ? 'الإصدار ${updateInfo.latestVersion} متوفر'
                  : 'Version ${updateInfo.latestVersion} available')
              : updateInfo.message,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (!updateInfo.isRequired && onDismiss != null)
              IconButton(
                onPressed: onDismiss,
                icon: const Icon(Icons.close),
                iconSize: 20,
              ),
            Icon(
              isRTL ? Icons.arrow_back_ios : Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey,
            ),
          ],
        ),
        onTap: onTap,
      ),
    );
  }

  IconData _getUpdateIcon() {
    switch (updateInfo.type) {
      case UpdateType.required:
        return Icons.priority_high;
      case UpdateType.optional:
        return Icons.update;
      case UpdateType.maintenance:
        return Icons.build;
    }
  }

  Color _getUpdateColor() {
    switch (updateInfo.type) {
      case UpdateType.required:
        return Colors.red;
      case UpdateType.optional:
        return Colors.blue;
      case UpdateType.maintenance:
        return Colors.orange;
    }
  }
}
