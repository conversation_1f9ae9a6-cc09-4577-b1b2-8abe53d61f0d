import 'package:cloud_firestore/cloud_firestore.dart';

class DeviceCategory {
  final String id;
  final String name;
  final String? iconUrl;
  final String? description;
  final int deviceCount;

  DeviceCategory({
    required this.id,
    required this.name,
    this.iconUrl,
    this.description,
    this.deviceCount = 0,
  });

  factory DeviceCategory.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return DeviceCategory(
      id: doc.id,
      name: data['name'] ?? '',
      iconUrl: data['iconUrl'],
      description: data['description'],
      deviceCount: data['deviceCount'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'iconUrl': iconUrl,
      'description': description,
      'deviceCount': deviceCount,
    };
  }

  DeviceCategory copyWith({
    String? id,
    String? name,
    String? iconUrl,
    String? description,
    int? deviceCount,
  }) {
    return DeviceCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      iconUrl: iconUrl ?? this.iconUrl,
      description: description ?? this.description,
      deviceCount: deviceCount ?? this.deviceCount,
    );
  }
}
