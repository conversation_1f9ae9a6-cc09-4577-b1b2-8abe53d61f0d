import 'package:cloud_firestore/cloud_firestore.dart';

/// Enum for different types of attachments
enum AttachmentType {
  image,
  video,
  document,
}

/// Model for file attachments in device errors
class AttachmentModel {
  final String id;
  final String fileName;
  final String originalFileName;
  final AttachmentType type;
  final String url;
  final String? thumbnailUrl; // For videos and documents
  final int fileSize; // in bytes
  final String mimeType;
  final DateTime uploadedAt;
  final String uploadedBy;
  final Map<String, dynamic>? metadata; // Additional info like duration for videos

  AttachmentModel({
    required this.id,
    required this.fileName,
    required this.originalFileName,
    required this.type,
    required this.url,
    this.thumbnailUrl,
    required this.fileSize,
    required this.mimeType,
    required this.uploadedAt,
    required this.uploadedBy,
    this.metadata,
  });

  factory AttachmentModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AttachmentModel(
      id: doc.id,
      fileName: data['fileName'] ?? '',
      originalFileName: data['originalFileName'] ?? '',
      type: AttachmentType.values.firstWhere(
        (e) => e.toString() == 'AttachmentType.${data['type']}',
        orElse: () => AttachmentType.image,
      ),
      url: data['url'] ?? '',
      thumbnailUrl: data['thumbnailUrl'],
      fileSize: data['fileSize'] ?? 0,
      mimeType: data['mimeType'] ?? '',
      uploadedAt: (data['uploadedAt'] as Timestamp).toDate(),
      uploadedBy: data['uploadedBy'] ?? '',
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  factory AttachmentModel.fromMap(Map<String, dynamic> data, String id) {
    return AttachmentModel(
      id: id,
      fileName: data['fileName'] ?? '',
      originalFileName: data['originalFileName'] ?? '',
      type: AttachmentType.values.firstWhere(
        (e) => e.toString() == 'AttachmentType.${data['type']}',
        orElse: () => AttachmentType.image,
      ),
      url: data['url'] ?? '',
      thumbnailUrl: data['thumbnailUrl'],
      fileSize: data['fileSize'] ?? 0,
      mimeType: data['mimeType'] ?? '',
      uploadedAt: data['uploadedAt'] is Timestamp
          ? (data['uploadedAt'] as Timestamp).toDate()
          : DateTime.tryParse(data['uploadedAt'] ?? '') ?? DateTime.now(),
      uploadedBy: data['uploadedBy'] ?? '',
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'fileName': fileName,
      'originalFileName': originalFileName,
      'type': type.toString().split('.').last,
      'url': url,
      'thumbnailUrl': thumbnailUrl,
      'fileSize': fileSize,
      'mimeType': mimeType,
      'uploadedAt': Timestamp.fromDate(uploadedAt),
      'uploadedBy': uploadedBy,
      'metadata': metadata,
    };
  }

  AttachmentModel copyWith({
    String? id,
    String? fileName,
    String? originalFileName,
    AttachmentType? type,
    String? url,
    String? thumbnailUrl,
    int? fileSize,
    String? mimeType,
    DateTime? uploadedAt,
    String? uploadedBy,
    Map<String, dynamic>? metadata,
  }) {
    return AttachmentModel(
      id: id ?? this.id,
      fileName: fileName ?? this.fileName,
      originalFileName: originalFileName ?? this.originalFileName,
      type: type ?? this.type,
      url: url ?? this.url,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      fileSize: fileSize ?? this.fileSize,
      mimeType: mimeType ?? this.mimeType,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      uploadedBy: uploadedBy ?? this.uploadedBy,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Get file extension from fileName
  String get fileExtension {
    return fileName.split('.').last.toLowerCase();
  }

  /// Get formatted file size
  String get formattedFileSize {
    if (fileSize < 1024) {
      return '$fileSize B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    } else if (fileSize < 1024 * 1024 * 1024) {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// Check if file is an image
  bool get isImage => type == AttachmentType.image;

  /// Check if file is a video
  bool get isVideo => type == AttachmentType.video;

  /// Check if file is a document
  bool get isDocument => type == AttachmentType.document;

  /// Check if URL is valid
  bool get hasValidUrl => url.isNotEmpty && Uri.tryParse(url) != null;

  /// Get Google Drive direct download URL if it's a Google Drive file
  String get directDownloadUrl {
    if (url.contains('drive.google.com')) {
      // Extract file ID from various Google Drive URL formats
      String? fileId;

      // Try different patterns
      final patterns = [
        RegExp(r'/file/d/([a-zA-Z0-9-_]+)'),
        RegExp(r'id=([a-zA-Z0-9-_]+)'),
        RegExp(r'/([a-zA-Z0-9-_]+)/view'),
      ];

      for (final pattern in patterns) {
        final match = pattern.firstMatch(url);
        if (match != null && match.groupCount > 0) {
          fileId = match.group(1);
          break;
        }
      }

      if (fileId != null) {
        // استخدام استراتيجية محسنة حسب نوع الملف
        if (type == AttachmentType.image) {
          // للصور: استخدم thumbnail URL (أكثر استقراراً)
          return 'https://drive.google.com/thumbnail?id=$fileId&sz=w1000';
        } else {
          // للملفات الأخرى: استخدم export download
          return 'https://drive.google.com/uc?export=download&id=$fileId';
        }
      }
    }
    return url;
  }

  /// Get Google Drive thumbnail URL for images
  String get googleDriveThumbnailUrl {
    if (url.contains('drive.google.com') && type == AttachmentType.image) {
      final fileId = _extractGoogleDriveFileId(url);
      if (fileId != null) {
        return 'https://drive.google.com/thumbnail?id=$fileId&sz=w300';
      }
    }
    return thumbnailUrl ?? url;
  }

  /// Extract Google Drive file ID from URL
  String? _extractGoogleDriveFileId(String url) {
    final patterns = [
      RegExp(r'/file/d/([a-zA-Z0-9-_]+)'),
      RegExp(r'id=([a-zA-Z0-9-_]+)'),
      RegExp(r'/([a-zA-Z0-9-_]+)/view'),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(url);
      if (match != null && match.groupCount > 0) {
        return match.group(1);
      }
    }
    return null;
  }

  /// Get icon for the file type
  String get typeIcon {
    switch (type) {
      case AttachmentType.image:
        return 'image';
      case AttachmentType.video:
        return 'videocam';
      case AttachmentType.document:
        return 'description';
    }
  }

  /// Get supported file extensions for each type
  static List<String> getSupportedExtensions(AttachmentType type) {
    switch (type) {
      case AttachmentType.image:
        return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
      case AttachmentType.video:
        return ['mp4', 'mov', 'avi', 'mkv', 'webm', '3gp'];
      case AttachmentType.document:
        return ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'];
    }
  }

  /// Get MIME types for each attachment type
  static List<String> getSupportedMimeTypes(AttachmentType type) {
    switch (type) {
      case AttachmentType.image:
        return [
          'image/jpeg',
          'image/png',
          'image/gif',
          'image/webp',
          'image/bmp'
        ];
      case AttachmentType.video:
        return [
          'video/mp4',
          'video/quicktime',
          'video/x-msvideo',
          'video/x-matroska',
          'video/webm',
          'video/3gpp'
        ];
      case AttachmentType.document:
        return [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'text/plain',
          'application/rtf',
          'application/vnd.oasis.opendocument.text'
        ];
    }
  }

  /// Determine attachment type from MIME type
  static AttachmentType getTypeFromMimeType(String mimeType) {
    if (getSupportedMimeTypes(AttachmentType.image).contains(mimeType)) {
      return AttachmentType.image;
    } else if (getSupportedMimeTypes(AttachmentType.video).contains(mimeType)) {
      return AttachmentType.video;
    } else if (getSupportedMimeTypes(AttachmentType.document).contains(mimeType)) {
      return AttachmentType.document;
    } else {
      // Default to document for unknown types
      return AttachmentType.document;
    }
  }

  /// Determine attachment type from file extension
  static AttachmentType getTypeFromExtension(String extension) {
    final ext = extension.toLowerCase();
    if (getSupportedExtensions(AttachmentType.image).contains(ext)) {
      return AttachmentType.image;
    } else if (getSupportedExtensions(AttachmentType.video).contains(ext)) {
      return AttachmentType.video;
    } else if (getSupportedExtensions(AttachmentType.document).contains(ext)) {
      return AttachmentType.document;
    } else {
      return AttachmentType.document;
    }
  }
}
