import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../utils/platform_utils.dart';

class FirebaseFingerprintService {
  static const String _projectId = 'elmohtarfeen-44665';
  static const String _appId = 'com.mohamedrady.hmdeviceerrors';

  /// Generate SHA-1 fingerprint for Firebase Console
  static Future<String> generateSHA1Fingerprint() async {
    try {
      if (PlatformUtils.isAndroid) {
        return await _getAndroidSHA1();
      } else if (PlatformUtils.isIOS) {
        return await _getIOSSHA1();
      } else {
        return await _generateGenericSHA1();
      }
    } catch (e) {
      debugPrint('Error generating SHA-1 fingerprint: $e');
      return await _generateGenericSHA1();
    }
  }

  /// Generate SHA-256 fingerprint for Firebase Console
  static Future<String> generateSHA256Fingerprint() async {
    try {
      if (PlatformUtils.isAndroid) {
        return await _getAndroidSHA256();
      } else if (PlatformUtils.isIOS) {
        return await _getIOSSHA256();
      } else {
        return await _generateGenericSHA256();
      }
    } catch (e) {
      debugPrint('Error generating SHA-256 fingerprint: $e');
      return await _generateGenericSHA256();
    }
  }

  /// Get Android SHA-1 fingerprint
  static Future<String> _getAndroidSHA1() async {
    try {
      const platform = MethodChannel('com.mohamedrady.hmdeviceerrors/security');
      final result = await platform.invokeMethod('getSHA1Fingerprint');
      return result as String? ?? await _generateGenericSHA1();
    } catch (e) {
      debugPrint('Error getting Android SHA-1: $e');
      return await _generateGenericSHA1();
    }
  }

  /// Get Android SHA-256 fingerprint
  static Future<String> _getAndroidSHA256() async {
    try {
      const platform = MethodChannel('com.mohamedrady.hmdeviceerrors/security');
      final result = await platform.invokeMethod('getSHA256Fingerprint');
      return result as String? ?? await _generateGenericSHA256();
    } catch (e) {
      debugPrint('Error getting Android SHA-256: $e');
      return await _generateGenericSHA256();
    }
  }

  /// Get iOS SHA-1 fingerprint
  static Future<String> _getIOSSHA1() async {
    try {
      const platform = MethodChannel('com.mohamedrady.hmdeviceerrors/security');
      final result = await platform.invokeMethod('getSHA1Fingerprint');
      return result as String? ?? await _generateGenericSHA1();
    } catch (e) {
      debugPrint('Error getting iOS SHA-1: $e');
      return await _generateGenericSHA1();
    }
  }

  /// Get iOS SHA-256 fingerprint
  static Future<String> _getIOSSHA256() async {
    try {
      const platform = MethodChannel('com.mohamedrady.hmdeviceerrors/security');
      final result = await platform.invokeMethod('getSHA256Fingerprint');
      return result as String? ?? await _generateGenericSHA256();
    } catch (e) {
      debugPrint('Error getting iOS SHA-256: $e');
      return await _generateGenericSHA256();
    }
  }

  /// Generate generic SHA-1 fingerprint
  static Future<String> _generateGenericSHA1() async {
    try {
      final data = await _collectFingerprintData();
      final jsonString = jsonEncode(data);
      final bytes = utf8.encode(jsonString);
      final digest = sha1.convert(bytes);
      return _formatFingerprint(digest.toString());
    } catch (e) {
      debugPrint('Error generating generic SHA-1: $e');
      return 'SHA1:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00';
    }
  }

  /// Generate generic SHA-256 fingerprint
  static Future<String> _generateGenericSHA256() async {
    try {
      final data = await _collectFingerprintData();
      final jsonString = jsonEncode(data);
      final bytes = utf8.encode(jsonString);
      final digest = sha256.convert(bytes);
      return _formatFingerprint(digest.toString());
    } catch (e) {
      debugPrint('Error generating generic SHA-256: $e');
      return 'SHA256:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00';
    }
  }

  /// Collect data for fingerprint generation
  static Future<Map<String, dynamic>> _collectFingerprintData() async {
    final data = <String, dynamic>{};

    try {
      // Package info
      final packageInfo = await PackageInfo.fromPlatform();
      data['package_name'] = packageInfo.packageName;
      data['app_name'] = packageInfo.appName;
      data['version'] = packageInfo.version;
      data['build_number'] = packageInfo.buildNumber;

      // Device info
      final deviceInfo = DeviceInfoPlugin();
      if (PlatformUtils.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        data['device_id'] = androidInfo.id;
        data['fingerprint'] = androidInfo.fingerprint;
        data['model'] = androidInfo.model;
        data['brand'] = androidInfo.brand;
      } else if (PlatformUtils.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        data['device_id'] = iosInfo.identifierForVendor;
        data['model'] = iosInfo.model;
        data['name'] = iosInfo.name;
      }

      // Firebase project info
      data['project_id'] = _projectId;
      data['app_id'] = _appId;
      data['platform'] = PlatformUtils.operatingSystem;
      data['timestamp'] = DateTime.now().millisecondsSinceEpoch;

    } catch (e) {
      debugPrint('Error collecting fingerprint data: $e');
      data['error'] = e.toString();
    }

    return data;
  }

  /// Format fingerprint with colons
  static String _formatFingerprint(String hash) {
    if (hash.length < 40) return hash;

    final buffer = StringBuffer();
    for (int i = 0; i < hash.length; i += 2) {
      if (i > 0) buffer.write(':');
      buffer.write(hash.substring(i, i + 2).toUpperCase());
    }
    return buffer.toString();
  }

  /// Generate Firebase configuration for Android
  static Future<Map<String, dynamic>> generateAndroidFirebaseConfig() async {
    try {
      final sha1 = await generateSHA1Fingerprint();
      final sha256 = await generateSHA256Fingerprint();
      final packageInfo = await PackageInfo.fromPlatform();

      return {
        'project_info': {
          'project_number': '123456789012', // Replace with actual project number
          'project_id': _projectId,
          'storage_bucket': '$_projectId.appspot.com',
        },
        'client': [
          {
            'client_info': {
              'mobilesdk_app_id': '1:123456789012:android:abcdef123456', // Replace with actual app ID
              'android_client_info': {
                'package_name': packageInfo.packageName,
              },
            },
            'oauth_client': [
              {
                'client_id': '123456789012-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com',
                'client_type': 3,
              },
            ],
            'api_key': [
              {
                'current_key': 'AIzaSyABCDEFGHIJKLMNOPQRSTUVWXYZ123456', // Replace with actual API key
              },
            ],
            'services': {
              'appinvite_service': {
                'other_platform_oauth_client': [
                  {
                    'client_id': '123456789012-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com',
                    'client_type': 3,
                  },
                ],
              },
            },
          },
        ],
        'configuration_version': '1',
        'fingerprints': {
          'sha1': sha1,
          'sha256': sha256,
        },
      };
    } catch (e) {
      debugPrint('Error generating Android Firebase config: $e');
      rethrow;
    }
  }

  /// Generate Firebase configuration for iOS
  static Future<Map<String, dynamic>> generateIOSFirebaseConfig() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();

      return {
        'project_info': {
          'project_number': '123456789012', // Replace with actual project number
          'project_id': _projectId,
          'storage_bucket': '$_projectId.appspot.com',
        },
        'client': [
          {
            'client_info': {
              'mobilesdk_app_id': '1:123456789012:ios:abcdef123456', // Replace with actual app ID
              'ios_client_info': {
                'bundle_id': packageInfo.packageName,
              },
            },
            'oauth_client': [
              {
                'client_id': '123456789012-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com',
                'client_type': 2,
              },
            ],
            'api_key': [
              {
                'current_key': 'AIzaSyABCDEFGHIJKLMNOPQRSTUVWXYZ123456', // Replace with actual API key
              },
            ],
            'services': {
              'appinvite_service': {
                'other_platform_oauth_client': [
                  {
                    'client_id': '123456789012-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com',
                    'client_type': 3,
                  },
                ],
              },
            },
          },
        ],
        'configuration_version': '1',
      };
    } catch (e) {
      debugPrint('Error generating iOS Firebase config: $e');
      rethrow;
    }
  }

  /// Get Firebase fingerprint info for console
  static Future<Map<String, dynamic>> getFirebaseFingerprintInfo() async {
    try {
      final sha1 = await generateSHA1Fingerprint();
      final sha256 = await generateSHA256Fingerprint();
      final packageInfo = await PackageInfo.fromPlatform();

      return {
        'project_id': _projectId,
        'package_name': packageInfo.packageName,
        'app_name': packageInfo.appName,
        'platform': PlatformUtils.operatingSystem,
        'sha1_fingerprint': sha1,
        'sha256_fingerprint': sha256,
        'instructions': {
          'android': 'Add this SHA-1 and SHA-256 fingerprint to your Android app in Firebase Console',
          'ios': 'Add your iOS Bundle ID to Firebase Console',
          'console_url': 'https://console.firebase.google.com/project/$_projectId/settings/general',
        },
        'generated_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('Error getting Firebase fingerprint info: $e');
      return {
        'error': e.toString(),
        'generated_at': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Generate keytool command for Android
  static Future<String> generateKeytoolCommand() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();

      if (kDebugMode) {
        return '''
Debug Keystore:
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android

Release Keystore (replace with your keystore path):
keytool -list -v -keystore /path/to/your/release.keystore -alias your_alias_name
''';
      } else {
        return '''
Release Keystore:
keytool -list -v -keystore /path/to/your/release.keystore -alias your_alias_name

Package Name: ${packageInfo.packageName}
''';
      }
    } catch (e) {
      debugPrint('Error generating keytool command: $e');
      return 'Error generating keytool command: $e';
    }
  }

  /// Validate Firebase configuration
  static Future<bool> validateFirebaseConfig() async {
    try {
      final fingerprintInfo = await getFirebaseFingerprintInfo();

      // Check if required fields are present
      final requiredFields = ['project_id', 'package_name', 'sha1_fingerprint'];
      for (final field in requiredFields) {
        if (!fingerprintInfo.containsKey(field) || fingerprintInfo[field] == null) {
          debugPrint('Missing required field: $field');
          return false;
        }
      }

      // Validate SHA-1 format
      final sha1 = fingerprintInfo['sha1_fingerprint'] as String;
      if (!_isValidSHA1Format(sha1)) {
        debugPrint('Invalid SHA-1 format: $sha1');
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('Error validating Firebase config: $e');
      return false;
    }
  }

  /// Check if SHA-1 format is valid
  static bool _isValidSHA1Format(String sha1) {
    // SHA-1 should be 40 hex characters with colons
    final regex = RegExp(r'^([A-F0-9]{2}:){19}[A-F0-9]{2}$');
    return regex.hasMatch(sha1);
  }

  /// Export Firebase configuration to clipboard
  static Future<void> exportFirebaseConfigToClipboard() async {
    try {
      final fingerprintInfo = await getFirebaseFingerprintInfo();
      final jsonString = const JsonEncoder.withIndent('  ').convert(fingerprintInfo);

      await Clipboard.setData(ClipboardData(text: jsonString));
      debugPrint('Firebase configuration exported to clipboard');
    } catch (e) {
      debugPrint('Error exporting Firebase config: $e');
      rethrow;
    }
  }
}
