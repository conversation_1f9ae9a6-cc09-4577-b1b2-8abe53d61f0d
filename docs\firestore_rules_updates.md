# تحديثات قواعد Firestore v1.1.0

## نظرة عامة

تم تحديث قواعد Firestore لتدعم التحسينات الجديدة في إدارة المرفقات وcache المتقدم. هذا التحديث يشمل قواعد جديدة لتتبع cache، إحصائيات التحميل، وتحسينات أمنية.

## التحديثات الرئيسية

### 1. تحسين validation المرفقات

#### قبل التحديث:
```javascript
function isValidAttachment(data) {
  return data.keys().hasAll(['id', 'fileName', 'originalFileName', 'type', 'url', 'fileSize', 'mimeType', 'uploadedAt', 'uploadedBy']) &&
         // Basic validation only...
}
```

#### بعد التحديث:
```javascript
function isValidAttachment(data) {
  return data.keys().hasAll(['id', 'fileName', 'originalFileName', 'type', 'url', 'fileSize', 'mimeType', 'uploadedAt', 'uploadedBy']) &&
         // Basic validation...
         // Optional fields validation
         (!data.keys().hasAny(['thumbnailUrl']) || (data.thumbnailUrl is string)) &&
         (!data.keys().hasAny(['metadata']) || (data.metadata is map)) &&
         (!data.keys().hasAny(['cacheInfo']) || (data.cacheInfo is map)) &&
         (!data.keys().hasAny(['downloadCount']) || (data.downloadCount is number && data.downloadCount >= 0)) &&
         (!data.keys().hasAny(['lastAccessed']) || (data.lastAccessed is timestamp));
}
```

### 2. تحسين MIME types لدعم Google Drive

#### الإضافات الجديدة:
```javascript
function isValidMimeType(mimeType, type) {
  return (type == 'image' && mimeType.matches('image/(jpeg|jpg|png|gif|webp|bmp|tiff)')) ||
         (type == 'video' && mimeType.matches('video/(mp4|avi|mov|wmv|flv|webm|3gpp|quicktime|x-msvideo|x-matroska)')) ||
         (type == 'document' && mimeType.matches('application/(pdf|msword|vnd\\.openxmlformats-officedocument\\.wordprocessingml\\.document|vnd\\.ms-excel|vnd\\.openxmlformats-officedocument\\.spreadsheetml\\.sheet|vnd\\.ms-powerpoint|vnd\\.openxmlformats-officedocument\\.presentationml\\.presentation|rtf|vnd\\.oasis\\.opendocument\\.text)|text/plain')) ||
         // Allow Google Drive MIME types
         (mimeType == 'application/octet-stream'); // Google Drive sometimes returns this
}
```

### 3. زيادة حدود أحجام الملفات

#### التحسينات:
- **الصور**: من 10MB إلى 15MB
- **الفيديو**: من 50MB إلى 100MB  
- **المستندات**: من 25MB إلى 50MB
- **افتراضي**: من 5MB إلى 10MB

```javascript
function isValidFileSize(fileSize, type) {
  return fileSize > 0 &&
         ((type == 'image' && fileSize <= 15728640) ||    // 15MB for images
          (type == 'video' && fileSize <= 104857600) ||   // 100MB for videos
          (type == 'document' && fileSize <= 52428800) || // 50MB for documents
          fileSize <= 10485760);                          // 10MB default
}
```

## المجموعات الجديدة

### 1. Attachment Cache Metadata
```javascript
match /attachment_cache/{cacheId} {
  // تتبع الملفات المحفوظة في cache
  allow read: if isValidUser();
  allow create: if isValidUser() && request.resource.data.userId == request.auth.uid;
  allow update: if isValidUser() && resource.data.userId == request.auth.uid;
  allow delete: if isValidUser() && resource.data.userId == request.auth.uid || isAdmin();
}
```

**الحقول المطلوبة**:
- `attachmentId`: معرف المرفق
- `userId`: معرف المستخدم
- `cachedAt`: تاريخ الحفظ في cache
- `fileSize`: حجم الملف
- `lastAccessed`: آخر وصول للملف

### 2. Attachment Download Statistics
```javascript
match /attachment_stats/{attachmentId} {
  // إحصائيات تحميل المرفقات
  allow read: if isValidUser();
  allow create, update: if isValidUser() || isAdmin();
  allow delete: if isAdmin();
}
```

**الحقول المتوقعة**:
- `downloadCount`: عدد مرات التحميل
- `lastDownloaded`: آخر تحميل
- `popularityScore`: نقاط الشعبية
- `averageRating`: التقييم المتوسط

### 3. User Cache Settings
```javascript
match /user_cache_settings/{userId} {
  // إعدادات cache خاصة بالمستخدم
  allow read: if isOwner(userId) || isAdmin();
  allow write: if isOwner(userId) || isAdmin();
}
```

**الحقول المتوقعة**:
- `maxCacheSize`: الحد الأقصى لحجم cache
- `autoCleanup`: تنظيف تلقائي
- `cacheImages`: حفظ الصور
- `cacheVideos`: حفظ الفيديو
- `cacheDocuments`: حفظ المستندات

### 4. Global Cache Statistics
```javascript
match /global_cache_stats/{statId} {
  // إحصائيات cache عامة (للإدارة فقط)
  allow read: if isAdmin();
  allow create, update: if isAdmin();
  allow delete: if isAdmin();
}
```

### 5. Attachment Access Logs
```javascript
match /attachment_access_logs/{logId} {
  // سجلات الوصول للمرفقات
  allow read: if isAdmin() || (isValidUser() && resource.data.userId == request.auth.uid);
  allow create: if isValidUser() && request.resource.data.userId == request.auth.uid;
  allow update: if false; // غير قابل للتعديل
  allow delete: if isAdmin();
}
```

**الحقول المطلوبة**:
- `attachmentId`: معرف المرفق
- `userId`: معرف المستخدم
- `accessedAt`: وقت الوصول
- `accessType`: نوع الوصول (view, download, share)

## تحديثات Firebase Storage

### 1. زيادة حدود الملفات
```javascript
function isValidImageFile() {
  return resource.contentType.matches('image/.*') &&
         resource.size < 15 * 1024 * 1024; // 15MB (زيادة من 10MB)
}

function isValidVideoFile() {
  return resource.contentType.matches('video/.*') &&
         resource.size < 100 * 1024 * 1024; // 100MB (زيادة من 50MB)
}

function isValidDocumentFile() {
  return resource.contentType in [...] &&
         resource.size < 50 * 1024 * 1024; // 50MB (زيادة من 20MB)
}
```

### 2. مسارات جديدة للـ Cache

#### Cache Files
```javascript
match /cache/{userId}/{cacheId} {
  allow read: if isAuthenticated();
  allow write: if (isAuthenticated() && request.auth.uid == userId && isValidAttachment()) || isAdmin();
  allow delete: if (isAuthenticated() && request.auth.uid == userId) || isAdmin();
}
```

#### Thumbnail Cache
```javascript
match /thumbnails/{userId}/{thumbnailId} {
  allow read: if isAuthenticated();
  allow write: if (isAuthenticated() && request.auth.uid == userId && isValidImageFile()) || isAdmin();
  allow delete: if (isAuthenticated() && request.auth.uid == userId) || isAdmin();
}
```

#### Google Drive Cache
```javascript
match /google_drive_cache/{userId}/{fileId} {
  allow read: if isAuthenticated();
  allow write: if (isAuthenticated() && request.auth.uid == userId && isValidAttachment()) || isAdmin();
  allow delete: if (isAuthenticated() && request.auth.uid == userId) || isAdmin();
}
```

#### Attachment Previews
```javascript
match /previews/{attachmentId} {
  allow read: if isAuthenticated();
  allow write: if isAdmin() && isValidImageFile();
  allow delete: if isAdmin();
}
```

## الفوائد الأمنية

### 1. تحكم دقيق في الوصول
- المستخدمون يمكنهم إدارة cache الخاص بهم فقط
- الإدارة لها صلاحيات كاملة
- سجلات الوصول غير قابلة للتعديل

### 2. validation محسن
- دعم حقول اختيارية في المرفقات
- validation أفضل لـ MIME types
- حدود ملفات أكثر مرونة

### 3. تتبع شامل
- سجلات وصول مفصلة
- إحصائيات تحميل
- مراقبة cache عامة

## التطبيق والنشر

### 1. نشر القواعد الجديدة
```bash
firebase deploy --only firestore:rules
firebase deploy --only storage
```

### 2. اختبار القواعد
```bash
firebase emulators:start --only firestore,storage
```

### 3. مراقبة الأداء
- تتبع استخدام cache
- مراقبة أحجام الملفات
- تحليل أنماط الوصول

## ملاحظات مهمة

### 1. التوافق مع الإصدارات السابقة
- جميع القواعد السابقة محفوظة
- الحقول الجديدة اختيارية
- لا توجد تغييرات كاسرة

### 2. الأمان
- جميع العمليات تتطلب مصادقة
- تحكم دقيق في الصلاحيات
- سجلات audit شاملة

### 3. الأداء
- قواعد محسنة للسرعة
- تقليل عدد الاستعلامات
- cache ذكي للبيانات

هذه التحديثات تدعم بشكل كامل التحسينات الجديدة في إدارة المرفقات وتوفر أساساً قوياً لميزات cache المتقدمة.
