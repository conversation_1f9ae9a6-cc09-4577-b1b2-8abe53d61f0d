import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../services/asset_optimization_service.dart';

class OptimizedImageWidget extends StatefulWidget {
  final String? imageUrl;
  final String? assetPath;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final int quality;
  final bool enableMemoryCache;
  final bool enableDiskCache;

  const OptimizedImageWidget({
    super.key,
    this.imageUrl,
    this.assetPath,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.quality = 85,
    this.enableMemoryCache = true,
    this.enableDiskCache = true,
  }) : assert(imageUrl != null || assetPath != null, 'Either imageUrl or assetPath must be provided');

  @override
  State<OptimizedImageWidget> createState() => _OptimizedImageWidgetState();
}

class _OptimizedImageWidgetState extends State<OptimizedImageWidget> {
  Uint8List? _optimizedImageData;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  @override
  void didUpdateWidget(OptimizedImageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imageUrl != widget.imageUrl || 
        oldWidget.assetPath != widget.assetPath ||
        oldWidget.quality != widget.quality) {
      _loadImage();
    }
  }

  Future<void> _loadImage() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _optimizedImageData = null;
    });

    try {
      Uint8List? imageData;

      if (widget.assetPath != null) {
        // Load optimized asset image
        imageData = await AssetOptimizationService.loadOptimizedImage(
          widget.assetPath!,
          maxWidth: widget.width?.toInt(),
          maxHeight: widget.height?.toInt(),
          quality: widget.quality,
        );
      } else if (widget.imageUrl != null) {
        // For network images, we'll use CachedNetworkImage
        // This widget is mainly for asset optimization
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
        return;
      }

      if (mounted) {
        setState(() {
          _optimizedImageData = imageData;
          _isLoading = false;
          _hasError = imageData == null;
        });
      }
    } catch (e) {
      debugPrint('Error loading optimized image: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // For network images, use CachedNetworkImage
    if (widget.imageUrl != null) {
      return CachedNetworkImage(
        imageUrl: widget.imageUrl!,
        width: widget.width,
        height: widget.height,
        fit: widget.fit,
        memCacheWidth: widget.width?.toInt(),
        memCacheHeight: widget.height?.toInt(),
        maxWidthDiskCache: widget.width?.toInt(),
        maxHeightDiskCache: widget.height?.toInt(),
        placeholder: (context, url) => _buildPlaceholder(),
        errorWidget: (context, url, error) => _buildErrorWidget(),
        fadeInDuration: const Duration(milliseconds: 200),
        fadeOutDuration: const Duration(milliseconds: 200),
      );
    }

    // For asset images, use optimized loading
    if (_isLoading) {
      return _buildPlaceholder();
    }

    if (_hasError || _optimizedImageData == null) {
      return _buildErrorWidget();
    }

    return Image.memory(
      _optimizedImageData!,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      gaplessPlayback: true,
      filterQuality: FilterQuality.medium,
    );
  }

  Widget _buildPlaceholder() {
    if (widget.placeholder != null) {
      return widget.placeholder!;
    }

    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    if (widget.errorWidget != null) {
      return widget.errorWidget!;
    }

    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Icon(
          Icons.error_outline,
          color: Colors.grey,
          size: 32,
        ),
      ),
    );
  }
}

// Optimized Avatar Widget
class OptimizedAvatarWidget extends StatelessWidget {
  final String? imageUrl;
  final String? assetPath;
  final double radius;
  final Widget? placeholder;
  final Widget? errorWidget;

  const OptimizedAvatarWidget({
    super.key,
    this.imageUrl,
    this.assetPath,
    this.radius = 20,
    this.placeholder,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    return ClipOval(
      child: OptimizedImageWidget(
        imageUrl: imageUrl,
        assetPath: assetPath,
        width: radius * 2,
        height: radius * 2,
        fit: BoxFit.cover,
        quality: 90, // Higher quality for avatars
        placeholder: placeholder ?? _buildDefaultPlaceholder(),
        errorWidget: errorWidget ?? _buildDefaultErrorWidget(),
      ),
    );
  }

  Widget _buildDefaultPlaceholder() {
    return Container(
      width: radius * 2,
      height: radius * 2,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        shape: BoxShape.circle,
      ),
      child: const Icon(
        Icons.person,
        color: Colors.grey,
      ),
    );
  }

  Widget _buildDefaultErrorWidget() {
    return Container(
      width: radius * 2,
      height: radius * 2,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        shape: BoxShape.circle,
      ),
      child: const Icon(
        Icons.person,
        color: Colors.grey,
      ),
    );
  }
}

// Optimized Icon Widget for better performance
class OptimizedIconWidget extends StatelessWidget {
  final IconData icon;
  final double? size;
  final Color? color;
  final String? semanticLabel;

  const OptimizedIconWidget(
    this.icon, {
    super.key,
    this.size,
    this.color,
    this.semanticLabel,
  });

  @override
  Widget build(BuildContext context) {
    return Icon(
      icon,
      size: size,
      color: color,
      semanticLabel: semanticLabel,
      // Use medium quality for better performance
      textDirection: TextDirection.ltr,
    );
  }
}

// Optimized List Tile with image
class OptimizedListTile extends StatelessWidget {
  final String? imageUrl;
  final String? assetPath;
  final String title;
  final String? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final double imageSize;

  const OptimizedListTile({
    super.key,
    this.imageUrl,
    this.assetPath,
    required this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.imageSize = 56,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: imageUrl != null || assetPath != null
          ? OptimizedImageWidget(
              imageUrl: imageUrl,
              assetPath: assetPath,
              width: imageSize,
              height: imageSize,
              fit: BoxFit.cover,
              quality: 75, // Lower quality for list items
            )
          : null,
      title: Text(
        title,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle!,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            )
          : null,
      trailing: trailing,
      onTap: onTap,
      dense: true, // More compact for better performance
    );
  }
}
