package com.mohamedrady.hmdeviceerrors

import android.content.Context
import android.content.pm.PackageManager
import android.content.pm.Signature
import android.os.Build
import android.provider.Settings
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException

class SecurityPlugin: FlutterPlugin, MethodCallHandler {
    private lateinit var channel: MethodChannel
    private lateinit var context: Context

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "com.mohamedrady.hmdeviceerrors/security")
        channel.setMethodCallHandler(this)
        context = flutterPluginBinding.applicationContext
    }

    override fun onMethodCall(call: Method<PERSON><PERSON>, result: Result) {
        when (call.method) {
            "getSignature" -> {
                try {
                    val signature = getAppSignature()
                    result.success(signature)
                } catch (e: Exception) {
                    result.error("SIGNATURE_ERROR", "Failed to get app signature", e.message)
                }
            }
            "isEmulator" -> {
                try {
                    val isEmulator = isRunningOnEmulator()
                    result.success(isEmulator)
                } catch (e: Exception) {
                    result.error("EMULATOR_CHECK_ERROR", "Failed to check emulator", e.message)
                }
            }
            "isRooted" -> {
                try {
                    val isRooted = isDeviceRooted()
                    result.success(isRooted)
                } catch (e: Exception) {
                    result.error("ROOT_CHECK_ERROR", "Failed to check root", e.message)
                }
            }
            "getDeviceId" -> {
                try {
                    val deviceId = getDeviceId()
                    result.success(deviceId)
                } catch (e: Exception) {
                    result.error("DEVICE_ID_ERROR", "Failed to get device ID", e.message)
                }
            }
            "getSecurityInfo" -> {
                try {
                    val securityInfo = getSecurityInfo()
                    result.success(securityInfo)
                } catch (e: Exception) {
                    result.error("SECURITY_INFO_ERROR", "Failed to get security info", e.message)
                }
            }
            "getSHA1Fingerprint" -> {
                try {
                    val sha1 = getSHA1Fingerprint()
                    result.success(sha1)
                } catch (e: Exception) {
                    result.error("SHA1_ERROR", "Failed to get SHA1 fingerprint", e.message)
                }
            }
            "getSHA256Fingerprint" -> {
                try {
                    val sha256 = getSHA256Fingerprint()
                    result.success(sha256)
                } catch (e: Exception) {
                    result.error("SHA256_ERROR", "Failed to get SHA256 fingerprint", e.message)
                }
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
    }

    private fun getAppSignature(): String {
        try {
            val packageInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                context.packageManager.getPackageInfo(
                    context.packageName,
                    PackageManager.GET_SIGNING_CERTIFICATES
                )
            } else {
                @Suppress("DEPRECATION")
                context.packageManager.getPackageInfo(
                    context.packageName,
                    PackageManager.GET_SIGNATURES
                )
            }

            val signatures = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                packageInfo.signingInfo?.apkContentsSigners
            } else {
                @Suppress("DEPRECATION")
                packageInfo.signatures
            }

            if (signatures != null && signatures.isNotEmpty()) {
                val signature = signatures[0]
                return getSignatureHash(signature)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return "unknown_signature"
    }

    private fun getSignatureHash(signature: Signature): String {
        try {
            val md = MessageDigest.getInstance("SHA-256")
            md.update(signature.toByteArray())
            val digest = md.digest()
            return bytesToHex(digest)
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
        }
        return "hash_error"
    }

    private fun bytesToHex(bytes: ByteArray): String {
        val hexArray = "0123456789ABCDEF".toCharArray()
        val hexChars = CharArray(bytes.size * 2)
        for (j in bytes.indices) {
            val v = bytes[j].toInt() and 0xFF
            hexChars[j * 2] = hexArray[v ushr 4]
            hexChars[j * 2 + 1] = hexArray[v and 0x0F]
        }
        return String(hexChars)
    }

    private fun isRunningOnEmulator(): Boolean {
        return (Build.FINGERPRINT.startsWith("generic")
                || Build.FINGERPRINT.startsWith("unknown")
                || Build.MODEL.contains("google_sdk")
                || Build.MODEL.contains("Emulator")
                || Build.MODEL.contains("Android SDK built for x86")
                || Build.MANUFACTURER.contains("Genymotion")
                || Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic")
                || "google_sdk" == Build.PRODUCT
                || Build.HARDWARE.contains("goldfish")
                || Build.HARDWARE.contains("ranchu"))
    }

    private fun isDeviceRooted(): Boolean {
        return checkRootMethod1() || checkRootMethod2() || checkRootMethod3()
    }

    private fun checkRootMethod1(): Boolean {
        val buildTags = Build.TAGS
        return buildTags != null && buildTags.contains("test-keys")
    }

    private fun checkRootMethod2(): Boolean {
        val paths = arrayOf(
            "/system/app/Superuser.apk",
            "/sbin/su",
            "/system/bin/su",
            "/system/xbin/su",
            "/data/local/xbin/su",
            "/data/local/bin/su",
            "/system/sd/xbin/su",
            "/system/bin/failsafe/su",
            "/data/local/su",
            "/su/bin/su"
        )
        for (path in paths) {
            if (java.io.File(path).exists()) return true
        }
        return false
    }

    private fun checkRootMethod3(): Boolean {
        var process: Process? = null
        return try {
            process = Runtime.getRuntime().exec(arrayOf("/system/xbin/which", "su"))
            val bufferedReader = java.io.BufferedReader(java.io.InputStreamReader(process.inputStream))
            bufferedReader.readLine() != null
        } catch (t: Throwable) {
            false
        } finally {
            process?.destroy()
        }
    }

    private fun getDeviceId(): String {
        return try {
            Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
        } catch (e: Exception) {
            "unknown_device_id"
        }
    }

    private fun getSHA1Fingerprint(): String {
        try {
            val signature = getFirstSignature()
            if (signature != null) {
                val md = MessageDigest.getInstance("SHA-1")
                md.update(signature.toByteArray())
                val digest = md.digest()
                return formatFingerprint(bytesToHex(digest))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return "SHA1:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00"
    }

    private fun getSHA256Fingerprint(): String {
        try {
            val signature = getFirstSignature()
            if (signature != null) {
                val md = MessageDigest.getInstance("SHA-256")
                md.update(signature.toByteArray())
                val digest = md.digest()
                return formatFingerprint(bytesToHex(digest))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return "SHA256:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00"
    }

    private fun getFirstSignature(): Signature? {
        try {
            val packageInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                context.packageManager.getPackageInfo(
                    context.packageName,
                    PackageManager.GET_SIGNING_CERTIFICATES
                )
            } else {
                @Suppress("DEPRECATION")
                context.packageManager.getPackageInfo(
                    context.packageName,
                    PackageManager.GET_SIGNATURES
                )
            }

            val signatures = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                packageInfo.signingInfo?.apkContentsSigners
            } else {
                @Suppress("DEPRECATION")
                packageInfo.signatures
            }

            return if (signatures != null && signatures.isNotEmpty()) {
                signatures[0]
            } else null
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }

    private fun formatFingerprint(hex: String): String {
        val sb = StringBuilder()
        for (i in hex.indices step 2) {
            if (i > 0) sb.append(":")
            sb.append(hex.substring(i, minOf(i + 2, hex.length)))
        }
        return sb.toString().uppercase()
    }

    private fun getSecurityInfo(): Map<String, Any> {
        return mapOf(
            "signature" to getAppSignature(),
            "sha1Fingerprint" to getSHA1Fingerprint(),
            "sha256Fingerprint" to getSHA256Fingerprint(),
            "isEmulator" to isRunningOnEmulator(),
            "isRooted" to isDeviceRooted(),
            "deviceId" to getDeviceId(),
            "buildFingerprint" to Build.FINGERPRINT,
            "buildTags" to (Build.TAGS ?: ""),
            "manufacturer" to Build.MANUFACTURER,
            "model" to Build.MODEL,
            "brand" to Build.BRAND,
            "device" to Build.DEVICE,
            "hardware" to Build.HARDWARE,
            "product" to Build.PRODUCT,
            "androidVersion" to Build.VERSION.RELEASE,
            "sdkVersion" to Build.VERSION.SDK_INT,
            "timestamp" to System.currentTimeMillis()
        )
    }
}
