# 🔧 **دليل تفعيل Google Drive عبر Firebase Remote Config**

## 📋 **نظرة عامة**

هذا الدليل يوضح كيفية تفعيل Google Drive في التطبيق باستخدام Firebase Remote Config، مما يتيح لك تحديث إعدادات Google Drive بدون إعادة نشر التطبيق.

## 🎯 **الفوائد**

- ✅ **تحديث فوري**: تغيير الإعدادات بدون إعادة نشر
- ✅ **أمان عالي**: المفاتيح محفوظة في Firebase
- ✅ **مرونة**: تفعيل/تعطيل Google Drive حسب الحاجة
- ✅ **إدارة مركزية**: جميع الإعدادات في مكان واحد

## 🔧 **الخطوات التفصيلية**

### **1. إعداد Google Cloud Console**

#### **أ) إنشاء مشروع جديد أو استخدام موجود:**
1. انتقل إلى [Google Cloud Console](https://console.cloud.google.com/)
2. اختر مشروعك أو أنشئ مشروع جديد
3. تأكد من أن المشروع مفعل ومرتبط بحساب فوترة

#### **ب) تفعيل Google Drive API:**
1. في القائمة الجانبية، انتقل إلى **APIs & Services** → **Library**
2. ابحث عن **"Google Drive API"**
3. اضغط على النتيجة الأولى
4. اضغط **"Enable"**
5. انتظر حتى يتم التفعيل (قد يستغرق دقيقة)

#### **ج) إنشاء Service Account:**
1. انتقل إلى **IAM & Admin** → **Service Accounts**
2. اضغط **"Create Service Account"**
3. املأ التفاصيل:
   ```
   Service account name: HM Device Errors Service
   Service account ID: hm-device-errors-service
   Description: Service account for HM Device Errors app Google Drive integration
   ```
4. اضغط **"Create and Continue"**

#### **د) منح الأذونات:**
1. في قسم **"Grant this service account access to project"**
2. أضف الأدوار التالية:
   - **Storage Admin** (للوصول الكامل للتخزين)
   - **Service Account User** (للاستخدام الآمن)
3. اضغط **"Continue"**
4. اضغط **"Done"**

#### **هـ) إنشاء وتحميل المفتاح:**
1. في قائمة Service Accounts، اضغط على الحساب الذي أنشأته
2. انتقل إلى تبويب **"Keys"**
3. اضغط **"Add Key"** → **"Create new key"**
4. اختر **JSON** format
5. اضغط **"Create"**
6. سيتم تحميل ملف JSON - احفظه في مكان آمن

### **2. إعداد Google Drive**

#### **أ) إنشاء مجلد للنسخ الاحتياطية:**
1. انتقل إلى [Google Drive](https://drive.google.com/)
2. اضغط **"New"** → **"Folder"**
3. اسم المجلد: **"HM Device Errors Backups"**
4. اضغط **"Create"**

#### **ب) مشاركة المجلد مع Service Account:**
1. انقر بالزر الأيمن على المجلد
2. اختر **"Share"**
3. في حقل **"Add people and groups"**
4. أدخل **client_email** من ملف JSON (مثل: `<EMAIL>`)
5. اختر **"Editor"** permissions
6. اضغط **"Send"**

#### **ج) الحصول على Folder ID:**
1. افتح المجلد في Google Drive
2. انسخ الرابط من شريط العناوين
3. الـ Folder ID هو الجزء بعد `/folders/`
   ```
   مثال: https://drive.google.com/drive/folders/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74mHZjTuDLY
   Folder ID: 1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74mHZjTuDLY
   ```

### **3. تكوين Firebase Remote Config**

#### **أ) الانتقال إلى Firebase Console:**
1. انتقل إلى [Firebase Console](https://console.firebase.google.com/)
2. اختر مشروعك
3. في القائمة الجانبية، انتقل إلى **Remote Config**

#### **ب) إضافة المفاتيح الأساسية:**

**المفاتيح الإجبارية:**
```json
{
  "google_drive_enabled": true,
  "google_drive_client_id": "من ملف JSON",
  "google_drive_client_secret": "من ملف JSON", 
  "google_drive_project_id": "من ملف JSON",
  "google_drive_private_key_id": "من ملف JSON",
  "google_drive_private_key": "من ملف JSON (مع الحفاظ على \\n)",
  "google_drive_client_email": "من ملف JSON",
  "google_drive_parent_folder_id": "معرف المجلد من الخطوة السابقة"
}
```

**المفاتيح الاختيارية (مع القيم الافتراضية):**
```json
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
```

#### **ج) خطوات إضافة كل مفتاح:**
1. اضغط **"Add parameter"**
2. أدخل اسم المفتاح (مثل: `google_drive_enabled`)
3. أدخل القيمة
4. اضغط **"Save"**
5. كرر للمفاتيح الأخرى

#### **د) نشر التكوين:**
1. بعد إضافة جميع المفاتيح
2. اضغط **"Publish changes"**
3. أضف وصف للتغيير: "Enable Google Drive integration"
4. اضغط **"Publish"**

## 📱 **اختبار التكوين في التطبيق**

### **1. الوصول لصفحة الإعداد:**
```
Admin Panel → Google Drive Setup
```

### **2. التحقق من الحالة:**
- ✅ **Enabled**: يجب أن يكون `true`
- ✅ **Remote Config Primary**: يجب أن يكون `true`
- ✅ **Configuration Complete**: يجب أن يكون `true`

### **3. اختبار الاتصال:**
1. اضغط **"Test Connection"**
2. يجب أن تظهر رسالة نجاح

## 🔍 **استكشاف الأخطاء**

### **مشاكل شائعة وحلولها:**

#### **1. "Google Drive is disabled via remote config"**
**الحل:**
- تأكد من أن `google_drive_enabled = true` في Firebase Remote Config
- اضغط "Refresh Configuration" في التطبيق

#### **2. "Configuration incomplete"**
**الحل:**
- تحقق من وجود جميع المفاتيح المطلوبة
- تأكد من صحة القيم المدخلة

#### **3. "HandshakeException: Connection terminated"**
**الحل:**
- تحقق من اتصال الإنترنت
- تأكد من صحة `private_key` (يجب أن يحتوي على `\\n`)

#### **4. "Access denied"**
**الحل:**
- تأكد من مشاركة المجلد مع Service Account
- تحقق من صحة `client_email`

## 📋 **قائمة التحقق النهائية**

### **Google Cloud Console:**
- ✅ Google Drive API مفعل
- ✅ Service Account منشأ
- ✅ JSON key file محمل
- ✅ الأذونات صحيحة

### **Google Drive:**
- ✅ مجلد منشأ
- ✅ مشارك مع Service Account
- ✅ Folder ID محفوظ

### **Firebase Remote Config:**
- ✅ جميع المفاتيح مضافة
- ✅ القيم صحيحة
- ✅ التكوين منشور

### **التطبيق:**
- ✅ Remote Config محدث
- ✅ Google Drive مفعل
- ✅ اختبار الاتصال ناجح

## 🎯 **الخطوات التالية**

بعد إكمال الإعداد:

1. **اختبر النسخ الاحتياطي:**
   - انتقل إلى إعدادات التطبيق
   - جرب إنشاء نسخة احتياطية
   - تحقق من ظهور الملف في Google Drive

2. **راقب الأداء:**
   - تابع logs التطبيق
   - تحقق من عدم وجود أخطاء

3. **أمان إضافي:**
   - راجع أذونات Service Account دورياً
   - احتفظ بنسخة آمنة من ملف JSON

## 🔒 **ملاحظات أمنية**

- ⚠️ **لا تشارك ملف JSON** مع أي شخص
- ⚠️ **احتفظ بنسخة احتياطية** من ملف JSON في مكان آمن
- ⚠️ **راجع الأذونات** دورياً وأزل ما لا تحتاجه
- ⚠️ **استخدم مجلدات منفصلة** لبيئات مختلفة (تطوير/إنتاج)

## 🎉 **تهانينا!**

إذا اتبعت جميع الخطوات بنجاح، فإن Google Drive الآن مفعل ويعمل مع التطبيق عبر Firebase Remote Config!

يمكنك الآن:
- إنشاء نسخ احتياطية تلقائية
- تحديث الإعدادات بدون إعادة نشر
- إدارة التكوين مركزياً من Firebase
