import 'package:cloud_firestore/cloud_firestore.dart';

class UserSettings {
  final String userId;
  final String language; // 'ar' or 'en'
  final String theme; // 'light', 'dark', or 'system'
  final bool rememberMe;
  final String defaultStorageService; // 'google_drive' or 'onedrive'
  final bool enableNotifications;
  final bool autoBackup;
  // Cache settings
  final bool enableCache;
  final int maxCacheSizeMB;
  final bool autoCleanupCache;
  final int cacheRetentionDays;
  final bool cacheImages;
  final bool cacheVideos;
  final bool cacheDocuments;
  final DateTime lastUpdated;

  UserSettings({
    required this.userId,
    this.language = 'ar',
    this.theme = 'system',
    this.rememberMe = false,
    this.defaultStorageService = 'google_drive',
    this.enableNotifications = true,
    this.autoBackup = true,
    // Cache settings defaults
    this.enableCache = true,
    this.maxCacheSizeMB = 500,
    this.autoCleanupCache = true,
    this.cacheRetentionDays = 30,
    this.cacheImages = true,
    this.cacheVideos = true,
    this.cacheDocuments = true,
    required this.lastUpdated,
  });

  // Convert to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'language': language,
      'theme': theme,
      'rememberMe': rememberMe,
      'defaultStorageService': defaultStorageService,
      'enableNotifications': enableNotifications,
      'autoBackup': autoBackup,
      // Cache settings
      'enableCache': enableCache,
      'maxCacheSizeMB': maxCacheSizeMB,
      'autoCleanupCache': autoCleanupCache,
      'cacheRetentionDays': cacheRetentionDays,
      'cacheImages': cacheImages,
      'cacheVideos': cacheVideos,
      'cacheDocuments': cacheDocuments,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
    };
  }

  // Create from Firestore document
  factory UserSettings.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserSettings(
      userId: data['userId'] ?? '',
      language: data['language'] ?? 'ar',
      theme: data['theme'] ?? 'system',
      rememberMe: data['rememberMe'] ?? false,
      defaultStorageService: data['defaultStorageService'] ?? 'google_drive',
      enableNotifications: data['enableNotifications'] ?? true,
      autoBackup: data['autoBackup'] ?? true,
      // Cache settings
      enableCache: data['enableCache'] ?? true,
      maxCacheSizeMB: data['maxCacheSizeMB'] ?? 500,
      autoCleanupCache: data['autoCleanupCache'] ?? true,
      cacheRetentionDays: data['cacheRetentionDays'] ?? 30,
      cacheImages: data['cacheImages'] ?? true,
      cacheVideos: data['cacheVideos'] ?? true,
      cacheDocuments: data['cacheDocuments'] ?? true,
      lastUpdated: (data['lastUpdated'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  // Create from Map
  factory UserSettings.fromMap(Map<String, dynamic> map) {
    return UserSettings(
      userId: map['userId'] ?? '',
      language: map['language'] ?? 'ar',
      theme: map['theme'] ?? 'system',
      rememberMe: map['rememberMe'] ?? false,
      defaultStorageService: map['defaultStorageService'] ?? 'google_drive',
      enableNotifications: map['enableNotifications'] ?? true,
      autoBackup: map['autoBackup'] ?? true,
      // Cache settings
      enableCache: map['enableCache'] ?? true,
      maxCacheSizeMB: map['maxCacheSizeMB'] ?? 500,
      autoCleanupCache: map['autoCleanupCache'] ?? true,
      cacheRetentionDays: map['cacheRetentionDays'] ?? 30,
      cacheImages: map['cacheImages'] ?? true,
      cacheVideos: map['cacheVideos'] ?? true,
      cacheDocuments: map['cacheDocuments'] ?? true,
      lastUpdated: map['lastUpdated'] is Timestamp
          ? (map['lastUpdated'] as Timestamp).toDate()
          : DateTime.now(),
    );
  }

  // Copy with new values
  UserSettings copyWith({
    String? userId,
    String? language,
    String? theme,
    bool? rememberMe,
    String? defaultStorageService,
    bool? enableNotifications,
    bool? autoBackup,
    // Cache settings
    bool? enableCache,
    int? maxCacheSizeMB,
    bool? autoCleanupCache,
    int? cacheRetentionDays,
    bool? cacheImages,
    bool? cacheVideos,
    bool? cacheDocuments,
    DateTime? lastUpdated,
  }) {
    return UserSettings(
      userId: userId ?? this.userId,
      language: language ?? this.language,
      theme: theme ?? this.theme,
      rememberMe: rememberMe ?? this.rememberMe,
      defaultStorageService: defaultStorageService ?? this.defaultStorageService,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      autoBackup: autoBackup ?? this.autoBackup,
      // Cache settings
      enableCache: enableCache ?? this.enableCache,
      maxCacheSizeMB: maxCacheSizeMB ?? this.maxCacheSizeMB,
      autoCleanupCache: autoCleanupCache ?? this.autoCleanupCache,
      cacheRetentionDays: cacheRetentionDays ?? this.cacheRetentionDays,
      cacheImages: cacheImages ?? this.cacheImages,
      cacheVideos: cacheVideos ?? this.cacheVideos,
      cacheDocuments: cacheDocuments ?? this.cacheDocuments,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  @override
  String toString() {
    return 'UserSettings(userId: $userId, language: $language, theme: $theme, rememberMe: $rememberMe, defaultStorageService: $defaultStorageService, enableNotifications: $enableNotifications, autoBackup: $autoBackup, lastUpdated: $lastUpdated)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserSettings &&
        other.userId == userId &&
        other.language == language &&
        other.theme == theme &&
        other.rememberMe == rememberMe &&
        other.defaultStorageService == defaultStorageService &&
        other.enableNotifications == enableNotifications &&
        other.autoBackup == autoBackup;
  }

  @override
  int get hashCode {
    return userId.hashCode ^
        language.hashCode ^
        theme.hashCode ^
        rememberMe.hashCode ^
        defaultStorageService.hashCode ^
        enableNotifications.hashCode ^
        autoBackup.hashCode;
  }
}