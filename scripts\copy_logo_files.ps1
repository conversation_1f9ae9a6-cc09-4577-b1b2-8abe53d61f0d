# PowerShell script to copy logo files from Downloads folder
param(
    [string]$SourcePath = "C:\Users\<USER>\Downloads\ic_launcher\res"
)

Write-Host "Copying logo files from Downloads folder..." -ForegroundColor Green
Write-Host ""

$ProjectDir = Split-Path -Parent $PSScriptRoot
$SourceDir = $SourcePath

# Check if source directory exists
if (-not (Test-Path $SourceDir)) {
    Write-Host "Error: Source directory not found: $SourceDir" -ForegroundColor Red
    Write-Host "Please make sure the ic_launcher folder exists in your Downloads." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Source directory: $SourceDir" -ForegroundColor Cyan
Write-Host "Project directory: $ProjectDir" -ForegroundColor Cyan
Write-Host ""

# List files in source directory
Write-Host "Files and folders in source directory:" -ForegroundColor Yellow
Get-ChildItem $SourceDir | Format-Table Name, Length, LastWriteTime -AutoSize
Write-Host ""

# Function to copy files safely
function Copy-FilesIfExists {
    param($Source, $Destination, $Description)
    
    if (Test-Path $Source) {
        # Create destination directory if it doesn't exist
        $DestDir = Split-Path $Destination -Parent
        if (-not (Test-Path $DestDir)) {
            New-Item -ItemType Directory -Path $DestDir -Force | Out-Null
        }
        
        if (Test-Path $Source -PathType Container) {
            # Copy directory contents
            Copy-Item "$Source\*" $Destination -Recurse -Force
        } else {
            # Copy single file
            Copy-Item $Source $Destination -Force
        }
        Write-Host "✅ $Description" -ForegroundColor Green
        return $true
    } else {
        Write-Host "⚠️  $Description - Source not found: $Source" -ForegroundColor Yellow
        return $false
    }
}

# Copy Android launcher icons
Write-Host "Copying Android launcher icons..." -ForegroundColor Cyan

$AndroidResPath = "$ProjectDir\android\app\src\main\res"
$CopiedAny = $false

# Check for mipmap folders (including adaptive icons)
$MipmapFolders = @("mipmap-mdpi", "mipmap-hdpi", "mipmap-xhdpi", "mipmap-xxhdpi", "mipmap-xxxhdpi", "mipmap-anydpi-v26")
foreach ($folder in $MipmapFolders) {
    $result = Copy-FilesIfExists "$SourceDir\$folder" "$AndroidResPath\$folder" "Copied $folder"
    $CopiedAny = $CopiedAny -or $result
}

# Check for individual PNG files in root
$PngFiles = Get-ChildItem "$SourceDir\*.png" -ErrorAction SilentlyContinue
if ($PngFiles) {
    Write-Host "Found PNG files in root directory. Copying to mipmap-xxxhdpi..." -ForegroundColor Yellow
    foreach ($file in $PngFiles) {
        Copy-FilesIfExists $file.FullName "$AndroidResPath\mipmap-xxxhdpi\$($file.Name)" "Copied $($file.Name) to mipmap-xxxhdpi"
        $CopiedAny = $true
    }
}

# Copy drawable files
Write-Host ""
Write-Host "Copying drawable files..." -ForegroundColor Cyan
Copy-FilesIfExists "$SourceDir\drawable" "$AndroidResPath\drawable" "Copied drawable files"

# Check for XML files in root
$XmlFiles = Get-ChildItem "$SourceDir\*.xml" -ErrorAction SilentlyContinue
if ($XmlFiles) {
    foreach ($file in $XmlFiles) {
        Copy-FilesIfExists $file.FullName "$AndroidResPath\drawable\$($file.Name)" "Copied $($file.Name) to drawable"
    }
}

# Copy iOS icons
Write-Host ""
Write-Host "Copying iOS icons..." -ForegroundColor Cyan
Copy-FilesIfExists "$SourceDir\ios" "$ProjectDir\ios\Runner\Assets.xcassets\AppIcon.appiconset" "Copied iOS icons"

# Copy web icons
Write-Host ""
Write-Host "Copying web icons..." -ForegroundColor Cyan
Copy-FilesIfExists "$SourceDir\web" "$ProjectDir\web\icons" "Copied web icons"
Copy-FilesIfExists "$SourceDir\favicon.png" "$ProjectDir\web\favicon.png" "Copied favicon"

# Copy macOS icons
Write-Host ""
Write-Host "Copying macOS icons..." -ForegroundColor Cyan
Copy-FilesIfExists "$SourceDir\macos" "$ProjectDir\macos\Runner\Assets.xcassets\AppIcon.appiconset" "Copied macOS icons"

# Check for any remaining image files
Write-Host ""
Write-Host "Checking for other image files..." -ForegroundColor Cyan
$ImageFiles = Get-ChildItem "$SourceDir\*" -Include "*.png", "*.jpg", "*.jpeg", "*.svg" -ErrorAction SilentlyContinue
if ($ImageFiles) {
    Write-Host "Found additional image files:" -ForegroundColor Yellow
    foreach ($file in $ImageFiles) {
        Write-Host "  - $($file.Name)" -ForegroundColor White
    }
}

Write-Host ""
Write-Host "✅ Logo files processing completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Clean and rebuild your Flutter project" -ForegroundColor White
Write-Host "2. Test the app to verify new icons appear correctly" -ForegroundColor White
Write-Host ""
Write-Host "Commands to run:" -ForegroundColor Yellow
Write-Host "  flutter clean" -ForegroundColor White
Write-Host "  flutter pub get" -ForegroundColor White
Write-Host "  flutter build apk" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to exit"
