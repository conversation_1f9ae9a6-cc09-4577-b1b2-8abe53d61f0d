import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../providers/locale_provider.dart';
import '../../providers/auth_provider.dart';
import '../../services/firebase_fingerprint_service.dart';

class FirebaseFingerprintScreen extends StatefulWidget {
  const FirebaseFingerprintScreen({super.key});

  @override
  State<FirebaseFingerprintScreen> createState() => _FirebaseFingerprintScreenState();
}

class _FirebaseFingerprintScreenState extends State<FirebaseFingerprintScreen> {
  Map<String, dynamic>? _fingerprintInfo;
  bool _isLoading = true;
  String? _keytoolCommand;

  @override
  void initState() {
    super.initState();
    _loadFingerprintInfo();
  }

  Future<void> _loadFingerprintInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final info = await FirebaseFingerprintService.getFirebaseFingerprintInfo();
      final command = await FirebaseFingerprintService.generateKeytoolCommand();

      setState(() {
        _fingerprintInfo = info;
        _keytoolCommand = command;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل معلومات Firebase: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final isRTL = localeProvider.isRTL;

    if (!authProvider.isAdmin) {
      return Scaffold(
        appBar: AppBar(
          title: Text(
            isRTL ? 'غير مصرح' : 'Unauthorized',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
          elevation: 4,
          iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
          centerTitle: true,
        ),
        body: Center(
          child: Text(
            isRTL ? 'ليس لديك صلاحية للوصول لهذه الصفحة' : 'You don\'t have permission to access this page',
            style: const TextStyle(fontSize: 18),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isRTL ? 'Firebase Fingerprint' : 'Firebase Fingerprint',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadFingerprintInfo,
            tooltip: isRTL ? 'تحديث' : 'Refresh',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'copy_sha1',
                child: Row(
                  children: [
                    const Icon(Icons.copy),
                    const SizedBox(width: 8),
                    Text(isRTL ? 'نسخ SHA-1' : 'Copy SHA-1'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'copy_sha256',
                child: Row(
                  children: [
                    const Icon(Icons.copy),
                    const SizedBox(width: 8),
                    Text(isRTL ? 'نسخ SHA-256' : 'Copy SHA-256'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'copy_all',
                child: Row(
                  children: [
                    const Icon(Icons.content_copy),
                    const SizedBox(width: 8),
                    Text(isRTL ? 'نسخ الكل' : 'Copy All'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'open_console',
                child: Row(
                  children: [
                    const Icon(Icons.open_in_browser),
                    const SizedBox(width: 8),
                    Text(isRTL ? 'فتح Firebase Console' : 'Open Firebase Console'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Instructions Card
                  _buildInstructionsCard(isRTL),

                  const SizedBox(height: 16),

                  // Fingerprint Info Card
                  _buildFingerprintCard(isRTL),

                  const SizedBox(height: 16),

                  // Keytool Command Card
                  _buildKeytoolCard(isRTL),

                  const SizedBox(height: 16),

                  // Quick Actions Card
                  _buildQuickActionsCard(isRTL),
                ],
              ),
            ),
    );
  }

  Widget _buildInstructionsCard(bool isRTL) {
    return Card(
      color: Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Colors.blue.shade700),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'تعليمات Firebase' : 'Firebase Instructions',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              isRTL
                ? 'لإضافة التطبيق إلى Firebase Console:'
                : 'To add your app to Firebase Console:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              isRTL
                ? '1. افتح Firebase Console\n2. اختر مشروعك\n3. اذهب إلى إعدادات المشروع\n4. أضف التطبيق\n5. أدخل package name و SHA fingerprints'
                : '1. Open Firebase Console\n2. Select your project\n3. Go to Project Settings\n4. Add App\n5. Enter package name and SHA fingerprints',
              style: const TextStyle(height: 1.5),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFingerprintCard(bool isRTL) {
    if (_fingerprintInfo == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.fingerprint, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'معلومات Firebase' : 'Firebase Information',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildInfoRow(
              isRTL ? 'معرف المشروع' : 'Project ID',
              _fingerprintInfo!['project_id'] ?? 'N/A',
              copyable: true,
            ),

            _buildInfoRow(
              isRTL ? 'اسم الحزمة' : 'Package Name',
              _fingerprintInfo!['package_name'] ?? 'N/A',
              copyable: true,
            ),

            _buildInfoRow(
              isRTL ? 'اسم التطبيق' : 'App Name',
              _fingerprintInfo!['app_name'] ?? 'N/A',
            ),

            _buildInfoRow(
              isRTL ? 'المنصة' : 'Platform',
              _fingerprintInfo!['platform'] ?? 'N/A',
            ),

            const Divider(height: 24),

            Text(
              isRTL ? 'بصمات SHA' : 'SHA Fingerprints',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),

            _buildFingerprintRow(
              'SHA-1',
              _fingerprintInfo!['sha1_fingerprint'] ?? 'N/A',
              Colors.orange,
            ),

            _buildFingerprintRow(
              'SHA-256',
              _fingerprintInfo!['sha256_fingerprint'] ?? 'N/A',
              Colors.purple,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildKeytoolCard(bool isRTL) {
    if (_keytoolCommand == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.terminal, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'أوامر Keytool' : 'Keytool Commands',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ],
            ),
            const SizedBox(height: 16),

            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        isRTL ? 'أوامر Terminal' : 'Terminal Commands',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      IconButton(
                        icon: const Icon(Icons.copy, size: 20),
                        onPressed: () => _copyToClipboard(_keytoolCommand!),
                        tooltip: isRTL ? 'نسخ' : 'Copy',
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _keytoolCommand!,
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsCard(bool isRTL) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.flash_on, color: Colors.purple),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'إجراءات سريعة' : 'Quick Actions',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ],
            ),
            const SizedBox(height: 16),

            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: () => _handleMenuAction('copy_sha1'),
                  icon: const Icon(Icons.copy),
                  label: Text(isRTL ? 'نسخ SHA-1' : 'Copy SHA-1'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _handleMenuAction('copy_sha256'),
                  icon: const Icon(Icons.copy),
                  label: Text(isRTL ? 'نسخ SHA-256' : 'Copy SHA-256'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _handleMenuAction('copy_all'),
                  icon: const Icon(Icons.content_copy),
                  label: Text(isRTL ? 'نسخ الكل' : 'Copy All'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _handleMenuAction('open_console'),
                  icon: const Icon(Icons.open_in_browser),
                  label: Text(isRTL ? 'Firebase Console' : 'Firebase Console'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {bool copyable = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          if (copyable)
            IconButton(
              icon: const Icon(Icons.copy, size: 16),
              onPressed: () => _copyToClipboard(value),
              tooltip: 'نسخ',
            ),
        ],
      ),
    );
  }

  Widget _buildFingerprintRow(String type, String fingerprint, Color color) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                type,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              IconButton(
                icon: Icon(Icons.copy, size: 20, color: color),
                onPressed: () => _copyToClipboard(fingerprint),
                tooltip: 'نسخ $type',
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            fingerprint,
            style: const TextStyle(
              fontFamily: 'monospace',
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    if (_fingerprintInfo == null) return;

    switch (action) {
      case 'copy_sha1':
        _copyToClipboard(_fingerprintInfo!['sha1_fingerprint'] ?? '');
        break;
      case 'copy_sha256':
        _copyToClipboard(_fingerprintInfo!['sha256_fingerprint'] ?? '');
        break;
      case 'copy_all':
        _copyAllInfo();
        break;
      case 'open_console':
        _openFirebaseConsole();
        break;
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم النسخ إلى الحافظة'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _copyAllInfo() {
    if (_fingerprintInfo == null) return;

    final info = '''
Firebase Configuration:
Project ID: ${_fingerprintInfo!['project_id']}
Package Name: ${_fingerprintInfo!['package_name']}
App Name: ${_fingerprintInfo!['app_name']}
Platform: ${_fingerprintInfo!['platform']}

SHA Fingerprints:
SHA-1: ${_fingerprintInfo!['sha1_fingerprint']}
SHA-256: ${_fingerprintInfo!['sha256_fingerprint']}

Generated at: ${_fingerprintInfo!['generated_at']}
''';

    _copyToClipboard(info);
  }

  void _openFirebaseConsole() {
    if (_fingerprintInfo == null) return;

    final projectId = _fingerprintInfo!['project_id'];
    final url = 'https://console.firebase.google.com/project/$projectId/settings/general';

    // In a real app, you would use url_launcher here
    _copyToClipboard(url);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم نسخ رابط Firebase Console إلى الحافظة'),
          duration: Duration(seconds: 3),
        ),
      );
    }
  }
}
