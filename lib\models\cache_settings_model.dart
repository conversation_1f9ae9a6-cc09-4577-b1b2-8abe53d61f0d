import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// نموذج إعدادات الكاش
class CacheSettingsModel {
  final int maxCacheSizeMB;
  final int retentionDays;
  final bool autoCleanup;
  final bool enableThumbnails;
  final bool enableVideoThumbnails;
  final int thumbnailQuality;
  final bool wifiOnlyDownload;
  final bool autoCompressImages;
  final bool enableScheduledCleanup;
  final int cleanupIntervalDays;

  const CacheSettingsModel({
    this.maxCacheSizeMB = 200,
    this.retentionDays = 14,
    this.autoCleanup = true,
    this.enableThumbnails = true,
    this.enableVideoThumbnails = true,
    this.thumbnailQuality = 75,
    this.wifiOnlyDownload = false,
    this.autoCompressImages = true,
    this.enableScheduledCleanup = true,
    this.cleanupIntervalDays = 3,
  });

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'maxCacheSizeMB': maxCacheSizeMB,
      'retentionDays': retentionDays,
      'autoCleanup': autoCleanup,
      'enableThumbnails': enableThumbnails,
      'enableVideoThumbnails': enableVideoThumbnails,
      'thumbnailQuality': thumbnailQuality,
      'wifiOnlyDownload': wifiOnlyDownload,
      'autoCompressImages': autoCompressImages,
      'enableScheduledCleanup': enableScheduledCleanup,
      'cleanupIntervalDays': cleanupIntervalDays,
    };
  }

  /// إنشاء من Map
  factory CacheSettingsModel.fromMap(Map<String, dynamic> map) {
    return CacheSettingsModel(
      maxCacheSizeMB: map['maxCacheSizeMB']?.toInt() ?? 200,
      retentionDays: map['retentionDays']?.toInt() ?? 14,
      autoCleanup: map['autoCleanup'] ?? true,
      enableThumbnails: map['enableThumbnails'] ?? true,
      enableVideoThumbnails: map['enableVideoThumbnails'] ?? true,
      thumbnailQuality: map['thumbnailQuality']?.toInt() ?? 75,
      wifiOnlyDownload: map['wifiOnlyDownload'] ?? false,
      autoCompressImages: map['autoCompressImages'] ?? true,
      enableScheduledCleanup: map['enableScheduledCleanup'] ?? true,
      cleanupIntervalDays: map['cleanupIntervalDays']?.toInt() ?? 3,
    );
  }

  /// نسخ مع تعديل
  CacheSettingsModel copyWith({
    int? maxCacheSizeMB,
    int? retentionDays,
    bool? autoCleanup,
    bool? enableThumbnails,
    bool? enableVideoThumbnails,
    int? thumbnailQuality,
    bool? wifiOnlyDownload,
    bool? autoCompressImages,
    bool? enableScheduledCleanup,
    int? cleanupIntervalDays,
  }) {
    return CacheSettingsModel(
      maxCacheSizeMB: maxCacheSizeMB ?? this.maxCacheSizeMB,
      retentionDays: retentionDays ?? this.retentionDays,
      autoCleanup: autoCleanup ?? this.autoCleanup,
      enableThumbnails: enableThumbnails ?? this.enableThumbnails,
      enableVideoThumbnails: enableVideoThumbnails ?? this.enableVideoThumbnails,
      thumbnailQuality: thumbnailQuality ?? this.thumbnailQuality,
      wifiOnlyDownload: wifiOnlyDownload ?? this.wifiOnlyDownload,
      autoCompressImages: autoCompressImages ?? this.autoCompressImages,
      enableScheduledCleanup: enableScheduledCleanup ?? this.enableScheduledCleanup,
      cleanupIntervalDays: cleanupIntervalDays ?? this.cleanupIntervalDays,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CacheSettingsModel &&
        other.maxCacheSizeMB == maxCacheSizeMB &&
        other.retentionDays == retentionDays &&
        other.autoCleanup == autoCleanup &&
        other.enableThumbnails == enableThumbnails &&
        other.enableVideoThumbnails == enableVideoThumbnails &&
        other.thumbnailQuality == thumbnailQuality &&
        other.wifiOnlyDownload == wifiOnlyDownload &&
        other.autoCompressImages == autoCompressImages &&
        other.enableScheduledCleanup == enableScheduledCleanup &&
        other.cleanupIntervalDays == cleanupIntervalDays;
  }

  @override
  int get hashCode {
    return maxCacheSizeMB.hashCode ^
        retentionDays.hashCode ^
        autoCleanup.hashCode ^
        enableThumbnails.hashCode ^
        enableVideoThumbnails.hashCode ^
        thumbnailQuality.hashCode ^
        wifiOnlyDownload.hashCode ^
        autoCompressImages.hashCode ^
        enableScheduledCleanup.hashCode ^
        cleanupIntervalDays.hashCode;
  }

  @override
  String toString() {
    return 'CacheSettingsModel(maxCacheSizeMB: $maxCacheSizeMB, retentionDays: $retentionDays, autoCleanup: $autoCleanup, enableThumbnails: $enableThumbnails, enableVideoThumbnails: $enableVideoThumbnails, thumbnailQuality: $thumbnailQuality)';
  }
}

/// خدمة إدارة إعدادات الكاش
class CacheSettingsService {
  static const String _settingsKey = 'cache_settings';
  static CacheSettingsService? _instance;
  static CacheSettingsService get instance => _instance ??= CacheSettingsService._();

  CacheSettingsService._();

  CacheSettingsModel _currentSettings = const CacheSettingsModel();

  /// الحصول على الإعدادات الحالية
  CacheSettingsModel get currentSettings => _currentSettings;

  /// تحميل الإعدادات من SharedPreferences
  Future<CacheSettingsModel> loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);
      
      if (settingsJson != null) {
        final Map<String, dynamic> settingsMap = {};
        // تحليل JSON بسيط (يمكن تحسينه لاحقاً)
        final parts = settingsJson.split(',');
        for (final part in parts) {
          final keyValue = part.split(':');
          if (keyValue.length == 2) {
            final key = keyValue[0].trim();
            final value = keyValue[1].trim();
            
            if (value == 'true' || value == 'false') {
              settingsMap[key] = value == 'true';
            } else {
              settingsMap[key] = int.tryParse(value) ?? value;
            }
          }
        }
        
        _currentSettings = CacheSettingsModel.fromMap(settingsMap);
      }
    } catch (e) {
      debugPrint('Error loading cache settings: $e');
      _currentSettings = const CacheSettingsModel();
    }
    
    return _currentSettings;
  }

  /// حفظ الإعدادات في SharedPreferences
  Future<void> saveSettings(CacheSettingsModel settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsMap = settings.toMap();
      
      // تحويل إلى JSON بسيط
      final settingsJson = settingsMap.entries
          .map((e) => '${e.key}:${e.value}')
          .join(',');
      
      await prefs.setString(_settingsKey, settingsJson);
      _currentSettings = settings;
    } catch (e) {
      debugPrint('Error saving cache settings: $e');
    }
  }

  /// إعادة تعيين الإعدادات إلى القيم الافتراضية
  Future<void> resetToDefaults() async {
    const defaultSettings = CacheSettingsModel();
    await saveSettings(defaultSettings);
  }

  /// الحصول على حجم الكاش الأقصى بالبايت
  int get maxCacheSizeBytes => _currentSettings.maxCacheSizeMB * 1024 * 1024;

  /// الحصول على مدة الاحتفاظ بالأيام
  int get retentionDays => _currentSettings.retentionDays;

  /// هل التنظيف التلقائي مفعل
  bool get isAutoCleanupEnabled => _currentSettings.autoCleanup;

  /// هل thumbnails الصور مفعلة
  bool get areThumbnailsEnabled => _currentSettings.enableThumbnails;

  /// هل thumbnails الفيديوهات مفعلة
  bool get areVideoThumbnailsEnabled => _currentSettings.enableVideoThumbnails;

  /// جودة thumbnails
  int get thumbnailQuality => _currentSettings.thumbnailQuality;

  /// هل التحميل على WiFi فقط مفعل
  bool get isWifiOnlyDownloadEnabled => _currentSettings.wifiOnlyDownload;

  /// هل ضغط الصور التلقائي مفعل
  bool get isAutoCompressImagesEnabled => _currentSettings.autoCompressImages;

  /// هل التنظيف المجدول مفعل
  bool get isScheduledCleanupEnabled => _currentSettings.enableScheduledCleanup;

  /// فترة التنظيف المجدول بالأيام
  int get cleanupIntervalDays => _currentSettings.cleanupIntervalDays;
}

/// نموذج إحصائيات الكاش
class CacheStatsModel {
  final int totalFiles;
  final int totalSizeBytes;
  final int thumbnailFiles;
  final int thumbnailSizeBytes;
  final int videoThumbnailFiles;
  final int videoThumbnailSizeBytes;
  final DateTime lastCleanup;
  final int oldestFileAgeInDays;

  const CacheStatsModel({
    this.totalFiles = 0,
    this.totalSizeBytes = 0,
    this.thumbnailFiles = 0,
    this.thumbnailSizeBytes = 0,
    this.videoThumbnailFiles = 0,
    this.videoThumbnailSizeBytes = 0,
    required this.lastCleanup,
    this.oldestFileAgeInDays = 0,
  });

  /// الحجم الإجمالي بالميجابايت
  double get totalSizeMB => totalSizeBytes / (1024 * 1024);

  /// حجم thumbnails بالميجابايت
  double get thumbnailSizeMB => thumbnailSizeBytes / (1024 * 1024);

  /// حجم thumbnails الفيديوهات بالميجابايت
  double get videoThumbnailSizeMB => videoThumbnailSizeBytes / (1024 * 1024);

  /// نسبة استخدام الكاش
  double getUsagePercentage(int maxSizeBytes) {
    if (maxSizeBytes <= 0) return 0;
    return (totalSizeBytes / maxSizeBytes * 100).clamp(0, 100);
  }

  @override
  String toString() {
    return 'CacheStatsModel(totalFiles: $totalFiles, totalSizeMB: ${totalSizeMB.toStringAsFixed(2)}, thumbnailFiles: $thumbnailFiles, videoThumbnailFiles: $videoThumbnailFiles, oldestFileAgeInDays: $oldestFileAgeInDays)';
  }
}
