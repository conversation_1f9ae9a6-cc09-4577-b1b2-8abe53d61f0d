import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';

import '../models/login_history_model.dart';
import 'location_service.dart';

class LoginHistoryService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();

  static Future<void> recordLogin({
    required String userId,
    required String userEmail,
    String? userName,
    bool isSuccessful = true,
  }) async {
    try {
      // Get comprehensive location and IP information
      final locationData = await LocationService.getComprehensiveLocation();
      final ipAddress = locationData['ip'] as String?;
      final locationInfo = locationData['ipLocation'] as Map<String, dynamic>?;

      final deviceInfo = await _getDeviceInfo();
      final loginHistory = LoginHistoryModel(
        id: _firestore.collection('login_history').doc().id,
        userId: userId,
        userEmail: userEmail,
        userName: userName,
        loginTime: DateTime.now(),
        deviceInfo: deviceInfo,
        ipAddress: ipAddress,
        location: locationInfo != null ? LocationService.formatLocationForDisplay({'ipLocation': locationInfo}) : null,
        userAgent: await _getUserAgent(),
        isSuccessful: isSuccessful,
      );

      await _firestore
          .collection('login_history')
          .doc(loginHistory.id)
          .set({
            ...loginHistory.toMap(),
            'fullLocationData': locationData, // Store complete location data
            'locationSummary': LocationService.getLocationSummary(locationData),
            'timezone': LocationService.getTimezoneFromLocation(locationData),
            'isp': LocationService.getIspFromLocation(locationData),
          });

      // Also update user's last login time with enhanced information
      await _firestore.collection('users').doc(userId).update({
        'lastLoginTime': FieldValue.serverTimestamp(),
        'lastLoginDevice': deviceInfo,
        'lastLoginIp': ipAddress,
        'lastLoginLocation': LocationService.getLocationSummary(locationData),
        'lastLoginTimezone': LocationService.getTimezoneFromLocation(locationData),
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error recording login: $e');
      }
    }
  }

  static Future<String> _getDeviceInfo() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        return '${androidInfo.brand} ${androidInfo.model} (Android ${androidInfo.version.release})';
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        return '${iosInfo.name} ${iosInfo.model} (iOS ${iosInfo.systemVersion})';
      } else if (Platform.isWindows) {
        final windowsInfo = await _deviceInfo.windowsInfo;
        return '${windowsInfo.computerName} (Windows)';
      } else if (Platform.isMacOS) {
        final macInfo = await _deviceInfo.macOsInfo;
        return '${macInfo.computerName} (macOS ${macInfo.osRelease})';
      } else if (Platform.isLinux) {
        final linuxInfo = await _deviceInfo.linuxInfo;
        return '${linuxInfo.name} (Linux)';
      } else {
        return 'Unknown Device';
      }
    } catch (e) {
      return 'Unknown Device';
    }
  }



  static Future<String?> _getUserAgent() async {
    try {
      if (kIsWeb) {
        // For web, you can get user agent from the browser
        return 'Web Browser';
      } else {
        return Platform.operatingSystem;
      }
    } catch (e) {
      return null;
    }
  }

  static Future<List<LoginHistoryModel>> getUserLoginHistory(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection('login_history')
          .where('userId', isEqualTo: userId)
          .orderBy('loginTime', descending: true)
          .limit(50)
          .get();

      return querySnapshot.docs
          .map((doc) => LoginHistoryModel.fromMap(doc.data()))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting user login history: $e');
      }
      return [];
    }
  }

  static Future<List<LoginHistoryModel>> getAllLoginHistory() async {
    try {
      final querySnapshot = await _firestore
          .collection('login_history')
          .orderBy('loginTime', descending: true)
          .limit(100)
          .get();

      return querySnapshot.docs
          .map((doc) => LoginHistoryModel.fromMap(doc.data()))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting all login history: $e');
      }
      return [];
    }
  }
}
