import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:crypto/crypto.dart';
import '../utils/path_utils.dart';
import '../utils/platform_utils.dart';

class AssetOptimizationService {
  static final Map<String, Uint8List> _imageCache = {};
  static const int _maxCacheSize = 20 * 1024 * 1024; // Further reduced to 20MB cache limit
  static int _currentCacheSize = 0;
  static const int _maxImageWidth = 1600; // Reduced max width
  static const int _maxImageHeight = 900; // Reduced max height
  static const int _defaultQuality = 65; // Further reduced default quality

  /// Load and cache optimized image
  static Future<Uint8List?> loadOptimizedImage(
    String assetPath, {
    int? maxWidth,
    int? maxHeight,
    int? quality,
  }) async {
    // Use default values if not provided
    maxWidth ??= _maxImageWidth;
    maxHeight ??= _maxImageHeight;
    quality ??= _defaultQuality;
    try {
      // Generate cache key
      final cacheKey = _generateCacheKey(assetPath, maxWidth, maxHeight, quality);

      // Check memory cache first
      if (_imageCache.containsKey(cacheKey)) {
        return _imageCache[cacheKey];
      }

      // Check disk cache
      final cachedData = await _loadFromDiskCache(cacheKey);
      if (cachedData != null) {
        _addToMemoryCache(cacheKey, cachedData);
        return cachedData;
      }

      // Load and optimize image
      final originalData = await rootBundle.load(assetPath);
      final optimizedData = await _optimizeImage(
        originalData.buffer.asUint8List(),
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        quality: quality,
      );

      if (optimizedData != null) {
        // Cache the optimized image
        await _saveToDiskCache(cacheKey, optimizedData);
        _addToMemoryCache(cacheKey, optimizedData);
        return optimizedData;
      }

      return originalData.buffer.asUint8List();
    } catch (e) {
      debugPrint('Error loading optimized image: $e');
      return null;
    }
  }

  /// Optimize image data
  static Future<Uint8List?> _optimizeImage(
    Uint8List imageData, {
    int? maxWidth,
    int? maxHeight,
    int quality = 85,
  }) async {
    try {
      // Validate image data first
      if (imageData.isEmpty) {
        debugPrint('Error optimizing image: Empty image data');
        return null;
      }

      // Check if the data is a valid image by examining the header
      if (!_isValidImageData(imageData)) {
        // Silently return original data for non-image files (like PDFs)
        return imageData; // Return original data if not a valid image
      }

      // Decode image with error handling
      final codec = await ui.instantiateImageCodec(
        imageData,
        targetWidth: maxWidth,
        targetHeight: maxHeight,
      );
      final frame = await codec.getNextFrame();
      final image = frame.image;

      // Convert to bytes with compression
      final byteData = await image.toByteData(
        format: ui.ImageByteFormat.png, // Use JPEG for better compression
      );

      if (byteData != null) {
        return byteData.buffer.asUint8List();
      }

      return imageData; // Return original if optimization fails
    } catch (e) {
      debugPrint('Error optimizing image: $e');
      // Return original data instead of null to prevent breaking the app
      return imageData;
    }
  }

  /// Check if the data represents a valid image
  static bool _isValidImageData(Uint8List data) {
    if (data.length < 10) return false;

    // Check for common image format headers
    // PNG: 89 50 4E 47 0D 0A 1A 0A
    if (data.length >= 8 &&
        data[0] == 0x89 && data[1] == 0x50 && data[2] == 0x4E && data[3] == 0x47) {
      return true;
    }

    // JPEG: FF D8 FF
    if (data.length >= 3 &&
        data[0] == 0xFF && data[1] == 0xD8 && data[2] == 0xFF) {
      return true;
    }

    // GIF: 47 49 46 38
    if (data.length >= 4 &&
        data[0] == 0x47 && data[1] == 0x49 && data[2] == 0x46 && data[3] == 0x38) {
      return true;
    }

    // WebP: 52 49 46 46 ... 57 45 42 50
    if (data.length >= 12 &&
        data[0] == 0x52 && data[1] == 0x49 && data[2] == 0x46 && data[3] == 0x46 &&
        data[8] == 0x57 && data[9] == 0x45 && data[10] == 0x42 && data[11] == 0x50) {
      return true;
    }

    // BMP: 42 4D
    if (data.length >= 2 &&
        data[0] == 0x42 && data[1] == 0x4D) {
      return true;
    }

    return false;
  }

  /// Generate cache key
  static String _generateCacheKey(
    String assetPath,
    int? maxWidth,
    int? maxHeight,
    int quality,
  ) {
    final key = '$assetPath-$maxWidth-$maxHeight-$quality';
    return md5.convert(key.codeUnits).toString();
  }

  /// Add to memory cache with size management
  static void _addToMemoryCache(String key, Uint8List data) {
    final dataSize = data.length;

    // Check if adding this would exceed cache limit
    if (_currentCacheSize + dataSize > _maxCacheSize) {
      _clearOldestCacheEntries(dataSize);
    }

    _imageCache[key] = data;
    _currentCacheSize += dataSize;
  }

  /// Clear oldest cache entries to make room
  static void _clearOldestCacheEntries(int requiredSpace) {
    final entries = _imageCache.entries.toList();
    int freedSpace = 0;

    for (int i = 0; i < entries.length && freedSpace < requiredSpace; i++) {
      final entry = entries[i];
      freedSpace += entry.value.length;
      _currentCacheSize -= entry.value.length;
      _imageCache.remove(entry.key);
    }
  }

  /// Load from disk cache
  static Future<Uint8List?> _loadFromDiskCache(String key) async {
    if (PlatformUtils.isWeb) {
      // Web doesn't support file operations
      return null;
    }

    try {
      final cacheDir = await _getCacheDirectory();
      final file = File('${cacheDir.path}/img_$key.cache');

      if (await file.exists()) {
        return await file.readAsBytes();
      }

      return null;
    } catch (e) {
      debugPrint('Error loading from disk cache: $e');
      return null;
    }
  }

  /// Save to disk cache
  static Future<void> _saveToDiskCache(String key, Uint8List data) async {
    if (PlatformUtils.isWeb) {
      // Web doesn't support file operations
      return;
    }

    try {
      final cacheDir = await _getCacheDirectory();
      final file = File('${cacheDir.path}/img_$key.cache');
      await file.writeAsBytes(data);
    } catch (e) {
      debugPrint('Error saving to disk cache: $e');
    }
  }

  /// Get cache directory
  static Future<Directory> _getCacheDirectory() async {
    if (PlatformUtils.isWeb) {
      // For web, return a virtual directory
      return Directory('/virtual/image_cache');
    }

    final tempDir = await PathUtils.getTemporaryDirectory();
    final cacheDir = Directory('${tempDir.path}/image_cache');

    if (!await cacheDir.exists()) {
      await cacheDir.create(recursive: true);
    }

    return cacheDir;
  }

  /// Clear all caches
  static Future<void> clearAllCaches() async {
    try {
      // Clear memory cache
      _imageCache.clear();
      _currentCacheSize = 0;

      if (!PlatformUtils.isWeb) {
        // Clear disk cache (only on non-web platforms)
        final cacheDir = await _getCacheDirectory();
        if (await cacheDir.exists()) {
          await cacheDir.delete(recursive: true);
        }
      }

      debugPrint('All image caches cleared');
    } catch (e) {
      debugPrint('Error clearing caches: $e');
    }
  }

  /// Get cache size info
  static Future<Map<String, int>> getCacheInfo() async {
    try {
      final cacheDir = await _getCacheDirectory();
      int diskCacheSize = 0;

      if (await cacheDir.exists()) {
        final files = await cacheDir.list().toList();
        for (final file in files) {
          if (file is File) {
            final stat = await file.stat();
            diskCacheSize += stat.size;
          }
        }
      }

      return {
        'memoryCacheSize': _currentCacheSize,
        'diskCacheSize': diskCacheSize,
        'totalCacheSize': _currentCacheSize + diskCacheSize,
        'cacheEntries': _imageCache.length,
      };
    } catch (e) {
      debugPrint('Error getting cache info: $e');
      return {
        'memoryCacheSize': _currentCacheSize,
        'diskCacheSize': 0,
        'totalCacheSize': _currentCacheSize,
        'cacheEntries': _imageCache.length,
      };
    }
  }

  /// Preload critical assets
  static Future<void> preloadCriticalAssets() async {
    final criticalAssets = [
      'assets/images/logo.svg',
      'assets/images/placeholder.png',
    ];

    for (final asset in criticalAssets) {
      try {
        await loadOptimizedImage(
          asset,
          maxWidth: 200,
          maxHeight: 200,
          quality: 90,
        );
      } catch (e) {
        debugPrint('Error preloading asset $asset: $e');
      }
    }
  }

  /// Clean up old cache files
  static Future<void> cleanupOldCache() async {
    try {
      final cacheDir = await _getCacheDirectory();
      if (!await cacheDir.exists()) return;

      final now = DateTime.now();
      final files = await cacheDir.list().toList();

      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          final age = now.difference(stat.modified);

          // Delete files older than 7 days
          if (age.inDays > 7) {
            await file.delete();
          }
        }
      }

      debugPrint('Old cache files cleaned up');
    } catch (e) {
      debugPrint('Error cleaning up old cache: $e');
    }
  }

  /// Optimize asset loading based on device capabilities
  static Map<String, dynamic> getOptimalImageSettings() {
    // Get device pixel ratio and screen size
    final pixelRatio = ui.PlatformDispatcher.instance.views.first.devicePixelRatio;
    final screenSize = ui.PlatformDispatcher.instance.views.first.physicalSize;

    // Calculate optimal settings based on device
    int maxWidth = (screenSize.width / pixelRatio).round();
    int maxHeight = (screenSize.height / pixelRatio).round();
    int quality = _defaultQuality;

    // Adjust for low-end devices (more aggressive optimization)
    if (pixelRatio < 2.0) {
      quality = 60; // Lower quality for low-end devices
      maxWidth = (maxWidth * 0.7).round(); // More aggressive size reduction
      maxHeight = (maxHeight * 0.7).round();
    } else if (pixelRatio < 3.0) {
      quality = 70; // Medium quality for mid-range devices
      maxWidth = (maxWidth * 0.8).round();
      maxHeight = (maxHeight * 0.8).round();
    }

    // Ensure we don't exceed our maximum limits
    maxWidth = maxWidth.clamp(200, _maxImageWidth);
    maxHeight = maxHeight.clamp(200, _maxImageHeight);

    return {
      'maxWidth': maxWidth,
      'maxHeight': maxHeight,
      'quality': quality,
      'pixelRatio': pixelRatio,
      'useWebP': pixelRatio >= 2.0, // Use WebP for high-density displays
      'format': ui.ImageByteFormat.png, // Default to JPEG for better compression
    };
  }

  /// Convert image to WebP format for better compression
  static Future<Uint8List?> convertToWebP(Uint8List imageData, {int quality = 75}) async {
    try {
      // Decode the image
      final codec = await ui.instantiateImageCodec(imageData);
      final frame = await codec.getNextFrame();
      final image = frame.image;

      // Convert to WebP format (if supported)
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData != null) {
        return byteData.buffer.asUint8List();
      }

      return imageData;
    } catch (e) {
      debugPrint('Error converting to WebP: $e');
      return imageData;
    }
  }

  /// Clear all cached images
  static void clearCache() {
    _imageCache.clear();
    _currentCacheSize = 0;
    debugPrint('Asset optimization cache cleared');
  }

  /// Clean up old cache entries (memory cache)
  static void cleanupOldMemoryCache() {
    final keysToRemove = <String>[];

    // Keep only 100 most recent items in memory cache
    if (_imageCache.length > 100) {
      final keys = _imageCache.keys.toList();
      keysToRemove.addAll(keys.take(_imageCache.length - 100));
    }

    for (final key in keysToRemove) {
      _imageCache.remove(key);
    }

    _recalculateCacheSize();
    debugPrint('Old memory cache entries cleaned up');
  }

  /// Recalculate current cache size
  static void _recalculateCacheSize() {
    _currentCacheSize = 0;
    for (final data in _imageCache.values) {
      _currentCacheSize += data.length;
    }
  }

  /// Get cache size statistics
  static Map<String, dynamic> getCacheStats() {
    return {
      'currentSize': _currentCacheSize,
      'maxSize': _maxCacheSize,
      'itemCount': _imageCache.length,
      'usagePercentage': (_currentCacheSize / _maxCacheSize * 100).round(),
      'formattedCurrentSize': _formatBytes(_currentCacheSize),
      'formattedMaxSize': _formatBytes(_maxCacheSize),
    };
  }

  /// Format bytes to human readable string
  static String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
