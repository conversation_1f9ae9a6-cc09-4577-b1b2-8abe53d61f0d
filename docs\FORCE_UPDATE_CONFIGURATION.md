# Force Update Configuration Guide

This guide explains how to configure your app to force users to update using Firebase Remote Config.

## Overview

The app includes a comprehensive force update system that allows you to:
- Force all users to update to a specific version
- Set minimum supported versions
- Enable maintenance mode to block app access
- Provide optional update notifications
- Customize update messages in multiple languages

## Quick Setup

### 1. Access Firebase Remote Config

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Navigate to **Remote Config** in the left sidebar
4. Click **Add parameter** to add new configuration keys

### 2. Force Update Configuration

To force all users to update immediately, set these parameters:

```json
{
  "update_enabled": true,
  "update_required": true,
  "latest_version": "1.0.2",
  "minimum_version": "1.0.2",
  "update_url_android": "https://play.google.com/store/apps/details?id=your.package.name",
  "update_url_ios": "https://apps.apple.com/app/your-app-id",
  "force_update_message_ar": "يجب تحديث التطبيق للمتابعة. هذا الإصدار لم يعد مدعوماً.",
  "force_update_message_en": "You must update the app to continue. This version is no longer supported.",
  "update_title_ar": "تحديث مطلوب",
  "update_title_en": "Update Required"
}
```

### 3. Publish Configuration

1. Click **Publish changes** in Firebase Remote Config
2. Confirm the changes
3. Users will receive the update prompt within minutes

## Configuration Parameters

### Core Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `update_enabled` | boolean | Enable/disable update checking | `true` |
| `update_required` | boolean | Make update mandatory | `true` |
| `latest_version` | string | Latest app version available | `"1.0.2"` |
| `minimum_version` | string | Minimum supported version | `"1.0.1"` |

### Store URLs

| Parameter | Type | Description |
|-----------|------|-------------|
| `update_url_android` | string | Google Play Store URL |
| `update_url_ios` | string | Apple App Store URL |

### Messages (Arabic)

| Parameter | Type | Description |
|-----------|------|-------------|
| `update_title_ar` | string | Update dialog title in Arabic |
| `force_update_message_ar` | string | Force update message in Arabic |
| `update_message_ar` | string | Optional update message in Arabic |

### Messages (English)

| Parameter | Type | Description |
|-----------|------|-------------|
| `update_title_en` | string | Update dialog title in English |
| `force_update_message_en` | string | Force update message in English |
| `update_message_en` | string | Optional update message in English |

### Maintenance Mode

| Parameter | Type | Description |
|-----------|------|-------------|
| `maintenance_mode` | boolean | Block all app access |
| `maintenance_message_ar` | string | Maintenance message in Arabic |
| `maintenance_message_en` | string | Maintenance message in English |

## Common Scenarios

### Scenario 1: Force All Users to Update

**Use Case**: Critical security update or major bug fix

```json
{
  "update_enabled": true,
  "update_required": true,
  "latest_version": "1.0.2",
  "minimum_version": "1.0.2"
}
```

**Result**: All users must update to continue using the app

### Scenario 2: Force Users Below Minimum Version

**Use Case**: Deprecate old versions while allowing recent versions

```json
{
  "update_enabled": true,
  "update_required": false,
  "latest_version": "1.0.3",
  "minimum_version": "1.0.1"
}
```

**Result**: 
- Users on v1.0.0 → Force update
- Users on v1.0.1-1.0.2 → Optional update
- Users on v1.0.3+ → No update needed

### Scenario 3: Optional Update Notification

**Use Case**: New features available but not critical

```json
{
  "update_enabled": true,
  "update_required": false,
  "latest_version": "1.0.2",
  "minimum_version": "1.0.0"
}
```

**Result**: Users see optional update notification with "Skip" option

### Scenario 4: Maintenance Mode

**Use Case**: Server maintenance or critical issues

```json
{
  "maintenance_mode": true,
  "maintenance_message_ar": "التطبيق تحت الصيانة حالياً. يرجى المحاولة لاحقاً.",
  "maintenance_message_en": "The app is currently under maintenance. Please try again later."
}
```

**Result**: All users are blocked from using the app

### Scenario 5: Disable Updates

**Use Case**: Temporarily disable update checking

```json
{
  "update_enabled": false
}
```

**Result**: No update checking performed

## Admin Interface

### Access Update Management

1. Open the app as an admin user
2. Go to **Admin Panel** → **Update Management**
3. Click the **Settings** icon in the top-right
4. Access **App Update Management** screen

### Features Available

- **Current Status**: View current update configuration
- **Quick Actions**: Apply common settings with one click
- **Configuration Guide**: Step-by-step instructions
- **Real-time Monitoring**: See current remote config values

### Quick Actions

The admin interface provides these quick actions:

1. **Force Update**: Force all users to update immediately
2. **Optional Update**: Show optional update notification
3. **Maintenance Mode**: Block all app access
4. **Disable Updates**: Turn off update checking

## Testing Force Updates

### Test Environment Setup

1. **Create Test Remote Config**:
   ```json
   {
     "update_enabled": true,
     "update_required": true,
     "latest_version": "999.0.0",
     "minimum_version": "999.0.0"
   }
   ```

2. **Publish Configuration** in Firebase Console

3. **Test App Behavior**:
   - Open the app
   - Should see force update dialog
   - Cannot dismiss or skip the dialog
   - "Update" button redirects to store

### Production Deployment

1. **Prepare Store Listings**: Ensure new version is available in stores
2. **Set Realistic Versions**: Use actual version numbers
3. **Test Messages**: Verify Arabic and English messages
4. **Gradual Rollout**: Consider using Firebase Remote Config conditions for gradual rollout

## Version Comparison Logic

The app uses semantic version comparison:

```
Current: 1.0.1
Minimum: 1.0.2
Latest:  1.0.3

Result: Force update (current < minimum)
```

```
Current: 1.0.2
Minimum: 1.0.1
Latest:  1.0.3

Result: Optional update (minimum ≤ current < latest)
```

```
Current: 1.0.3
Minimum: 1.0.1
Latest:  1.0.3

Result: No update needed (current = latest)
```

## Troubleshooting

### Common Issues

1. **Update Dialog Not Showing**
   - Check `update_enabled` is `true`
   - Verify version numbers are correct
   - Ensure Remote Config is published

2. **Wrong Store URL**
   - Verify `update_url_android` and `update_url_ios`
   - Test URLs in browser

3. **Messages Not Displaying**
   - Check message parameters are set
   - Verify language-specific keys

4. **Remote Config Not Updating**
   - Allow up to 12 hours for changes to propagate
   - Use "Fetch and activate" in admin interface
   - Check network connectivity

### Debug Information

Use the admin interface to view:
- Current app version
- Remote config values
- Update status
- Last check time

## Best Practices

1. **Test Before Production**: Always test force updates in development
2. **Clear Communication**: Provide clear update messages
3. **Store Availability**: Ensure new version is available before forcing updates
4. **Gradual Rollout**: Use Remote Config conditions for phased rollouts
5. **Monitor Impact**: Watch for user feedback and app store reviews
6. **Emergency Disable**: Keep ability to quickly disable force updates

## Security Considerations

1. **Access Control**: Limit who can modify Remote Config
2. **Audit Trail**: Monitor Remote Config changes
3. **Backup Plans**: Have rollback procedures ready
4. **Version Validation**: Ensure version numbers are valid

## Support

For technical support:
- Check the admin interface for current status
- Review Firebase Remote Config logs
- Contact development team for assistance

---

**Last Updated**: May 30, 2024  
**Version**: 1.0.1+  
**Related Documentation**: [Google Drive Remote Config](./GOOGLE_DRIVE_REMOTE_CONFIG.md)
