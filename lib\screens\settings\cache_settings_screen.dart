import 'dart:async';
import 'package:flutter/material.dart';
import '../../models/cache_settings_model.dart';
import '../../services/attachment_cache_service.dart';
import '../../services/video_thumbnail_service.dart';

/// شاشة إعدادات الكاش المتقدمة
class CacheSettingsScreen extends StatefulWidget {
  const CacheSettingsScreen({super.key});

  @override
  State<CacheSettingsScreen> createState() => _CacheSettingsScreenState();
}

class _CacheSettingsScreenState extends State<CacheSettingsScreen>
    with TickerProviderStateMixin {
  late CacheSettingsModel _settings;
  CacheStatsModel? _stats;
  bool _isLoading = true;
  bool _isSaving = false;
  bool _isClearing = false;
  bool _isOptimizing = false;

  late TabController _tabController;
  Timer? _refreshTimer;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadSettings();
    _startAutoRefresh();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _refreshTimer?.cancel();
    super.dispose();
  }

  void _startAutoRefresh() {
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        _loadCacheStats();
      }
    });
  }

  Future<void> _loadSettings() async {
    setState(() => _isLoading = true);
    
    try {
      _settings = await CacheSettingsService.instance.loadSettings();
      await _loadCacheStats();
    } catch (e) {
      _showErrorSnackBar('فشل في تحميل الإعدادات: $e');
    }
    
    setState(() => _isLoading = false);
  }

  Future<void> _loadCacheStats() async {
    try {
      final cacheInfo = await AttachmentCacheService.instance.getCacheInfo();
      final videoThumbnailSize = await VideoThumbnailService.instance.getThumbnailCacheSize();

      // حساب إحصائيات أكثر تفصيلاً
      final thumbnailInfo = await _getThumbnailStats();
      final oldestFileAge = await _getOldestFileAge();

      _stats = CacheStatsModel(
        totalFiles: cacheInfo.fileCount,
        totalSizeBytes: cacheInfo.totalSize,
        thumbnailFiles: thumbnailInfo['count'] ?? 0,
        thumbnailSizeBytes: thumbnailInfo['size'] ?? 0,
        videoThumbnailFiles: 0, // سيتم تحسينه
        videoThumbnailSizeBytes: videoThumbnailSize,
        lastCleanup: DateTime.now(),
        oldestFileAgeInDays: oldestFileAge,
      );

      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      debugPrint('Error loading cache stats: $e');
    }
  }

  Future<Map<String, int>> _getThumbnailStats() async {
    try {
      // هذه دالة مساعدة لحساب إحصائيات thumbnails
      // يمكن تحسينها لاحقاً بإضافة طرق في AttachmentCacheService
      return {'count': 0, 'size': 0};
    } catch (e) {
      debugPrint('Error getting thumbnail stats: $e');
      return {'count': 0, 'size': 0};
    }
  }

  Future<int> _getOldestFileAge() async {
    try {
      // حساب عمر أقدم ملف في الكاش
      // يمكن تحسينها لاحقاً
      return 0;
    } catch (e) {
      debugPrint('Error getting oldest file age: $e');
      return 0;
    }
  }

  Future<void> _saveSettings() async {
    setState(() => _isSaving = true);
    
    try {
      await CacheSettingsService.instance.saveSettings(_settings);
      _showSuccessSnackBar('تم حفظ الإعدادات بنجاح');
    } catch (e) {
      _showErrorSnackBar('فشل في حفظ الإعدادات: $e');
    }
    
    setState(() => _isSaving = false);
  }

  Future<void> _clearCache() async {
    final confirmed = await _showConfirmDialog(
      'مسح الكاش',
      'هل أنت متأكد من مسح جميع الملفات المخزنة مؤقتاً؟\nسيتم إعادة تحميلها عند الحاجة.',
    );
    
    if (!confirmed) return;
    
    setState(() => _isClearing = true);
    
    try {
      await AttachmentCacheService.instance.clearAllCache();
      await VideoThumbnailService.instance.clearThumbnailCache();
      await _loadCacheStats();
      _showSuccessSnackBar('تم مسح الكاش بنجاح');
    } catch (e) {
      _showErrorSnackBar('فشل في مسح الكاش: $e');
    }
    
    setState(() => _isClearing = false);
  }

  Future<void> _resetSettings() async {
    final confirmed = await _showConfirmDialog(
      'إعادة تعيين الإعدادات',
      'هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟',
    );

    if (!confirmed) return;

    try {
      await CacheSettingsService.instance.resetToDefaults();
      _settings = await CacheSettingsService.instance.loadSettings();
      setState(() {});
      _showSuccessSnackBar('تم إعادة تعيين الإعدادات بنجاح');
    } catch (e) {
      _showErrorSnackBar('فشل في إعادة تعيين الإعدادات: $e');
    }
  }

  Future<void> _optimizeCache() async {
    final confirmed = await _showConfirmDialog(
      'تحسين الكاش',
      'سيتم تحسين الكاش عبر:\n• إزالة الملفات المكررة\n• ضغط الصور الكبيرة\n• تنظيف thumbnails القديمة\n\nهل تريد المتابعة؟',
    );

    if (!confirmed) return;

    setState(() => _isOptimizing = true);

    try {
      // تنظيف thumbnails القديمة
      await VideoThumbnailService.instance.cleanOldThumbnails(maxAgeInDays: _settings.retentionDays);

      // تحديث الإحصائيات
      await _loadCacheStats();

      _showSuccessSnackBar('تم تحسين الكاش بنجاح');
    } catch (e) {
      _showErrorSnackBar('فشل في تحسين الكاش: $e');
    }

    setState(() => _isOptimizing = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات الكاش المتقدمة'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.analytics), text: 'الإحصائيات'),
            Tab(icon: Icon(Icons.settings), text: 'الإعدادات'),
            Tab(icon: Icon(Icons.build), text: 'الأدوات'),
          ],
        ),
        actions: [
          if (_isSaving)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          else
            IconButton(
              onPressed: _saveSettings,
              icon: const Icon(Icons.save),
              tooltip: 'حفظ الإعدادات',
            ),
          IconButton(
            onPressed: () => _loadCacheStats(),
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث الإحصائيات',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildStatsTab(),
                _buildSettingsTab(),
                _buildToolsTab(),
              ],
            ),
    );
  }

  Widget _buildStatsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatsSection(),
          const SizedBox(height: 16),
          _buildDetailedStatsSection(),
          const SizedBox(height: 16),
          _buildUsageChartSection(),
        ],
      ),
    );
  }

  Widget _buildSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSizeSettings(),
          const SizedBox(height: 16),
          _buildRetentionSettings(),
          const SizedBox(height: 16),
          _buildThumbnailSettings(),
          const SizedBox(height: 16),
          _buildNetworkSettings(),
        ],
      ),
    );
  }

  Widget _buildToolsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildActionsSection(),
          const SizedBox(height: 16),
          _buildMaintenanceSection(),
          const SizedBox(height: 16),
          _buildAdvancedToolsSection(),
        ],
      ),
    );
  }

  Widget _buildStatsSection() {
    if (_stats == null) return const SizedBox.shrink();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.analytics, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'إحصائيات الكاش',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatRow('إجمالي الملفات', '${_stats!.totalFiles}'),
            _buildStatRow('الحجم الإجمالي', '${_stats!.totalSizeMB.toStringAsFixed(1)} ميجابايت'),
            _buildStatRow('thumbnails الفيديوهات', '${(_stats!.videoThumbnailSizeBytes / 1024 / 1024).toStringAsFixed(1)} ميجابايت'),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: _stats!.getUsagePercentage(CacheSettingsService.instance.maxCacheSizeBytes) / 100,
              backgroundColor: Colors.grey.shade300,
              valueColor: AlwaysStoppedAnimation<Color>(
                _stats!.getUsagePercentage(CacheSettingsService.instance.maxCacheSizeBytes) > 80
                    ? Colors.red
                    : Colors.blue,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'استخدام الكاش: ${_stats!.getUsagePercentage(CacheSettingsService.instance.maxCacheSizeBytes).toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildSizeSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.storage, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  'إعدادات الحجم',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text('الحد الأقصى لحجم الكاش: ${_settings.maxCacheSizeMB} ميجابايت'),
            Slider(
              value: _settings.maxCacheSizeMB.toDouble(),
              min: 50,
              max: 1000,
              divisions: 19,
              label: '${_settings.maxCacheSizeMB} MB',
              onChanged: (value) {
                setState(() {
                  _settings = _settings.copyWith(maxCacheSizeMB: value.round());
                });
              },
            ),
            const SizedBox(height: 8),
            SwitchListTile(
              title: const Text('التنظيف التلقائي'),
              subtitle: const Text('مسح الملفات القديمة تلقائياً عند امتلاء الكاش'),
              value: _settings.autoCleanup,
              onChanged: (value) {
                setState(() {
                  _settings = _settings.copyWith(autoCleanup: value);
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRetentionSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.schedule, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'إعدادات الاحتفاظ',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text('مدة الاحتفاظ بالملفات: ${_settings.retentionDays} يوم'),
            Slider(
              value: _settings.retentionDays.toDouble(),
              min: 1,
              max: 30,
              divisions: 29,
              label: '${_settings.retentionDays} يوم',
              onChanged: (value) {
                setState(() {
                  _settings = _settings.copyWith(retentionDays: value.round());
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThumbnailSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.image, color: Colors.purple),
                const SizedBox(width: 8),
                Text(
                  'إعدادات الصور المصغرة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('تفعيل الصور المصغرة'),
              subtitle: const Text('إنشاء صور مصغرة للصور'),
              value: _settings.enableThumbnails,
              onChanged: (value) {
                setState(() {
                  _settings = _settings.copyWith(enableThumbnails: value);
                });
              },
            ),
            SwitchListTile(
              title: const Text('تفعيل صور الفيديوهات المصغرة'),
              subtitle: const Text('استخراج صور مصغرة من الفيديوهات'),
              value: _settings.enableVideoThumbnails,
              onChanged: (value) {
                setState(() {
                  _settings = _settings.copyWith(enableVideoThumbnails: value);
                });
              },
            ),
            if (_settings.enableThumbnails || _settings.enableVideoThumbnails) ...[
              const SizedBox(height: 8),
              Text('جودة الصور المصغرة: ${_settings.thumbnailQuality}%'),
              Slider(
                value: _settings.thumbnailQuality.toDouble(),
                min: 30,
                max: 100,
                divisions: 7,
                label: '${_settings.thumbnailQuality}%',
                onChanged: (value) {
                  setState(() {
                    _settings = _settings.copyWith(thumbnailQuality: value.round());
                  });
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.settings, color: Colors.red),
                const SizedBox(width: 8),
                Text(
                  'إجراءات سريعة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isClearing ? null : _clearCache,
                icon: _isClearing
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.clear_all),
                label: Text(_isClearing ? 'جاري المسح...' : 'مسح الكاش'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isOptimizing ? null : _optimizeCache,
                icon: _isOptimizing
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.tune),
                label: Text(_isOptimizing ? 'جاري التحسين...' : 'تحسين الكاش'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<bool> _showConfirmDialog(String title, String content) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  Widget _buildDetailedStatsSection() {
    if (_stats == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info_outline, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'تفاصيل الكاش',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildDetailedStatRow('ملفات الصور', '${_stats!.totalFiles - _stats!.videoThumbnailFiles}'),
            _buildDetailedStatRow('thumbnails الصور', '${_stats!.thumbnailFiles}'),
            _buildDetailedStatRow('thumbnails الفيديوهات', '${_stats!.videoThumbnailFiles}'),
            _buildDetailedStatRow('عمر أقدم ملف', '${_stats!.oldestFileAgeInDays} يوم'),
            const Divider(),
            _buildDetailedStatRow('حجم thumbnails الصور', '${_stats!.thumbnailSizeMB.toStringAsFixed(1)} ميجابايت'),
            _buildDetailedStatRow('حجم thumbnails الفيديوهات', '${_stats!.videoThumbnailSizeMB.toStringAsFixed(1)} ميجابايت'),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsageChartSection() {
    if (_stats == null) return const SizedBox.shrink();

    final usagePercentage = _stats!.getUsagePercentage(CacheSettingsService.instance.maxCacheSizeBytes);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.pie_chart, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'استخدام المساحة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: usagePercentage / 100,
              backgroundColor: Colors.grey.shade300,
              valueColor: AlwaysStoppedAnimation<Color>(
                usagePercentage > 90 ? Colors.red :
                usagePercentage > 70 ? Colors.orange : Colors.green,
              ),
              minHeight: 8,
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('${_stats!.totalSizeMB.toStringAsFixed(1)} ميجابايت'),
                Text('${_settings.maxCacheSizeMB} ميجابايت'),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'المساحة المستخدمة: ${usagePercentage.toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: usagePercentage > 90 ? Colors.red : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNetworkSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.network_check, color: Colors.teal),
                const SizedBox(width: 8),
                Text(
                  'إعدادات الشبكة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('التحميل على WiFi فقط'),
              subtitle: const Text('تحميل الملفات الكبيرة على WiFi فقط'),
              value: _settings.wifiOnlyDownload,
              onChanged: (value) {
                setState(() {
                  _settings = _settings.copyWith(wifiOnlyDownload: value);
                });
              },
            ),
            SwitchListTile(
              title: const Text('ضغط الصور تلقائياً'),
              subtitle: const Text('ضغط الصور قبل التخزين لتوفير المساحة'),
              value: _settings.autoCompressImages,
              onChanged: (value) {
                setState(() {
                  _settings = _settings.copyWith(autoCompressImages: value);
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMaintenanceSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.build_circle, color: Colors.purple),
                const SizedBox(width: 8),
                Text(
                  'الصيانة التلقائية',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.schedule),
              title: const Text('جدولة التنظيف'),
              subtitle: Text('تنظيف تلقائي كل ${_settings.cleanupIntervalDays} أيام'),
              trailing: Switch(
                value: _settings.enableScheduledCleanup,
                onChanged: (value) {
                  setState(() {
                    _settings = _settings.copyWith(enableScheduledCleanup: value);
                  });
                },
              ),
            ),
            if (_settings.enableScheduledCleanup) ...[
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('فترة التنظيف: ${_settings.cleanupIntervalDays} أيام'),
                    Slider(
                      value: _settings.cleanupIntervalDays.toDouble(),
                      min: 1,
                      max: 7,
                      divisions: 6,
                      label: '${_settings.cleanupIntervalDays} أيام',
                      onChanged: (value) {
                        setState(() {
                          _settings = _settings.copyWith(cleanupIntervalDays: value.round());
                        });
                      },
                    ),
                  ],
                ),
              ),
            ],
            const Divider(),
            OutlinedButton.icon(
              onPressed: () => _resetSettings(),
              icon: const Icon(Icons.restore),
              label: const Text('إعادة تعيين الإعدادات'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedToolsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.engineering, color: Colors.indigo),
                const SizedBox(width: 8),
                Text(
                  'أدوات متقدمة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.analytics),
              title: const Text('تصدير تقرير الكاش'),
              subtitle: const Text('إنشاء تقرير مفصل عن استخدام الكاش'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                _showInfoDialog('تصدير التقرير', 'هذه الميزة ستكون متاحة قريباً');
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.folder_open),
              title: const Text('فتح مجلد الكاش'),
              subtitle: const Text('عرض ملفات الكاش في مدير الملفات'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                _showInfoDialog('مجلد الكاش', 'هذه الميزة ستكون متاحة قريباً');
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showInfoDialog(String title, String content) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
