import 'package:flutter/material.dart';
import '../../models/cache_settings_model.dart';
import '../../services/attachment_cache_service.dart';
import '../../services/video_thumbnail_service.dart';

/// شاشة إعدادات الكاش المتقدمة
class CacheSettingsScreen extends StatefulWidget {
  const CacheSettingsScreen({super.key});

  @override
  State<CacheSettingsScreen> createState() => _CacheSettingsScreenState();
}

class _CacheSettingsScreenState extends State<CacheSettingsScreen> {
  late CacheSettingsModel _settings;
  CacheStatsModel? _stats;
  bool _isLoading = true;
  bool _isSaving = false;
  bool _isClearing = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() => _isLoading = true);
    
    try {
      _settings = await CacheSettingsService.instance.loadSettings();
      await _loadCacheStats();
    } catch (e) {
      _showErrorSnackBar('فشل في تحميل الإعدادات: $e');
    }
    
    setState(() => _isLoading = false);
  }

  Future<void> _loadCacheStats() async {
    try {
      final cacheInfo = await AttachmentCacheService.instance.getCacheInfo();
      final videoThumbnailSize = await VideoThumbnailService.instance.getThumbnailCacheSize();
      
      _stats = CacheStatsModel(
        totalFiles: cacheInfo.fileCount,
        totalSizeBytes: cacheInfo.totalSize,
        thumbnailFiles: 0, // يمكن تحسينه لاحقاً
        thumbnailSizeBytes: 0,
        videoThumbnailFiles: 0,
        videoThumbnailSizeBytes: videoThumbnailSize,
        lastCleanup: DateTime.now(),
        oldestFileAgeInDays: 0,
      );
    } catch (e) {
      print('Error loading cache stats: $e');
    }
  }

  Future<void> _saveSettings() async {
    setState(() => _isSaving = true);
    
    try {
      await CacheSettingsService.instance.saveSettings(_settings);
      _showSuccessSnackBar('تم حفظ الإعدادات بنجاح');
    } catch (e) {
      _showErrorSnackBar('فشل في حفظ الإعدادات: $e');
    }
    
    setState(() => _isSaving = false);
  }

  Future<void> _clearCache() async {
    final confirmed = await _showConfirmDialog(
      'مسح الكاش',
      'هل أنت متأكد من مسح جميع الملفات المخزنة مؤقتاً؟\nسيتم إعادة تحميلها عند الحاجة.',
    );
    
    if (!confirmed) return;
    
    setState(() => _isClearing = true);
    
    try {
      await AttachmentCacheService.instance.clearAllCache();
      await VideoThumbnailService.instance.clearThumbnailCache();
      await _loadCacheStats();
      _showSuccessSnackBar('تم مسح الكاش بنجاح');
    } catch (e) {
      _showErrorSnackBar('فشل في مسح الكاش: $e');
    }
    
    setState(() => _isClearing = false);
  }

  Future<void> _resetSettings() async {
    final confirmed = await _showConfirmDialog(
      'إعادة تعيين الإعدادات',
      'هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟',
    );
    
    if (!confirmed) return;
    
    try {
      await CacheSettingsService.instance.resetToDefaults();
      _settings = await CacheSettingsService.instance.loadSettings();
      setState(() {});
      _showSuccessSnackBar('تم إعادة تعيين الإعدادات بنجاح');
    } catch (e) {
      _showErrorSnackBar('فشل في إعادة تعيين الإعدادات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات الكاش'),
        actions: [
          if (_isSaving)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          else
            IconButton(
              onPressed: _saveSettings,
              icon: const Icon(Icons.save),
              tooltip: 'حفظ الإعدادات',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildStatsSection(),
                  const SizedBox(height: 24),
                  _buildSizeSettings(),
                  const SizedBox(height: 24),
                  _buildRetentionSettings(),
                  const SizedBox(height: 24),
                  _buildThumbnailSettings(),
                  const SizedBox(height: 24),
                  _buildActionsSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildStatsSection() {
    if (_stats == null) return const SizedBox.shrink();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.analytics, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'إحصائيات الكاش',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatRow('إجمالي الملفات', '${_stats!.totalFiles}'),
            _buildStatRow('الحجم الإجمالي', '${_stats!.totalSizeMB.toStringAsFixed(1)} ميجابايت'),
            _buildStatRow('thumbnails الفيديوهات', '${(_stats!.videoThumbnailSizeBytes / 1024 / 1024).toStringAsFixed(1)} ميجابايت'),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: _stats!.getUsagePercentage(CacheSettingsService.instance.maxCacheSizeBytes) / 100,
              backgroundColor: Colors.grey.shade300,
              valueColor: AlwaysStoppedAnimation<Color>(
                _stats!.getUsagePercentage(CacheSettingsService.instance.maxCacheSizeBytes) > 80
                    ? Colors.red
                    : Colors.blue,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'استخدام الكاش: ${_stats!.getUsagePercentage(CacheSettingsService.instance.maxCacheSizeBytes).toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildSizeSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.storage, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  'إعدادات الحجم',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text('الحد الأقصى لحجم الكاش: ${_settings.maxCacheSizeMB} ميجابايت'),
            Slider(
              value: _settings.maxCacheSizeMB.toDouble(),
              min: 50,
              max: 1000,
              divisions: 19,
              label: '${_settings.maxCacheSizeMB} MB',
              onChanged: (value) {
                setState(() {
                  _settings = _settings.copyWith(maxCacheSizeMB: value.round());
                });
              },
            ),
            const SizedBox(height: 8),
            SwitchListTile(
              title: const Text('التنظيف التلقائي'),
              subtitle: const Text('مسح الملفات القديمة تلقائياً عند امتلاء الكاش'),
              value: _settings.autoCleanup,
              onChanged: (value) {
                setState(() {
                  _settings = _settings.copyWith(autoCleanup: value);
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRetentionSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.schedule, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'إعدادات الاحتفاظ',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text('مدة الاحتفاظ بالملفات: ${_settings.retentionDays} يوم'),
            Slider(
              value: _settings.retentionDays.toDouble(),
              min: 1,
              max: 30,
              divisions: 29,
              label: '${_settings.retentionDays} يوم',
              onChanged: (value) {
                setState(() {
                  _settings = _settings.copyWith(retentionDays: value.round());
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThumbnailSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.image, color: Colors.purple),
                const SizedBox(width: 8),
                Text(
                  'إعدادات الصور المصغرة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('تفعيل الصور المصغرة'),
              subtitle: const Text('إنشاء صور مصغرة للصور'),
              value: _settings.enableThumbnails,
              onChanged: (value) {
                setState(() {
                  _settings = _settings.copyWith(enableThumbnails: value);
                });
              },
            ),
            SwitchListTile(
              title: const Text('تفعيل صور الفيديوهات المصغرة'),
              subtitle: const Text('استخراج صور مصغرة من الفيديوهات'),
              value: _settings.enableVideoThumbnails,
              onChanged: (value) {
                setState(() {
                  _settings = _settings.copyWith(enableVideoThumbnails: value);
                });
              },
            ),
            if (_settings.enableThumbnails || _settings.enableVideoThumbnails) ...[
              const SizedBox(height: 8),
              Text('جودة الصور المصغرة: ${_settings.thumbnailQuality}%'),
              Slider(
                value: _settings.thumbnailQuality.toDouble(),
                min: 30,
                max: 100,
                divisions: 7,
                label: '${_settings.thumbnailQuality}%',
                onChanged: (value) {
                  setState(() {
                    _settings = _settings.copyWith(thumbnailQuality: value.round());
                  });
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.settings, color: Colors.red),
                const SizedBox(width: 8),
                Text(
                  'إجراءات',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isClearing ? null : _clearCache,
                icon: _isClearing
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.clear_all),
                label: Text(_isClearing ? 'جاري المسح...' : 'مسح الكاش'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _resetSettings,
                icon: const Icon(Icons.restore),
                label: const Text('إعادة تعيين الإعدادات'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<bool> _showConfirmDialog(String title, String content) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
