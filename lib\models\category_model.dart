class Category {
  final String id;
  final String nameAr;
  final String nameEn;
  final String? description;
  final String? iconName;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Category({
    required this.id,
    required this.nameAr,
    required this.nameEn,
    this.description,
    this.iconName,
    required this.createdAt,
    this.updatedAt,
  });

  factory Category.fromMap(Map<String, dynamic> map, String id) {
    return Category(
      id: id,
      nameAr: map['nameAr'] ?? '',
      nameEn: map['nameEn'] ?? '',
      description: map['description'],
      iconName: map['iconName'],
      createdAt: map['createdAt']?.toDate() ?? DateTime.now(),
      updatedAt: map['updatedAt']?.toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'nameAr': nameAr,
      'nameEn': nameEn,
      'description': description,
      'iconName': iconName,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  Category copyWith({
    String? id,
    String? nameAr,
    String? nameEn,
    String? description,
    String? iconName,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Category(
      id: id ?? this.id,
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      description: description ?? this.description,
      iconName: iconName ?? this.iconName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String getDisplayName(bool isRTL) {
    return isRTL ? nameAr : nameEn;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Category && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Category(id: $id, nameAr: $nameAr, nameEn: $nameEn)';
  }
}
