import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../../providers/remote_config_provider.dart';
import '../../providers/locale_provider.dart';

class AppUpdateManagementScreen extends StatefulWidget {
  const AppUpdateManagementScreen({super.key});

  @override
  State<AppUpdateManagementScreen> createState() => _AppUpdateManagementScreenState();
}

class _AppUpdateManagementScreenState extends State<AppUpdateManagementScreen> {
  String _currentVersion = '';

  @override
  void initState() {
    super.initState();
    _loadCurrentVersion();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadRemoteConfig();
    });
  }

  Future<void> _loadCurrentVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      _currentVersion = packageInfo.version;
    });
  }

  Future<void> _loadRemoteConfig() async {
    final provider = Provider.of<RemoteConfigProvider>(context, listen: false);
    if (!provider.isInitialized) {
      await provider.initialize();
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isRTL ? 'إدارة تحديثات التطبيق' : 'App Update Management',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshConfig,
            tooltip: isRTL ? 'تحديث التكوين' : 'Refresh Config',
          ),
        ],
      ),
      body: Consumer<RemoteConfigProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildCurrentStatusCard(provider, isRTL),
                const SizedBox(height: 16),
                _buildUpdateConfigCard(provider, isRTL),
                const SizedBox(height: 16),
                _buildQuickActionsCard(provider, isRTL),
                const SizedBox(height: 16),
                _buildConfigurationGuideCard(isRTL),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCurrentStatusCard(RemoteConfigProvider provider, bool isRTL) {
    final updateEnabled = provider.getBool('update_enabled');
    final updateRequired = provider.getBool('update_required');
    final maintenanceMode = provider.getBool('maintenance_mode');
    final latestVersion = provider.getString('latest_version');
    final minimumVersion = provider.getString('minimum_version');

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue[600],
                ),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'حالة التحديث الحالية' : 'Current Update Status',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatusRow(
              isRTL ? 'الإصدار الحالي' : 'Current Version',
              _currentVersion,
              Colors.blue,
              isRTL,
            ),
            _buildStatusRow(
              isRTL ? 'أحدث إصدار' : 'Latest Version',
              latestVersion.isNotEmpty ? latestVersion : 'Not set',
              latestVersion.isNotEmpty ? Colors.green : Colors.orange,
              isRTL,
            ),
            _buildStatusRow(
              isRTL ? 'الحد الأدنى للإصدار' : 'Minimum Version',
              minimumVersion.isNotEmpty ? minimumVersion : 'Not set',
              minimumVersion.isNotEmpty ? Colors.green : Colors.orange,
              isRTL,
            ),
            _buildStatusRow(
              isRTL ? 'التحديث مفعل' : 'Update Enabled',
              updateEnabled ? (isRTL ? 'مفعل' : 'Enabled') : (isRTL ? 'معطل' : 'Disabled'),
              updateEnabled ? Colors.green : Colors.red,
              isRTL,
            ),
            _buildStatusRow(
              isRTL ? 'التحديث مطلوب' : 'Update Required',
              updateRequired ? (isRTL ? 'مطلوب' : 'Required') : (isRTL ? 'اختياري' : 'Optional'),
              updateRequired ? Colors.red : Colors.green,
              isRTL,
            ),
            _buildStatusRow(
              isRTL ? 'وضع الصيانة' : 'Maintenance Mode',
              maintenanceMode ? (isRTL ? 'مفعل' : 'Active') : (isRTL ? 'معطل' : 'Inactive'),
              maintenanceMode ? Colors.red : Colors.green,
              isRTL,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value, Color color, bool isRTL) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: color.withValues(alpha: 0.3)),
            ),
            child: Text(
              value,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpdateConfigCard(RemoteConfigProvider provider, bool isRTL) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.settings,
                  color: Colors.orange[600],
                ),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'إعدادات التحديث' : 'Update Configuration',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              isRTL
                ? 'المفاتيح المطلوبة في Firebase Remote Config:'
                : 'Required keys in Firebase Remote Config:',
              style: TextStyle(color: Colors.grey[700]),
            ),
            const SizedBox(height: 12),
            ..._getUpdateConfigKeys(isRTL).entries.map((entry) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        entry.key,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontFamily: 'monospace',
                          fontSize: 12,
                        ),
                      ),
                      Text(
                        provider.getString(entry.key).isNotEmpty || provider.getBool(entry.key)
                          ? (isRTL ? 'مُعيَّن' : 'Set')
                          : (isRTL ? 'غير مُعيَّن' : 'Not Set'),
                        style: TextStyle(
                          color: provider.getString(entry.key).isNotEmpty || provider.getBool(entry.key)
                            ? Colors.green
                            : Colors.red,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  Text(
                    entry.value,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 11,
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsCard(RemoteConfigProvider provider, bool isRTL) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.flash_on,
                  color: Colors.red[600],
                ),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'إجراءات سريعة' : 'Quick Actions',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              isRTL
                ? 'استخدم هذه الإجراءات لتطبيق إعدادات شائعة بسرعة:'
                : 'Use these actions to quickly apply common settings:',
              style: TextStyle(color: Colors.grey[700]),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildActionButton(
                  isRTL ? 'إجبار التحديث' : 'Force Update',
                  Icons.system_update_alt,
                  Colors.red,
                  () => _showForceUpdateDialog(isRTL),
                ),
                _buildActionButton(
                  isRTL ? 'تحديث اختياري' : 'Optional Update',
                  Icons.update,
                  Colors.orange,
                  () => _showOptionalUpdateDialog(isRTL),
                ),
                _buildActionButton(
                  isRTL ? 'وضع الصيانة' : 'Maintenance Mode',
                  Icons.build,
                  Colors.purple,
                  () => _showMaintenanceModeDialog(isRTL),
                ),
                _buildActionButton(
                  isRTL ? 'تعطيل التحديث' : 'Disable Updates',
                  Icons.block,
                  Colors.grey,
                  () => _showDisableUpdatesDialog(isRTL),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(String label, IconData icon, Color color, VoidCallback onPressed) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  Widget _buildConfigurationGuideCard(bool isRTL) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.help_outline,
                  color: Colors.blue[600],
                ),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'دليل التكوين' : 'Configuration Guide',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._getConfigurationSteps(isRTL).map((step) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '• ',
                    style: TextStyle(
                      color: Colors.blue[600],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      step,
                      style: TextStyle(color: Colors.grey[700]),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Map<String, String> _getUpdateConfigKeys(bool isRTL) {
    return {
      'update_enabled': isRTL ? 'تفعيل/تعطيل فحص التحديثات' : 'Enable/disable update checking',
      'update_required': isRTL ? 'جعل التحديث إجباري' : 'Make update mandatory',
      'latest_version': isRTL ? 'أحدث إصدار متاح' : 'Latest available version',
      'minimum_version': isRTL ? 'الحد الأدنى للإصدار المدعوم' : 'Minimum supported version',
      'update_url_android': isRTL ? 'رابط متجر Google Play' : 'Google Play Store URL',
      'update_url_ios': isRTL ? 'رابط App Store' : 'App Store URL',
      'maintenance_mode': isRTL ? 'تفعيل وضع الصيانة' : 'Enable maintenance mode',
      'force_update_message_ar': isRTL ? 'رسالة التحديث الإجباري بالعربية' : 'Force update message in Arabic',
      'force_update_message_en': isRTL ? 'رسالة التحديث الإجباري بالإنجليزية' : 'Force update message in English',
    };
  }

  List<String> _getConfigurationSteps(bool isRTL) {
    if (isRTL) {
      return [
        'انتقل إلى Firebase Console > Remote Config',
        'أضف المفاتيح المطلوبة مع القيم المناسبة',
        'انشر التكوين لتطبيق التغييرات',
        'استخدم الإجراءات السريعة لتطبيق إعدادات شائعة',
        'راقب حالة التحديث من خلال هذه الشاشة',
      ];
    } else {
      return [
        'Go to Firebase Console > Remote Config',
        'Add required keys with appropriate values',
        'Publish configuration to apply changes',
        'Use quick actions to apply common settings',
        'Monitor update status through this screen',
      ];
    }
  }

  Future<void> _refreshConfig() async {
    final provider = Provider.of<RemoteConfigProvider>(context, listen: false);
    await provider.fetchAndActivate();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            Provider.of<LocaleProvider>(context, listen: false).isRTL
                ? 'تم تحديث التكوين البعيد'
                : 'Remote config refreshed',
          ),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _showForceUpdateDialog(bool isRTL) {
    _showConfigDialog(
      isRTL ? 'إجبار التحديث' : 'Force Update',
      isRTL
        ? 'سيتم إجبار جميع المستخدمين على التحديث. هل تريد المتابعة؟'
        : 'This will force all users to update. Do you want to continue?',
      isRTL,
      () => _showConfigInstructions('force_update', isRTL),
    );
  }

  void _showOptionalUpdateDialog(bool isRTL) {
    _showConfigDialog(
      isRTL ? 'تحديث اختياري' : 'Optional Update',
      isRTL
        ? 'سيتم إظهار تحديث اختياري للمستخدمين. هل تريد المتابعة؟'
        : 'This will show an optional update to users. Do you want to continue?',
      isRTL,
      () => _showConfigInstructions('optional_update', isRTL),
    );
  }

  void _showMaintenanceModeDialog(bool isRTL) {
    _showConfigDialog(
      isRTL ? 'وضع الصيانة' : 'Maintenance Mode',
      isRTL
        ? 'سيتم منع جميع المستخدمين من استخدام التطبيق. هل تريد المتابعة؟'
        : 'This will prevent all users from using the app. Do you want to continue?',
      isRTL,
      () => _showConfigInstructions('maintenance', isRTL),
    );
  }

  void _showDisableUpdatesDialog(bool isRTL) {
    _showConfigDialog(
      isRTL ? 'تعطيل التحديث' : 'Disable Updates',
      isRTL
        ? 'سيتم تعطيل فحص التحديثات. هل تريد المتابعة؟'
        : 'This will disable update checking. Do you want to continue?',
      isRTL,
      () => _showConfigInstructions('disable_updates', isRTL),
    );
  }

  void _showConfigDialog(String title, String message, bool isRTL, VoidCallback onConfirm) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              onConfirm();
            },
            child: Text(isRTL ? 'متابعة' : 'Continue'),
          ),
        ],
      ),
    );
  }

  void _showConfigInstructions(String type, bool isRTL) {
    final instructions = _getConfigInstructions(type, isRTL);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'تعليمات التكوين' : 'Configuration Instructions'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: instructions.entries.map((entry) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    entry.key,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontFamily: 'monospace',
                    ),
                  ),
                  Text(
                    entry.value.toString(),
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ],
              ),
            )).toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'إغلاق' : 'Close'),
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _getConfigInstructions(String type, bool isRTL) {
    switch (type) {
      case 'force_update':
        return {
          'update_enabled': true,
          'update_required': true,
          'latest_version': '1.0.2',
          'minimum_version': '1.0.2',
        };
      case 'optional_update':
        return {
          'update_enabled': true,
          'update_required': false,
          'latest_version': '1.0.2',
          'minimum_version': '1.0.0',
        };
      case 'maintenance':
        return {
          'maintenance_mode': true,
          'maintenance_message_ar': 'التطبيق تحت الصيانة حالياً. يرجى المحاولة لاحقاً.',
          'maintenance_message_en': 'The app is currently under maintenance. Please try again later.',
        };
      case 'disable_updates':
        return {
          'update_enabled': false,
        };
      default:
        return {};
    }
  }
}
