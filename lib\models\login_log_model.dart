class LoginLog {
  final String id;
  final String userId;
  final String userEmail;
  final String deviceInfo;
  final String ipAddress;
  final String location;
  final DateTime timestamp;
  final bool success;

  LoginLog({
    required this.id,
    required this.userId,
    required this.userEmail,
    required this.deviceInfo,
    required this.ipAddress,
    required this.location,
    required this.timestamp,
    required this.success,
  });

  factory LoginLog.fromMap(String id, Map<String, dynamic> map) {
    return LoginLog(
      id: id,
      userId: map['userId'] ?? '',
      userEmail: map['userEmail'] ?? '',
      deviceInfo: map['deviceInfo'] ?? '',
      ipAddress: map['ipAddress'] ?? '',
      location: map['location'] ?? '',
      timestamp: map['timestamp']?.toDate() ?? DateTime.now(),
      success: map['success'] ?? false,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'userEmail': userEmail,
      'deviceInfo': deviceInfo,
      'ipAddress': ipAddress,
      'location': location,
      'timestamp': timestamp,
      'success': success,
    };
  }
}
