import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_settings_model.dart';

class UserSettingsProvider with ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  UserSettings? _settings;
  bool _isLoading = false;
  String? _error;

  UserSettings? get settings => _settings;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Initialize settings for a user
  Future<void> initializeSettings(String userId) async {
    _setLoading(true);
    _clearError();

    try {
      // Try to load from Firestore first
      final doc = await _firestore
          .collection('userSettings')
          .doc(userId)
          .get();

      if (doc.exists) {
        _settings = UserSettings.fromFirestore(doc);
      } else {
        // Create default settings
        _settings = UserSettings(
          userId: userId,
          lastUpdated: DateTime.now(),
        );
        // Save default settings to Firestore
        await _saveSettingsToFirestore();
      }

      // Apply settings to local providers
      await _applySettings();

      notifyListeners();
    } catch (e) {
      _setError(e.toString());
      // Fallback to default settings
      _settings = UserSettings(
        userId: userId,
        lastUpdated: DateTime.now(),
      );
      notifyListeners();
    } finally {
      _setLoading(false);
    }
  }

  // Update language setting
  Future<void> updateLanguage(String language) async {
    if (_settings == null) return;

    _setLoading(true);
    _clearError();

    try {
      _settings = _settings!.copyWith(
        language: language,
        lastUpdated: DateTime.now(),
      );

      await _saveSettingsToFirestore();
      await _saveToLocalStorage();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Update theme setting
  Future<void> updateTheme(String theme) async {
    if (_settings == null) return;

    _setLoading(true);
    _clearError();

    try {
      _settings = _settings!.copyWith(
        theme: theme,
        lastUpdated: DateTime.now(),
      );

      await _saveSettingsToFirestore();
      await _saveToLocalStorage();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Update remember me setting
  Future<void> updateRememberMe(bool rememberMe) async {
    if (_settings == null) return;

    _setLoading(true);
    _clearError();

    try {
      _settings = _settings!.copyWith(
        rememberMe: rememberMe,
        lastUpdated: DateTime.now(),
      );

      await _saveSettingsToFirestore();
      await _saveToLocalStorage();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Update default storage service
  Future<void> updateDefaultStorageService(String service) async {
    if (_settings == null) return;

    _setLoading(true);
    _clearError();

    try {
      _settings = _settings!.copyWith(
        defaultStorageService: service,
        lastUpdated: DateTime.now(),
      );

      await _saveSettingsToFirestore();
      await _saveToLocalStorage();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Update notifications setting
  Future<void> updateNotifications(bool enabled) async {
    if (_settings == null) return;

    _setLoading(true);
    _clearError();

    try {
      _settings = _settings!.copyWith(
        enableNotifications: enabled,
        lastUpdated: DateTime.now(),
      );

      await _saveSettingsToFirestore();
      await _saveToLocalStorage();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Update auto backup setting
  Future<void> updateAutoBackup(bool enabled) async {
    if (_settings == null) return;

    _setLoading(true);
    _clearError();

    try {
      _settings = _settings!.copyWith(
        autoBackup: enabled,
        lastUpdated: DateTime.now(),
      );

      await _saveSettingsToFirestore();
      await _saveToLocalStorage();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Update cache enabled setting
  Future<void> updateCacheEnabled(bool enabled) async {
    if (_settings == null) return;

    _setLoading(true);
    _clearError();

    try {
      _settings = _settings!.copyWith(
        enableCache: enabled,
        lastUpdated: DateTime.now(),
      );

      await _saveSettingsToFirestore();
      await _saveToLocalStorage();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Update max cache size
  Future<void> updateMaxCacheSize(int sizeMB) async {
    if (_settings == null) return;

    _setLoading(true);
    _clearError();

    try {
      _settings = _settings!.copyWith(
        maxCacheSizeMB: sizeMB,
        lastUpdated: DateTime.now(),
      );

      await _saveSettingsToFirestore();
      await _saveToLocalStorage();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Update auto cleanup cache setting
  Future<void> updateAutoCleanupCache(bool enabled) async {
    if (_settings == null) return;

    _setLoading(true);
    _clearError();

    try {
      _settings = _settings!.copyWith(
        autoCleanupCache: enabled,
        lastUpdated: DateTime.now(),
      );

      await _saveSettingsToFirestore();
      await _saveToLocalStorage();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Update cache retention days
  Future<void> updateCacheRetentionDays(int days) async {
    if (_settings == null) return;

    _setLoading(true);
    _clearError();

    try {
      _settings = _settings!.copyWith(
        cacheRetentionDays: days,
        lastUpdated: DateTime.now(),
      );

      await _saveSettingsToFirestore();
      await _saveToLocalStorage();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Update cache file types
  Future<void> updateCacheFileTypes({
    bool? images,
    bool? videos,
    bool? documents,
  }) async {
    if (_settings == null) return;

    _setLoading(true);
    _clearError();

    try {
      _settings = _settings!.copyWith(
        cacheImages: images ?? _settings!.cacheImages,
        cacheVideos: videos ?? _settings!.cacheVideos,
        cacheDocuments: documents ?? _settings!.cacheDocuments,
        lastUpdated: DateTime.now(),
      );

      await _saveSettingsToFirestore();
      await _saveToLocalStorage();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Save settings to Firestore
  Future<void> _saveSettingsToFirestore() async {
    if (_settings == null) return;

    try {
      await _firestore
          .collection('userSettings')
          .doc(_settings!.userId)
          .set(_settings!.toMap(), SetOptions(merge: true));
    } catch (e) {
      throw Exception('Failed to save settings to cloud: $e');
    }
  }

  // Save critical settings to local storage for offline access
  Future<void> _saveToLocalStorage() async {
    if (_settings == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_language', _settings!.language);
      await prefs.setString('user_theme', _settings!.theme);
      await prefs.setBool('user_remember_me', _settings!.rememberMe);
      await prefs.setString('user_storage_service', _settings!.defaultStorageService);
    } catch (e) {
      // Local storage errors are not critical
      debugPrint('Failed to save to local storage: $e');
    }
  }

  // Load settings from local storage (for offline use)
  Future<UserSettings?> loadFromLocalStorage(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      return UserSettings(
        userId: userId,
        language: prefs.getString('user_language') ?? 'ar',
        theme: prefs.getString('user_theme') ?? 'system',
        rememberMe: prefs.getBool('user_remember_me') ?? false,
        defaultStorageService: prefs.getString('user_storage_service') ?? 'google_drive',
        lastUpdated: DateTime.now(),
      );
    } catch (e) {
      debugPrint('Failed to load from local storage: $e');
      return null;
    }
  }

  // Apply settings to other providers
  Future<void> _applySettings() async {
    if (_settings == null) return;

    // Save to local storage for quick access
    await _saveToLocalStorage();
  }

  // Reset settings to default
  Future<void> resetToDefaults() async {
    if (_settings == null) return;

    _setLoading(true);
    _clearError();

    try {
      _settings = UserSettings(
        userId: _settings!.userId,
        lastUpdated: DateTime.now(),
      );

      await _saveSettingsToFirestore();
      await _saveToLocalStorage();
      await _applySettings();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Helper methods
  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  // Clear settings (on logout)
  void clearSettings() {
    _settings = null;
    _error = null;
    _isLoading = false;
    notifyListeners();
  }
}