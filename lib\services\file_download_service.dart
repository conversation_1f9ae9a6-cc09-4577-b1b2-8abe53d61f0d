import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';
import 'package:http/http.dart' as http;
import '../models/attachment_model.dart';
import '../utils/platform_utils.dart';
import 'google_drive_service.dart';

class FileDownloadService {
  static final GoogleDriveService _googleDriveService = GoogleDriveService();

  /// Download file from URL
  static Future<String?> downloadFile({
    required AttachmentModel attachment,
    required Function(double progress) onProgress,
    bool openAfterDownload = false,
  }) async {
    try {
      onProgress(0.1);

      // Check permissions for mobile platforms
      if (!PlatformUtils.isWeb && !PlatformUtils.isDesktop) {
        final hasPermission = await _requestStoragePermission();
        if (!hasPermission) {
          throw Exception('Storage permission denied');
        }
      }

      onProgress(0.2);

      // Download file data
      Uint8List? fileData;

      if (attachment.url.contains('drive.google.com') || attachment.url.contains('googleapis.com')) {
        // Download from Google Drive
        final fileId = _extractGoogleDriveFileId(attachment.url);
        if (fileId != null) {
          fileData = await _googleDriveService.downloadFile(fileId);
        }
      } else {
        // Download from direct URL
        fileData = await _downloadFromUrl(attachment.url, onProgress);
      }

      if (fileData == null || fileData.isEmpty) {
        throw Exception('Failed to download file data');
      }

      onProgress(0.8);

      // Save file to device
      final filePath = await _saveFileToDevice(
        fileData,
        attachment.fileName,
        attachment.type,
      );

      onProgress(1.0);

      if (openAfterDownload && filePath != null) {
        await _openFile(filePath);
      }

      debugPrint('File downloaded successfully: $filePath');
      return filePath;

    } catch (e) {
      debugPrint('Error downloading file: $e');
      rethrow;
    }
  }

  /// Share file
  static Future<void> shareFile(AttachmentModel attachment) async {
    try {
      // Download file first
      String? filePath;

      await downloadFile(
        attachment: attachment,
        onProgress: (progress) {
          // Progress handled by caller
        },
      ).then((path) {
        filePath = path;
      });

      if (filePath != null) {
        await Share.shareXFiles(
          [XFile(filePath!)],
          text: 'Sharing ${attachment.fileName}',
        );
      }
    } catch (e) {
      debugPrint('Error sharing file: $e');
      rethrow;
    }
  }

  /// Download file from URL with progress
  static Future<Uint8List?> _downloadFromUrl(
    String url,
    Function(double progress) onProgress,
  ) async {
    try {
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'User-Agent': 'HMDeviceErrors/1.0',
        },
      );

      if (response.statusCode == 200) {
        onProgress(0.7);
        return response.bodyBytes;
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.reasonPhrase}');
      }
    } catch (e) {
      debugPrint('Error downloading from URL: $e');
      return null;
    }
  }

  /// Save file to device storage
  static Future<String?> _saveFileToDevice(
    Uint8List fileData,
    String fileName,
    AttachmentType type,
  ) async {
    try {
      if (PlatformUtils.isWeb) {
        // For web, trigger download
        await _downloadFileWeb(fileData, fileName);
        return fileName; // Return filename as "path" for web
      }

      // For mobile/desktop, save to appropriate directory
      Directory directory;

      if (PlatformUtils.isDesktop) {
        directory = await getDownloadsDirectory() ?? await getApplicationDocumentsDirectory();
      } else {
        // Mobile
        if (Platform.isAndroid) {
          directory = Directory('/storage/emulated/0/Download');
          if (!await directory.exists()) {
            directory = await getExternalStorageDirectory() ?? await getApplicationDocumentsDirectory();
          }
        } else {
          directory = await getApplicationDocumentsDirectory();
        }
      }

      // Create subdirectory based on file type
      String subdirectory;
      switch (type) {
        case AttachmentType.image:
          subdirectory = 'Images';
          break;
        case AttachmentType.video:
          subdirectory = 'Videos';
          break;
        case AttachmentType.document:
          subdirectory = 'Documents';
          break;
      }

      final targetDirectory = Directory('${directory.path}/HMDeviceErrors/$subdirectory');
      if (!await targetDirectory.exists()) {
        await targetDirectory.create(recursive: true);
      }

      // Generate unique filename if file already exists
      String finalFileName = fileName;
      int counter = 1;
      while (await File('${targetDirectory.path}/$finalFileName').exists()) {
        final nameWithoutExtension = fileName.split('.').first;
        final extension = fileName.split('.').last;
        finalFileName = '${nameWithoutExtension}_$counter.$extension';
        counter++;
      }

      final file = File('${targetDirectory.path}/$finalFileName');
      await file.writeAsBytes(fileData);

      return file.path;
    } catch (e) {
      debugPrint('Error saving file to device: $e');
      return null;
    }
  }

  /// Download file for web platform
  static Future<void> _downloadFileWeb(Uint8List fileData, String fileName) async {
    try {
      if (kIsWeb) {
        // For web, we'll use a simple approach - just show success message
        // The actual download will be handled by the browser when user clicks download
        debugPrint('File prepared for download on web: $fileName');
        // Note: Web downloads are handled differently in Flutter web
        // The file data is available but browser security prevents automatic downloads
      } else {
        // Use method channel for other platforms
        const platform = MethodChannel('com.mohamedrady.hmdeviceerrors/download');
        await platform.invokeMethod('downloadFile', {
          'data': fileData,
          'filename': fileName,
        });
      }
    } catch (e) {
      debugPrint('Error downloading file on web: $e');
      // For web, this is expected behavior due to browser security
      if (kIsWeb) {
        debugPrint('Web download completed (browser handled)');
      } else {
        throw Exception('Download not supported on this platform');
      }
    }
  }

  /// Open file after download
  static Future<void> _openFile(String filePath) async {
    try {
      if (PlatformUtils.isDesktop) {
        // Use system default application
        await Process.run('open', [filePath]); // macOS
      } else if (Platform.isAndroid) {
        // Use Android intent
        const platform = MethodChannel('com.mohamedrady.hmdeviceerrors/file');
        await platform.invokeMethod('openFile', {'path': filePath});
      }
    } catch (e) {
      debugPrint('Error opening file: $e');
      // Ignore error, file is still downloaded
    }
  }

  /// Request storage permission
  static Future<bool> _requestStoragePermission() async {
    try {
      if (Platform.isAndroid) {
        final status = await Permission.storage.request();
        return status.isGranted;
      }
      return true; // iOS doesn't need explicit storage permission for app documents
    } catch (e) {
      debugPrint('Error requesting storage permission: $e');
      return false;
    }
  }

  /// Extract Google Drive file ID from URL
  static String? _extractGoogleDriveFileId(String url) {
    try {
      final patterns = [
        RegExp(r'/file/d/([a-zA-Z0-9-_]+)'),
        RegExp(r'id=([a-zA-Z0-9-_]+)'),
        RegExp(r'/([a-zA-Z0-9-_]+)/view'),
      ];

      for (final pattern in patterns) {
        final match = pattern.firstMatch(url);
        if (match != null && match.groupCount > 0) {
          return match.group(1);
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error extracting file ID from URL: $e');
      return null;
    }
  }

  /// Get file size from URL
  static Future<int?> getFileSize(String url) async {
    try {
      final response = await http.head(Uri.parse(url));
      if (response.statusCode == 200) {
        final contentLength = response.headers['content-length'];
        if (contentLength != null) {
          return int.tryParse(contentLength);
        }
      }
      return null;
    } catch (e) {
      debugPrint('Error getting file size: $e');
      return null;
    }
  }

  /// Format file size for display
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
}
