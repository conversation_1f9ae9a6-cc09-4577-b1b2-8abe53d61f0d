import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';

class AppSizeAnalyzer {
  static const String _reportFileName = 'app_size_report.json';

  /// Analyze app size and generate report
  static Future<Map<String, dynamic>> analyzeAppSize() async {
    try {
      final report = <String, dynamic>{};

      // Get app directories
      final appDir = await getApplicationDocumentsDirectory();
      final cacheDir = await getTemporaryDirectory();
      final supportDir = await getApplicationSupportDirectory();

      // Calculate directory sizes
      final appSize = await _calculateDirectorySize(appDir);
      final cacheSize = await _calculateDirectorySize(cacheDir);
      final supportSize = await _calculateDirectorySize(supportDir);

      // Get asset information
      final assetInfo = await _analyzeAssets();

      // Get dependency information
      final dependencyInfo = await _analyzeDependencies();

      // Build the report
      report['timestamp'] = DateTime.now().toIso8601String();
      report['app_size'] = {
        'documents': appSize,
        'cache': cacheSize,
        'support': supportSize,
        'total': appSize + cacheSize + supportSize,
        'formatted': {
          'documents': _formatBytes(appSize),
          'cache': _formatBytes(cacheSize),
          'support': _formatBytes(supportSize),
          'total': _formatBytes(appSize + cacheSize + supportSize),
        }
      };

      report['assets'] = assetInfo;
      report['dependencies'] = dependencyInfo;
      report['optimization_suggestions'] = _generateOptimizationSuggestions(report);

      // Save report
      await _saveReport(report);

      return report;
    } catch (e) {
      debugPrint('Error analyzing app size: $e');
      return {};
    }
  }

  /// Calculate directory size recursively
  static Future<int> _calculateDirectorySize(Directory directory) async {
    int size = 0;
    try {
      if (await directory.exists()) {
        await for (final entity in directory.list(recursive: true)) {
          if (entity is File) {
            final stat = await entity.stat();
            size += stat.size;
          }
        }
      }
    } catch (e) {
      debugPrint('Error calculating directory size for ${directory.path}: $e');
    }
    return size;
  }

  /// Analyze assets
  static Future<Map<String, dynamic>> _analyzeAssets() async {
    try {
      final assetInfo = <String, dynamic>{};
      final assetCategories = <String, int>{};
      int totalAssetSize = 0;

      // This is a simplified analysis - in a real implementation,
      // you would parse the pubspec.yaml and analyze actual asset files

      // Estimate common asset sizes
      assetCategories['images'] = 2 * 1024 * 1024; // 2MB estimated
      assetCategories['fonts'] = 500 * 1024; // 500KB estimated
      assetCategories['icons'] = 100 * 1024; // 100KB estimated

      totalAssetSize = assetCategories.values.reduce((a, b) => a + b);

      assetInfo['categories'] = assetCategories;
      assetInfo['total_size'] = totalAssetSize;
      assetInfo['formatted_total'] = _formatBytes(totalAssetSize);
      assetInfo['formatted_categories'] = assetCategories.map(
        (key, value) => MapEntry(key, _formatBytes(value)),
      );

      return assetInfo;
    } catch (e) {
      debugPrint('Error analyzing assets: $e');
      return {};
    }
  }

  /// Analyze dependencies
  static Future<Map<String, dynamic>> _analyzeDependencies() async {
    try {
      // This is a simplified analysis - in a real implementation,
      // you would parse pubspec.yaml and analyze actual dependencies

      final dependencyInfo = <String, dynamic>{};
      final largeDependencies = <String, String>{};

      // Estimate sizes of major dependencies
      largeDependencies['firebase_core'] = '2.5 MB';
      largeDependencies['cloud_firestore'] = '3.2 MB';
      largeDependencies['firebase_storage'] = '1.8 MB';
      largeDependencies['cached_network_image'] = '1.2 MB';
      largeDependencies['image_picker'] = '2.1 MB';
      largeDependencies['flutter_image_compress'] = '1.5 MB';

      dependencyInfo['large_dependencies'] = largeDependencies;
      dependencyInfo['total_dependencies'] = largeDependencies.length;

      return dependencyInfo;
    } catch (e) {
      debugPrint('Error analyzing dependencies: $e');
      return {};
    }
  }

  /// Generate optimization suggestions
  static List<Map<String, dynamic>> _generateOptimizationSuggestions(Map<String, dynamic> report) {
    final suggestions = <Map<String, dynamic>>[];

    try {
      final appSize = report['app_size'] as Map<String, dynamic>?;
      final cacheSize = appSize?['cache'] as int? ?? 0;
      final totalSize = appSize?['total'] as int? ?? 0;

      // Cache size suggestions (more aggressive)
      if (cacheSize > 30 * 1024 * 1024) { // 30MB (reduced from 50MB)
        suggestions.add({
          'type': 'cache',
          'priority': 'high',
          'title': 'Large Cache Size',
          'description': 'Cache size is ${_formatBytes(cacheSize)}. Consider clearing old cache files.',
          'action': 'Clear cache or implement automatic cache cleanup',
          'estimatedSaving': _formatBytes(cacheSize - 20 * 1024 * 1024),
        });
      }

      // Total size suggestions (more aggressive)
      if (totalSize > 80 * 1024 * 1024) { // 80MB (reduced from 100MB)
        suggestions.add({
          'type': 'size',
          'priority': 'high',
          'title': 'Large App Size',
          'description': 'Total app size is ${_formatBytes(totalSize)}. Consider optimization.',
          'action': 'Optimize images, remove unused dependencies, enable ProGuard',
          'estimatedSaving': _formatBytes((totalSize * 0.3).round()), // Estimate 30% reduction
        });
      }

      // Asset optimization suggestions
      suggestions.add({
        'type': 'assets',
        'priority': 'medium',
        'title': 'Asset Optimization',
        'description': 'Optimize images and assets for smaller size.',
        'action': 'Compress images, use WebP format, remove unused assets',
        'estimatedSaving': '2-5 MB',
      });

      // Dependency suggestions
      suggestions.add({
        'type': 'dependencies',
        'priority': 'medium',
        'title': 'Dependency Optimization',
        'description': 'Review dependencies for optimization opportunities.',
        'action': 'Remove unused dependencies, use lighter alternatives',
        'estimatedSaving': '5-15 MB',
      });

      // ProGuard/R8 suggestions
      suggestions.add({
        'type': 'build',
        'priority': 'high',
        'title': 'Build Optimization',
        'description': 'Enable advanced build optimizations.',
        'action': 'Enable R8 shrinking, obfuscation, and resource shrinking',
        'estimatedSaving': '10-30 MB',
      });

      // Image format suggestions
      suggestions.add({
        'type': 'images',
        'priority': 'medium',
        'title': 'Image Format Optimization',
        'description': 'Convert images to more efficient formats.',
        'action': 'Use WebP for images, reduce image quality for thumbnails',
        'estimatedSaving': '3-8 MB',
      });

      // Architecture-specific builds
      suggestions.add({
        'type': 'architecture',
        'priority': 'low',
        'title': 'Architecture-Specific Builds',
        'description': 'Create separate APKs for different architectures.',
        'action': 'Enable APK splitting by architecture',
        'estimatedSaving': '20-40% per APK',
      });

    } catch (e) {
      debugPrint('Error generating optimization suggestions: $e');
    }

    return suggestions;
  }

  /// Save report to file
  static Future<void> _saveReport(Map<String, dynamic> report) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final reportFile = File('${appDir.path}/$_reportFileName');

      final jsonString = const JsonEncoder.withIndent('  ').convert(report);
      await reportFile.writeAsString(jsonString);

      debugPrint('App size report saved to: ${reportFile.path}');
    } catch (e) {
      debugPrint('Error saving report: $e');
    }
  }

  /// Load previous report
  static Future<Map<String, dynamic>?> loadPreviousReport() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final reportFile = File('${appDir.path}/$_reportFileName');

      if (await reportFile.exists()) {
        final jsonString = await reportFile.readAsString();
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
    } catch (e) {
      debugPrint('Error loading previous report: $e');
    }
    return null;
  }

  /// Compare with previous report
  static Map<String, dynamic> compareWithPrevious(
    Map<String, dynamic> currentReport,
    Map<String, dynamic> previousReport,
  ) {
    final comparison = <String, dynamic>{};

    try {
      final currentSize = currentReport['app_size']?['total'] as int? ?? 0;
      final previousSize = previousReport['app_size']?['total'] as int? ?? 0;

      final sizeDiff = currentSize - previousSize;
      final percentChange = previousSize > 0 ? (sizeDiff / previousSize) * 100 : 0.0;

      comparison['size_change'] = {
        'bytes': sizeDiff,
        'formatted': _formatBytes(sizeDiff.abs()),
        'percent': percentChange,
        'direction': sizeDiff > 0 ? 'increased' : sizeDiff < 0 ? 'decreased' : 'unchanged',
      };

      comparison['current_size'] = _formatBytes(currentSize);
      comparison['previous_size'] = _formatBytes(previousSize);

    } catch (e) {
      debugPrint('Error comparing reports: $e');
    }

    return comparison;
  }

  /// Format bytes to human readable string
  static String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Get optimization score (0-100)
  static int getOptimizationScore(Map<String, dynamic> report) {
    int score = 100;

    try {
      final appSize = report['app_size'] as Map<String, dynamic>?;
      final totalSize = appSize?['total'] as int? ?? 0;
      final cacheSize = appSize?['cache'] as int? ?? 0;

      // Deduct points for large sizes
      if (totalSize > 200 * 1024 * 1024) {
        score -= 30; // 200MB
      } else if (totalSize > 100 * 1024 * 1024) {
        score -= 20; // 100MB
      } else if (totalSize > 50 * 1024 * 1024) {
        score -= 10; // 50MB
      }

      if (cacheSize > 100 * 1024 * 1024) {
        score -= 20; // 100MB cache
      } else if (cacheSize > 50 * 1024 * 1024) {
        score -= 10; // 50MB cache
      }

      // Ensure score is between 0 and 100
      score = score.clamp(0, 100);

    } catch (e) {
      debugPrint('Error calculating optimization score: $e');
      score = 50; // Default score
    }

    return score;
  }
}
