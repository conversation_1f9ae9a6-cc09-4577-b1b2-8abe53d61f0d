@echo off
echo ========================================
echo    Removing Unused Dependencies
echo    Expected size reduction: ~2-4MB
echo ========================================

echo [1/4] Backing up current pubspec.yaml...
copy pubspec.yaml pubspec.yaml.backup

echo [2/4] Removing unnecessary UI packages...
echo Removing flutter_staggered_grid_view...
flutter pub remove flutter_staggered_grid_view

echo Removing flutter_spinkit...
flutter pub remove flutter_spinkit

echo Removing shimmer...
flutter pub remove shimmer

echo [3/4] Cleaning and getting dependencies...
flutter clean
flutter pub get

echo [4/4] Dependencies removed successfully!
echo.
echo ========================================
echo              IMPORTANT NOTES
echo ========================================
echo The following packages have been removed:
echo - flutter_staggered_grid_view (use GridView instead)
echo - flutter_spinkit (use CircularProgressIndicator)
echo - shimmer (create custom shimmer effect)
echo.
echo Next steps:
echo 1. Update your code to use alternatives
echo 2. Test the app thoroughly
echo 3. Build with scripts\build_ultra_small.bat
echo.
echo If you need to restore: copy pubspec.yaml.backup pubspec.yaml
echo ========================================

pause
