@echo off
echo ========================================
echo    HM Device Errors - Dependencies Analysis
echo    Finding packages to remove for size reduction
echo ========================================

REM Create analysis directory
if not exist "analysis" mkdir "analysis"

echo [1/4] Analyzing current dependencies...
flutter pub deps --json > analysis\deps_tree.json
flutter pub deps > analysis\deps_tree.txt

echo [2/4] Checking for unused dependencies...
flutter pub deps --no-dev > analysis\runtime_deps.txt

echo [3/4] Analyzing package sizes and usage...

REM Create detailed analysis report
echo ======================================== > analysis\dependency_analysis.txt
echo    DEPENDENCY SIZE OPTIMIZATION ANALYSIS >> analysis\dependency_analysis.txt
echo ======================================== >> analysis\dependency_analysis.txt
echo Generated: %date% %time% >> analysis\dependency_analysis.txt
echo. >> analysis\dependency_analysis.txt

echo CURRENT DEPENDENCIES AND ESTIMATED SIZES: >> analysis\dependency_analysis.txt
echo ------------------------------------------ >> analysis\dependency_analysis.txt
echo. >> analysis\dependency_analysis.txt

echo FIREBASE PACKAGES ^(~20MB total^): >> analysis\dependency_analysis.txt
echo - firebase_core: ~3MB ^(REQUIRED^) >> analysis\dependency_analysis.txt
echo - firebase_auth: ~4MB ^(REQUIRED^) >> analysis\dependency_analysis.txt
echo - cloud_firestore: ~6MB ^(REQUIRED^) >> analysis\dependency_analysis.txt
echo - firebase_storage: ~3MB ^(REQUIRED^) >> analysis\dependency_analysis.txt
echo - firebase_messaging: ~2MB ^(REQUIRED^) >> analysis\dependency_analysis.txt
echo - firebase_remote_config: ~2MB ^(REQUIRED^) >> analysis\dependency_analysis.txt
echo. >> analysis\dependency_analysis.txt

echo GOOGLE APIS ^(~10MB total^): >> analysis\dependency_analysis.txt
echo - googleapis: ~8MB ^(REQUIRED for Google Drive^) >> analysis\dependency_analysis.txt
echo - googleapis_auth: ~2MB ^(REQUIRED for Google Drive^) >> analysis\dependency_analysis.txt
echo. >> analysis\dependency_analysis.txt

echo UI/IMAGE PACKAGES ^(~8MB total^): >> analysis\dependency_analysis.txt
echo - flutter_svg: ~2MB ^(REQUIRED for logos^) >> analysis\dependency_analysis.txt
echo - cached_network_image: ~2MB ^(REQUIRED^) >> analysis\dependency_analysis.txt
echo - image_picker: ~3MB ^(REQUIRED^) >> analysis\dependency_analysis.txt
echo - flutter_image_compress: ~1MB ^(REQUIRED^) >> analysis\dependency_analysis.txt
echo. >> analysis\dependency_analysis.txt

echo UTILITY PACKAGES ^(~5MB total^): >> analysis\dependency_analysis.txt
echo - file_picker: ~2MB ^(REQUIRED^) >> analysis\dependency_analysis.txt
echo - url_launcher: ~1MB ^(REQUIRED^) >> analysis\dependency_analysis.txt
echo - share_plus: ~1MB ^(REQUIRED^) >> analysis\dependency_analysis.txt
echo - path_provider: ~1MB ^(REQUIRED^) >> analysis\dependency_analysis.txt
echo. >> analysis\dependency_analysis.txt

echo POTENTIALLY REMOVABLE PACKAGES: >> analysis\dependency_analysis.txt
echo -------------------------------- >> analysis\dependency_analysis.txt
echo - flutter_staggered_grid_view: ~1MB ^(Can use GridView instead^) >> analysis\dependency_analysis.txt
echo - flutter_spinkit: ~0.5MB ^(Can use CircularProgressIndicator^) >> analysis\dependency_analysis.txt
echo - shimmer: ~0.3MB ^(Can create custom shimmer effect^) >> analysis\dependency_analysis.txt
echo - excel: ~2MB ^(Only if Excel export is rarely used^) >> analysis\dependency_analysis.txt
echo - encrypt: ~1MB ^(Only if encryption is not critical^) >> analysis\dependency_analysis.txt
echo. >> analysis\dependency_analysis.txt

echo OPTIMIZATION RECOMMENDATIONS: >> analysis\dependency_analysis.txt
echo ----------------------------- >> analysis\dependency_analysis.txt
echo 1. IMMEDIATE REMOVALS ^(~4MB savings^): >> analysis\dependency_analysis.txt
echo    - Remove flutter_staggered_grid_view >> analysis\dependency_analysis.txt
echo    - Remove flutter_spinkit >> analysis\dependency_analysis.txt
echo    - Remove shimmer >> analysis\dependency_analysis.txt
echo. >> analysis\dependency_analysis.txt
echo 2. CONDITIONAL REMOVALS ^(~3MB savings^): >> analysis\dependency_analysis.txt
echo    - Remove excel if export feature is rarely used >> analysis\dependency_analysis.txt
echo    - Remove encrypt if not critical for security >> analysis\dependency_analysis.txt
echo. >> analysis\dependency_analysis.txt
echo 3. FIREBASE OPTIMIZATION: >> analysis\dependency_analysis.txt
echo    - Use Firebase BoM for consistent versions >> analysis\dependency_analysis.txt
echo    - Remove unused Firebase features in ProGuard >> analysis\dependency_analysis.txt
echo. >> analysis\dependency_analysis.txt

echo [4/4] Generating removal script...

REM Create dependency removal script
echo @echo off > scripts\remove_unused_deps.bat
echo echo Removing unused dependencies to reduce app size... >> scripts\remove_unused_deps.bat
echo. >> scripts\remove_unused_deps.bat
echo REM Remove UI packages that can be replaced >> scripts\remove_unused_deps.bat
echo flutter pub remove flutter_staggered_grid_view >> scripts\remove_unused_deps.bat
echo flutter pub remove flutter_spinkit >> scripts\remove_unused_deps.bat
echo flutter pub remove shimmer >> scripts\remove_unused_deps.bat
echo. >> scripts\remove_unused_deps.bat
echo echo Dependencies removed successfully! >> scripts\remove_unused_deps.bat
echo echo Please update your code to use alternatives: >> scripts\remove_unused_deps.bat
echo echo - Use GridView instead of StaggeredGridView >> scripts\remove_unused_deps.bat
echo echo - Use CircularProgressIndicator instead of SpinKit >> scripts\remove_unused_deps.bat
echo echo - Create custom shimmer effect >> scripts\remove_unused_deps.bat
echo pause >> scripts\remove_unused_deps.bat

echo Analysis completed!
echo.
echo ========================================
echo              SUMMARY
echo ========================================
echo Potential size reduction: ~4-7MB
echo.
echo Files generated:
echo - analysis\dependency_analysis.txt
echo - analysis\deps_tree.json
echo - analysis\deps_tree.txt
echo - scripts\remove_unused_deps.bat
echo.
echo Next steps:
echo 1. Review analysis\dependency_analysis.txt
echo 2. Run scripts\remove_unused_deps.bat to remove safe packages
echo 3. Update code to use alternatives
echo 4. Test the app thoroughly
echo 5. Build with scripts\build_ultra_small.bat
echo ========================================

pause
