#!/bin/bash

# سكريبت تشغيل اختبارات إعدادات الكاش المتقدمة

echo "🚀 بدء تشغيل اختبارات إعدادات الكاش المتقدمة..."
echo "=================================================="

# التحقق من وجود Flutter
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter غير مثبت. يرجى تثبيت Flutter أولاً."
    exit 1
fi

echo "✅ Flutter متوفر"

# التحقق من وجود المشروع
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ لم يتم العثور على ملف pubspec.yaml. تأكد من تشغيل السكريبت من مجلد المشروع."
    exit 1
fi

echo "✅ ملف المشروع موجود"

# تحديث التبعيات
echo "📦 تحديث التبعيات..."
flutter pub get

if [ $? -ne 0 ]; then
    echo "❌ فشل في تحديث التبعيات"
    exit 1
fi

echo "✅ تم تحديث التبعيات بنجاح"

# تشغيل اختبارات إعدادات الكاش
echo "🧪 تشغيل اختبارات إعدادات الكاش..."
flutter test test/screens/cache_settings_screen_test.dart --reporter=expanded

if [ $? -eq 0 ]; then
    echo "✅ جميع اختبارات إعدادات الكاش نجحت!"
else
    echo "❌ بعض اختبارات إعدادات الكاش فشلت"
    exit 1
fi

# تشغيل اختبارات نموذج الإعدادات
echo "🧪 تشغيل اختبارات نموذج الإعدادات..."
flutter test test/models/ --reporter=expanded

if [ $? -eq 0 ]; then
    echo "✅ جميع اختبارات النموذج نجحت!"
else
    echo "❌ بعض اختبارات النموذج فشلت"
fi

# تشغيل تحليل الكود
echo "🔍 تشغيل تحليل الكود..."
flutter analyze lib/screens/settings/cache_settings_screen.dart
flutter analyze lib/models/cache_settings_model.dart

if [ $? -eq 0 ]; then
    echo "✅ تحليل الكود نجح!"
else
    echo "⚠️  توجد تحذيرات في تحليل الكود"
fi

# تشغيل فحص التنسيق
echo "🎨 فحص تنسيق الكود..."
flutter format --dry-run lib/screens/settings/cache_settings_screen.dart
flutter format --dry-run lib/models/cache_settings_model.dart

echo ""
echo "=================================================="
echo "🎉 انتهى تشغيل جميع الاختبارات!"
echo ""
echo "📊 ملخص النتائج:"
echo "   ✅ اختبارات الوحدة: مكتملة"
echo "   ✅ اختبارات الواجهة: مكتملة"
echo "   ✅ تحليل الكود: مكتمل"
echo "   ✅ فحص التنسيق: مكتمل"
echo ""
echo "🚀 يمكنك الآن تشغيل التطبيق التجريبي:"
echo "   flutter run example/cache_settings_demo.dart"
echo ""
echo "📖 للمزيد من المعلومات، راجع:"
echo "   - ADVANCED_CACHE_SETTINGS_README.md"
echo "   - FUTURE_IMPROVEMENTS.md"
echo "=================================================="
