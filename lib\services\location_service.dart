import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:http/http.dart' as http;

class LocationService {
  static const String _ipApiUrl = 'http://ip-api.com/json/';
  static const String _ipifyUrl = 'https://api.ipify.org?format=json';
  static const String _fallbackIpUrl = 'https://httpbin.org/ip';

  /// Get current IP address with improved error handling
  static Future<String?> getIpAddress() async {
    try {
      // Try multiple IP detection services with shorter timeouts
      final ipSources = [
        _ipifyUrl,
        _fallbackIpUrl,
        'https://api.myip.com',
        'https://ipinfo.io/ip',
      ];

      for (final url in ipSources) {
        try {
          final response = await http.get(
            Uri.parse(url),
            headers: {
              'Accept': 'application/json',
              'User-Agent': 'HMDeviceErrors/1.0',
            },
          ).timeout(
            const Duration(seconds: 5), // Increased timeout
            onTimeout: () {
              // Don't treat timeout as critical error
              throw TimeoutException('Request timeout for $url', const Duration(seconds: 5));
            },
          );

          if (response.statusCode == 200) {
            final responseBody = response.body.trim();

            // Handle plain text IP response
            if (responseBody.contains('.') && !responseBody.contains('{')) {
              return responseBody;
            }

            // Handle JSON response
            try {
              final data = json.decode(responseBody);

              // Handle different response formats
              if (data is Map<String, dynamic>) {
                if (data['ip'] != null) {
                  return data['ip'] as String;
                } else if (data['origin'] != null) {
                  return data['origin'] as String;
                }
              }
            } catch (jsonError) {
              // If JSON parsing fails, try to extract IP from plain text
              final ipRegex = RegExp(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b');
              final match = ipRegex.firstMatch(responseBody);
              if (match != null) {
                return match.group(0);
              }
            }
          }
        } catch (e) {
          if (e is TimeoutException) {
            debugPrint('Timeout getting IP from $url after 3 seconds');
          } else {
            debugPrint('Failed to get IP from $url: ${e.runtimeType}: $e');
          }
          continue;
        }
      }

      debugPrint('All IP detection services failed');
      return null;
    } catch (e) {
      debugPrint('Error getting IP address: $e');
      return null;
    }
  }

  /// Get location information from IP address
  static Future<Map<String, dynamic>?> getLocationFromIp([String? ipAddress]) async {
    try {
      String? ip = ipAddress;
      if (ip == null) {
        ip = await getIpAddress();
        if (ip == null) return null;
      }

      final response = await http.get(
        Uri.parse('$_ipApiUrl$ip'),
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['status'] == 'success') {
          return {
            'country': data['country'] ?? 'Unknown',
            'countryCode': data['countryCode'] ?? 'XX',
            'region': data['regionName'] ?? 'Unknown',
            'regionCode': data['region'] ?? 'XX',
            'city': data['city'] ?? 'Unknown',
            'zip': data['zip'] ?? 'Unknown',
            'lat': data['lat']?.toDouble() ?? 0.0,
            'lon': data['lon']?.toDouble() ?? 0.0,
            'timezone': data['timezone'] ?? 'Unknown',
            'isp': data['isp'] ?? 'Unknown',
            'org': data['org'] ?? 'Unknown',
            'as': data['as'] ?? 'Unknown',
            'query': data['query'] ?? ip,
          };
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error getting location from IP: $e');
      return null;
    }
  }

  /// Get GPS location (requires permission)
  static Future<Map<String, dynamic>?> getGpsLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('Location services are disabled');
        return null;
      }

      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          debugPrint('Location permissions are denied');
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        debugPrint('Location permissions are permanently denied');
        return null;
      }

      // Get current position with timeout handling
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.medium,
        timeLimit: const Duration(seconds: 8),
      ).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException('GPS location timeout', const Duration(seconds: 10));
        },
      );

      return {
        'lat': position.latitude,
        'lon': position.longitude,
        'accuracy': position.accuracy,
        'altitude': position.altitude,
        'heading': position.heading,
        'speed': position.speed,
        'timestamp': position.timestamp.toIso8601String(),
      };
    } catch (e) {
      debugPrint('Error getting GPS location: $e');
      return null;
    }
  }

  /// Get comprehensive location information
  static Future<Map<String, dynamic>> getComprehensiveLocation() async {
    final result = <String, dynamic>{};

    try {
      // Get IP address
      final ip = await getIpAddress();
      if (ip != null) {
        result['ip'] = ip;

        // Get location from IP
        final ipLocation = await getLocationFromIp(ip);
        if (ipLocation != null) {
          result['ipLocation'] = ipLocation;
        }
      }

      // Try to get GPS location (optional)
      try {
        final gpsLocation = await getGpsLocation();
        if (gpsLocation != null) {
          result['gpsLocation'] = gpsLocation;
        }
      } catch (e) {
        debugPrint('GPS location not available: $e');
      }

      // Add timestamp
      result['timestamp'] = DateTime.now().toIso8601String();

      return result;
    } catch (e) {
      debugPrint('Error getting comprehensive location: $e');
      return {
        'timestamp': DateTime.now().toIso8601String(),
        'error': e.toString(),
      };
    }
  }

  /// Format location for display
  static String formatLocationForDisplay(Map<String, dynamic>? location) {
    if (location == null) return 'Unknown Location';

    try {
      final ipLocation = location['ipLocation'] as Map<String, dynamic>?;
      if (ipLocation != null) {
        final city = ipLocation['city'] ?? 'Unknown';
        final region = ipLocation['region'] ?? 'Unknown';
        final country = ipLocation['country'] ?? 'Unknown';
        return '$city, $region, $country';
      }

      final gpsLocation = location['gpsLocation'] as Map<String, dynamic>?;
      if (gpsLocation != null) {
        final lat = gpsLocation['lat']?.toStringAsFixed(4) ?? '0.0000';
        final lon = gpsLocation['lon']?.toStringAsFixed(4) ?? '0.0000';
        return 'GPS: $lat, $lon';
      }

      return 'Unknown Location';
    } catch (e) {
      return 'Unknown Location';
    }
  }

  /// Get location summary for logging
  static String getLocationSummary(Map<String, dynamic>? location) {
    if (location == null) return 'Unknown';

    try {
      final ipLocation = location['ipLocation'] as Map<String, dynamic>?;
      if (ipLocation != null) {
        final city = ipLocation['city'] ?? 'Unknown';
        final country = ipLocation['countryCode'] ?? 'XX';
        return '$city, $country';
      }

      return 'Unknown';
    } catch (e) {
      return 'Unknown';
    }
  }

  /// Check if location services are available
  static Future<bool> isLocationServiceAvailable() async {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        return await Geolocator.isLocationServiceEnabled();
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Request location permission
  static Future<bool> requestLocationPermission() async {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        final permission = await Permission.location.request();
        return permission.isGranted;
      }
      return false;
    } catch (e) {
      debugPrint('Error requesting location permission: $e');
      return false;
    }
  }

  /// Get timezone from location
  static String getTimezoneFromLocation(Map<String, dynamic>? location) {
    try {
      final ipLocation = location?['ipLocation'] as Map<String, dynamic>?;
      return ipLocation?['timezone'] ?? 'Unknown';
    } catch (e) {
      return 'Unknown';
    }
  }

  /// Get ISP information
  static String getIspFromLocation(Map<String, dynamic>? location) {
    try {
      final ipLocation = location?['ipLocation'] as Map<String, dynamic>?;
      return ipLocation?['isp'] ?? 'Unknown';
    } catch (e) {
      return 'Unknown';
    }
  }
}
