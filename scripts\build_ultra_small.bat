@echo off
echo ========================================
echo    HM Device Errors - Ultra Small APK Build
echo    Target: Maximum size reduction (51MB -> 30MB)
echo ========================================

REM Clean everything thoroughly
echo Cleaning all build artifacts...
flutter clean
rd /s /q build 2>nul
rd /s /q .dart_tool 2>nul
flutter pub get

REM Pre-build optimizations
echo Performing pre-build optimizations...
flutter pub deps --json > build_deps.json
flutter analyze --no-fatal-infos

REM Build ultra-optimized APK for ARM64 (most common)
echo Building ultra-optimized ARM64 APK...
flutter build apk --release ^
  --shrink ^
  --obfuscate ^
  --split-debug-info=build/debug-info ^
  --target-platform android-arm64 ^
  --tree-shake-icons ^
  --dart-define=FLUTTER_WEB_USE_SKIA=false ^
  --dart-define=DART_OBFUSCATION=true ^
  --dart-define=ENABLE_LOGGING=false ^
  --dart-define=FLUTTER_WEB_AUTO_DETECT=false ^
  --dart-define=FLUTTER_WEB_CANVASKIT_URL=false ^
  --no-pub ^
  --no-track-widget-creation ^
  --no-tree-shake-icons=false

REM Build ultra-optimized APK for ARM32 (older devices)
echo Building ultra-optimized ARM32 APK...
flutter build apk --release ^
  --shrink ^
  --obfuscate ^
  --split-debug-info=build/debug-info ^
  --target-platform android-arm ^
  --tree-shake-icons ^
  --dart-define=FLUTTER_WEB_USE_SKIA=false ^
  --dart-define=DART_OBFUSCATION=true ^
  --dart-define=ENABLE_LOGGING=false ^
  --dart-define=FLUTTER_WEB_AUTO_DETECT=false ^
  --dart-define=FLUTTER_WEB_CANVASKIT_URL=false ^
  --no-pub ^
  --no-track-widget-creation ^
  --no-tree-shake-icons=false

REM Build App Bundle for Play Store (smallest distribution)
echo Building ultra-optimized App Bundle...
flutter build appbundle --release ^
  --shrink ^
  --obfuscate ^
  --split-debug-info=build/debug-info ^
  --tree-shake-icons ^
  --dart-define=DART_OBFUSCATION=true ^
  --dart-define=ENABLE_LOGGING=false ^
  --dart-define=FLUTTER_WEB_USE_SKIA=false ^
  --no-pub ^
  --no-track-widget-creation

REM Analyze final sizes
echo Analyzing final APK sizes...
echo.
echo ARM64 APK Size:
for %%I in (build\app\outputs\flutter-apk\app-arm64-v8a-release.apk) do echo %%~zI bytes (%%~zI / 1048576 MB)
echo.
echo ARM32 APK Size:
for %%I in (build\app\outputs\flutter-apk\app-armeabi-v7a-release.apk) do echo %%~zI bytes (%%~zI / 1048576 MB)
echo.
echo App Bundle Size:
for %%I in (build\app\outputs\bundle\release\app-release.aab) do echo %%~zI bytes (%%~zI / 1048576 MB)

REM Generate detailed size report
echo Generating detailed size analysis...
flutter build apk --analyze-size --target-platform android-arm64 > ultra_size_report.txt 2>&1

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo ARM64 APK: build\app\outputs\flutter-apk\app-arm64-v8a-release.apk
echo ARM32 APK: build\app\outputs\flutter-apk\app-armeabi-v7a-release.apk
echo App Bundle: build\app\outputs\bundle\release\app-release.aab
echo Size Report: ultra_size_report.txt
echo ========================================

pause
