import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/remote_config_provider.dart';
import '../../providers/storage_config_provider.dart';
import '../../providers/locale_provider.dart';

class ConfigSourceManagementScreen extends StatefulWidget {
  const ConfigSourceManagementScreen({super.key});

  @override
  State<ConfigSourceManagementScreen> createState() => _ConfigSourceManagementScreenState();
}

class _ConfigSourceManagementScreenState extends State<ConfigSourceManagementScreen> {
  String _selectedSource = 'remote';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCurrentSettings();
    });
  }

  Future<void> _loadCurrentSettings() async {
    final remoteConfigProvider = Provider.of<RemoteConfigProvider>(context, listen: false);
    if (!remoteConfigProvider.isInitialized) {
      await remoteConfigProvider.initialize();
    }

    setState(() {
      _selectedSource = remoteConfigProvider.getConfigSourcePreference();
    });
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isRTL ? 'إدارة مصدر التكوين' : 'Configuration Source Management',
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshSettings,
            tooltip: isRTL ? 'تحديث الإعدادات' : 'Refresh Settings',
          ),
        ],
      ),
      body: Consumer2<RemoteConfigProvider, StorageConfigProvider>(
        builder: (context, remoteConfigProvider, storageConfigProvider, child) {
          if (remoteConfigProvider.isLoading || storageConfigProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildCurrentStatusCard(remoteConfigProvider, storageConfigProvider, isRTL),
                const SizedBox(height: 16),
                _buildSourceSelectionCard(remoteConfigProvider, isRTL),
                const SizedBox(height: 16),
                _buildConfigurationComparisonCard(remoteConfigProvider, storageConfigProvider, isRTL),
                const SizedBox(height: 16),
                _buildActionButtons(remoteConfigProvider, isRTL),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCurrentStatusCard(RemoteConfigProvider remoteConfigProvider,
      StorageConfigProvider storageConfigProvider, bool isRTL) {
    final status = remoteConfigProvider.getConfigurationStatus();
    final currentSource = status['config_source'] ?? 'unknown';
    final useRemoteAsPrimary = status['use_remote_config_primary'] ?? false;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue[600],
                ),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'الحالة الحالية' : 'Current Status',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatusRow(
              isRTL ? 'المصدر النشط' : 'Active Source',
              currentSource == 'remote_config' ?
                (isRTL ? 'التكوين البعيد' : 'Remote Config') :
                (isRTL ? 'قاعدة البيانات' : 'Database'),
              currentSource == 'remote_config' ? Colors.green : Colors.orange,
              isRTL,
            ),
            _buildStatusRow(
              isRTL ? 'الأولوية للتكوين البعيد' : 'Remote Config Priority',
              useRemoteAsPrimary ?
                (isRTL ? 'مفعل' : 'Enabled') :
                (isRTL ? 'معطل' : 'Disabled'),
              useRemoteAsPrimary ? Colors.green : Colors.grey,
              isRTL,
            ),
            _buildStatusRow(
              isRTL ? 'التكوين البعيد متاح' : 'Remote Config Available',
              remoteConfigProvider.googleDriveConfig != null ?
                (isRTL ? 'متاح' : 'Available') :
                (isRTL ? 'غير متاح' : 'Not Available'),
              remoteConfigProvider.googleDriveConfig != null ? Colors.green : Colors.red,
              isRTL,
            ),
            _buildStatusRow(
              isRTL ? 'تكوين قاعدة البيانات متاح' : 'Database Config Available',
              storageConfigProvider.googleDriveConfig != null ?
                (isRTL ? 'متاح' : 'Available') :
                (isRTL ? 'غير متاح' : 'Not Available'),
              storageConfigProvider.googleDriveConfig != null ? Colors.green : Colors.red,
              isRTL,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value, Color color, bool isRTL) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: color.withValues(alpha: 0.3)),
            ),
            child: Text(
              value,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSourceSelectionCard(RemoteConfigProvider provider, bool isRTL) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.settings,
                  color: Colors.orange[600],
                ),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'اختيار مصدر التكوين' : 'Configuration Source Selection',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              isRTL
                ? 'اختر المصدر الأساسي لتكوين Google Drive:'
                : 'Choose the primary source for Google Drive configuration:',
              style: TextStyle(color: Colors.grey[700]),
            ),
            const SizedBox(height: 16),
            RadioListTile<String>(
              title: Text(isRTL ? 'التكوين البعيد (Firebase Remote Config)' : 'Remote Config (Firebase Remote Config)'),
              subtitle: Text(
                isRTL
                  ? 'يمكن تحديثه بدون إعادة نشر التطبيق'
                  : 'Can be updated without app redeployment',
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
              ),
              value: 'remote',
              groupValue: _selectedSource,
              onChanged: (value) {
                setState(() {
                  _selectedSource = value!;
                });
              },
            ),
            RadioListTile<String>(
              title: Text(isRTL ? 'قاعدة البيانات (Firestore)' : 'Database (Firestore)'),
              subtitle: Text(
                isRTL
                  ? 'يدار من خلال لوحة الإدارة في التطبيق'
                  : 'Managed through app admin panel',
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
              ),
              value: 'database',
              groupValue: _selectedSource,
              onChanged: (value) {
                setState(() {
                  _selectedSource = value!;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigurationComparisonCard(RemoteConfigProvider remoteConfigProvider,
      StorageConfigProvider storageConfigProvider, bool isRTL) {
    final remoteConfig = remoteConfigProvider.googleDriveConfig;
    final databaseConfig = storageConfigProvider.googleDriveConfig;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.compare,
                  color: Colors.purple[600],
                ),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'مقارنة التكوينات' : 'Configuration Comparison',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildConfigSummary(
                    isRTL ? 'التكوين البعيد' : 'Remote Config',
                    remoteConfig,
                    Colors.green,
                    isRTL,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildConfigSummary(
                    isRTL ? 'قاعدة البيانات' : 'Database',
                    databaseConfig,
                    Colors.orange,
                    isRTL,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigSummary(String title, dynamic config, Color color, bool isRTL) {
    final isConfigured = config != null;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: color.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: color.withValues(alpha: 0.05),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isConfigured
              ? (isRTL ? 'مكون' : 'Configured')
              : (isRTL ? 'غير مكون' : 'Not Configured'),
            style: TextStyle(
              color: isConfigured ? Colors.green : Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (isConfigured) ...[
            const SizedBox(height: 4),
            Text(
              '${isRTL ? 'آخر تحديث: ' : 'Last updated: '}${config.updatedAt.toString().split(' ')[0]}',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 10,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButtons(RemoteConfigProvider provider, bool isRTL) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.play_arrow,
                  color: Colors.blue[600],
                ),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'الإجراءات' : 'Actions',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _applyChanges,
                    icon: const Icon(Icons.save),
                    label: Text(isRTL ? 'تطبيق التغييرات' : 'Apply Changes'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _refreshSettings,
                    icon: const Icon(Icons.refresh),
                    label: Text(isRTL ? 'تحديث' : 'Refresh'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              isRTL
                ? 'ملاحظة: تغيير مصدر التكوين يتطلب تحديث إعدادات Firebase Remote Config'
                : 'Note: Changing configuration source requires updating Firebase Remote Config settings',
              style: TextStyle(
                color: Colors.orange[600],
                fontSize: 12,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _applyChanges() async {
    final remoteConfigProvider = Provider.of<RemoteConfigProvider>(context, listen: false);
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    try {
      await remoteConfigProvider.setConfigSourcePreference(_selectedSource);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL
                ? 'تم حفظ تفضيلات مصدر التكوين. يرجى تحديث Firebase Remote Config.'
                : 'Configuration source preference saved. Please update Firebase Remote Config.',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL
                ? 'خطأ في حفظ الإعدادات: $e'
                : 'Error saving settings: $e',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _refreshSettings() async {
    final remoteConfigProvider = Provider.of<RemoteConfigProvider>(context, listen: false);
    final storageConfigProvider = Provider.of<StorageConfigProvider>(context, listen: false);

    await Future.wait([
      remoteConfigProvider.fetchAndActivate(),
      storageConfigProvider.initialize(),
    ]);

    await _loadCurrentSettings();
  }
}
